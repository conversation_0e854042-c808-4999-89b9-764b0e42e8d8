# 🚀 GoReal Business Management Platform

[![Go Version](https://img.shields.io/badge/Go-1.22+-00ADD8?style=for-the-badge&logo=go)](https://golang.org/)
[![Next.js](https://img.shields.io/badge/Next.js-15-black?style=for-the-badge&logo=next.js)](https://nextjs.org/)
[![TypeScript](https://img.shields.io/badge/TypeScript-5.0+-3178C6?style=for-the-badge&logo=typescript)](https://www.typescriptlang.org/)
[![PostgreSQL](https://img.shields.io/badge/PostgreSQL-15+-336791?style=for-the-badge&logo=postgresql)](https://postgresql.org/)
[![Redis](https://img.shields.io/badge/Redis-7+-DC382D?style=for-the-badge&logo=redis)](https://redis.io/)
[![License](https://img.shields.io/badge/License-MIT-green?style=for-the-badge)](LICENSE)

A **modern, enterprise-grade business management platform** built with Go and Next.js, featuring comprehensive CRM, task management, analytics, and user management capabilities with **enterprise-level security** and **observability**.

## ✨ Key Features

### 🔐 **Enterprise Security**
- **JWT Authentication** with token blacklisting and Redis-based session management
- **Role-Based Access Control (RBAC)** with granular permissions
- **Comprehensive Input Validation** with XSS and injection protection
- **Advanced Rate Limiting** with Redis-based distributed limiting
- **Security Headers** and Content Security Policy (CSP)
- **Audit Logging** with structured security event tracking

### 👥 **User Management**
- Complete user authentication and profile management
- Multi-role support (Admin, Manager, Employee, User, Client)
- Avatar upload and profile customization
- User statistics and activity tracking
- Following/follower system

### 📋 **Task Management**
- Create, assign, and track tasks with priorities and deadlines
- Bulk operations and task filtering
- Real-time task updates and notifications
- Task completion tracking and analytics
- Tag-based organization

### 🎯 **Lead Management (CRM)**
- Complete CRM functionality for managing leads and sales pipeline
- Lead status tracking and conversion analytics
- Assignment and territory management
- Sales performance metrics
- Lead source tracking and ROI analysis

### 📊 **Analytics & Reporting**
- Business intelligence and comprehensive reporting
- Dashboard with key performance indicators
- Sales analytics and revenue tracking
- User performance metrics
- Custom report generation

### 🔔 **Real-time Features**
- Instant notifications and alerts
- Real-time updates across the platform
- Email and push notification support
- Notification preferences and management

## 🏗️ **Architecture & Tech Stack**

### **Backend (Go 1.22+)**
- **Standard Library HTTP Server**: Modern Go 1.22+ with new ServeMux features
- **Clean Architecture**: Domain-driven design with clear separation of concerns
- **PostgreSQL**: Robust relational database with connection pooling
- **Redis**: Caching, rate limiting, and session management
- **OpenTelemetry**: Comprehensive observability with distributed tracing
- **Enterprise Security**: Multi-layer security with audit logging

### **Frontend (Next.js 15)**
- **App Router**: Modern Next.js with server components and streaming
- **TypeScript**: Strict type safety with comprehensive type definitions
- **Tailwind CSS**: Utility-first styling with custom design system
- **Radix UI**: Accessible component primitives with custom styling
- **React Hook Form**: Type-safe form handling with validation
- **Advanced State Management**: Optimized data fetching and caching

### **Infrastructure & DevOps**
- **Docker**: Multi-stage builds with production optimization
- **nginx**: Reverse proxy with SSL termination and caching
- **Prometheus & Grafana**: Monitoring and alerting
- **Jaeger**: Distributed tracing and performance monitoring

## 🚀 **Quick Start**

### Prerequisites
- **Go 1.22+**
- **Node.js 18+**
- **PostgreSQL 15+**
- **Redis 7+**
- **Docker & Docker Compose** (optional)

### Development Setup

1. **Clone the repository**:
```bash
git clone https://github.com/your-org/goreal-business-platform.git
cd goreal-business-platform
```

2. **Start with Docker Compose** (Recommended):
```bash
# Start all services
docker-compose up -d

# Access the application
# Frontend: http://localhost:3000
# Backend API: http://localhost:8080
# API Documentation: http://localhost:8080/docs
```

3. **Manual Setup**:

**Backend**:
```bash
cd backend

# Install dependencies
go mod download

# Set up environment
cp .env.example .env
# Edit .env with your configuration

# Start database and Redis
docker-compose up -d postgres redis

# Run migrations
go run cmd/migrate/main.go

# Start the server
go run cmd/api/main.go
```

**Frontend**:
```bash
cd client

# Install dependencies
npm install

# Set up environment
cp .env.example .env.local
# Edit .env.local with your configuration

# Start development server
npm run dev
```

## 📁 **Project Structure**

```
goreal-business-platform/
├── backend/                    # Go backend application
│   ├── cmd/api/               # Application entrypoint
│   ├── internal/              # Private application code
│   │   ├── config/            # Configuration management
│   │   ├── container/         # Dependency injection
│   │   ├── domain/            # Domain models and interfaces
│   │   ├── handlers/          # HTTP handlers (Presentation)
│   │   ├── middleware/        # HTTP middleware
│   │   ├── repositories/      # Data access (Infrastructure)
│   │   └── services/          # Business logic (Application)
│   ├── pkg/                   # Public packages
│   │   ├── audit/             # Audit logging system
│   │   ├── jwt/               # JWT utilities with blacklisting
│   │   ├── observability/     # OpenTelemetry setup
│   │   ├── redis/             # Redis client wrapper
│   │   └── validation/        # Input validation
│   └── test/                  # Integration tests
├── client/                    # Next.js frontend application
│   ├── app/                   # App Router pages
│   ├── components/            # React components
│   │   ├── ui/                # Base UI components
│   │   ├── forms/             # Form components
│   │   └── layout/            # Layout components
│   ├── hooks/                 # Custom React hooks
│   ├── lib/                   # Utility libraries
│   ├── types/                 # TypeScript definitions
│   └── styles/                # Global styles
├── docs/                      # Documentation
│   ├── ARCHITECTURE.md        # Architecture documentation
│   ├── CONTRIBUTING.md        # Contributing guidelines
│   ├── PRODUCTION_DEPLOYMENT.md # Production deployment guide
│   └── api/                   # API documentation
└── docker-compose.yml         # Development environment
```

## 🔧 **Configuration**

### Backend Environment Variables
```bash
# Server Configuration
PORT=8080
ENVIRONMENT=development
SERVICE_NAME=goreal-backend

# Database
DB_HOST=localhost
DB_PORT=5432
DB_NAME=goreal
DB_USER=goreal_user
DB_PASSWORD=secure_password

# Redis
REDIS_HOST=localhost
REDIS_PORT=6379
REDIS_PASSWORD=redis_password

# JWT
JWT_ACCESS_SECRET=your-super-secure-access-secret
JWT_REFRESH_SECRET=your-super-secure-refresh-secret
JWT_ACCESS_DURATION=15m
JWT_REFRESH_DURATION=7d

# Security
RATE_LIMIT_REQUESTS_PER_WINDOW=100
RATE_LIMIT_WINDOW_SECONDS=60
ENABLE_AUDIT_LOG=true
```

### Frontend Environment Variables
```bash
# API Configuration
NEXT_PUBLIC_API_URL=http://localhost:8080
NEXT_PUBLIC_WS_URL=ws://localhost:8080

# Application
NEXT_PUBLIC_APP_NAME=GoReal Business Platform
NEXT_PUBLIC_ENVIRONMENT=development
```

## 🧪 **Testing**

### Backend Testing
```bash
cd backend

# Run all tests
go test ./...

# Run tests with coverage
go test -cover ./...

# Run integration tests
go test ./test -v

# Run benchmarks
go test -bench=. ./...
```

### Frontend Testing
```bash
cd client

# Run unit tests
npm test

# Run tests in watch mode
npm run test:watch

# Run E2E tests
npm run test:e2e
```

## 📊 **API Documentation**

- **Interactive API Docs**: http://localhost:8080/docs
- **OpenAPI Specification**: [docs/api/openapi.yaml](docs/api/openapi.yaml)
- **Postman Collection**: Available in the `docs/api/` directory

### Key API Endpoints

```
Authentication:
POST   /api/auth/login          # User login
POST   /api/auth/register       # User registration
POST   /api/auth/refresh        # Token refresh
POST   /api/auth/logout         # User logout

Users:
GET    /api/users/profile       # Get current user profile
PUT    /api/users/profile       # Update user profile
GET    /api/users/{id}          # Get user by ID

Tasks:
GET    /api/tasks               # List tasks (with filtering)
POST   /api/tasks               # Create task
GET    /api/tasks/{id}          # Get task by ID
PUT    /api/tasks/{id}          # Update task
DELETE /api/tasks/{id}          # Delete task

Leads:
GET    /api/leads               # List leads
POST   /api/leads               # Create lead
PUT    /api/leads/{id}          # Update lead
DELETE /api/leads/{id}          # Delete lead

Analytics:
GET    /api/analytics/dashboard # Dashboard statistics
GET    /api/analytics/sales     # Sales analytics
GET    /api/analytics/users     # User performance
```

## 🚀 **Deployment**

### Development
```bash
docker-compose up -d
```

### Production
See [docs/PRODUCTION_DEPLOYMENT.md](docs/PRODUCTION_DEPLOYMENT.md) for comprehensive production deployment guide including:
- Docker production setup
- nginx configuration
- SSL/TLS setup
- Monitoring and observability
- Security hardening
- Performance optimization

## 🔍 **Monitoring & Observability**

### Health Checks
- **Backend**: http://localhost:8080/health
- **Frontend**: http://localhost:3000/api/health

### Monitoring Stack
- **Metrics**: Prometheus + Grafana
- **Tracing**: Jaeger with OpenTelemetry
- **Logging**: Structured JSON logs with correlation IDs
- **Alerts**: Configurable alerts for critical metrics

### Key Metrics Tracked
- Request rates and response times
- Error rates and types
- Database performance
- Authentication success/failure rates
- Security events and violations
- Business metrics (tasks, leads, conversions)

## 🤝 **Contributing**

We welcome contributions! Please see [CONTRIBUTING.md](docs/CONTRIBUTING.md) for detailed guidelines including:
- Code style and standards
- Testing requirements
- Pull request process
- Development setup

### Development Commands
```bash
# Backend
go fmt ./...                    # Format code
go vet ./...                    # Vet code
golangci-lint run              # Lint code
go test ./...                  # Run tests

# Frontend
npm run lint                   # Lint code
npm run format                 # Format code
npm run type-check             # Type checking
npm test                       # Run tests
```

## 📚 **Documentation**

- **[Architecture Guide](docs/ARCHITECTURE.md)**: Comprehensive architecture documentation
- **[Contributing Guide](docs/CONTRIBUTING.md)**: Development and contribution guidelines
- **[Production Deployment](docs/PRODUCTION_DEPLOYMENT.md)**: Production deployment guide
- **[API Documentation](docs/api/openapi.yaml)**: OpenAPI specification

## 📄 **License**

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## 🆘 **Support**

- **Documentation**: Check the `/docs` directory
- **Issues**: [GitHub Issues](https://github.com/your-org/goreal-business-platform/issues)
- **Discussions**: [GitHub Discussions](https://github.com/your-org/goreal-business-platform/discussions)
- **Email**: <EMAIL>

## 🎯 **Roadmap**

- [ ] **Mobile App**: React Native mobile application
- [ ] **Advanced Analytics**: Machine learning insights
- [ ] **Integration Hub**: Third-party service integrations
- [ ] **Workflow Automation**: Advanced business process automation
- [ ] **Multi-tenant Support**: SaaS-ready multi-tenancy
- [ ] **Advanced Reporting**: Custom report builder

---

**Built with ❤️ by the GoReal Team**
