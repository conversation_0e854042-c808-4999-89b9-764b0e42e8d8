openapi: 3.0.3
info:
  title: GoReal Platform API
  description: |
    Comprehensive business management platform API providing CRM, task management, 
    analytics, and user management capabilities with enterprise-grade security.
    
    ## Authentication
    
    This API uses JWT Bearer tokens for authentication. Include the token in the 
    Authorization header:
    
    ```
    Authorization: Bearer <your-jwt-token>
    ```
    
    ## Rate Limiting
    
    API requests are rate limited to prevent abuse:
    - **Standard endpoints**: 100 requests per minute
    - **Authentication endpoints**: 10 requests per minute
    - **Bulk operations**: 20 requests per minute
    
    Rate limit headers are included in responses:
    - `X-RateLimit-Limit`: Request limit per window
    - `X-RateLimit-Remaining`: Remaining requests in current window
    - `X-RateLimit-Reset`: Time when the rate limit resets
    
    ## Error Handling
    
    The API uses conventional HTTP response codes and returns error details in JSON format:
    
    ```json
    {
      "error": "Validation failed",
      "code": "VALIDATION_ERROR",
      "details": {
        "field": "email",
        "message": "Invalid email format"
      },
      "timestamp": "2024-01-15T10:30:00Z"
    }
    ```
    
  version: 1.0.0
  contact:
    name: GoReal API Support
    email: <EMAIL>
    url: https://docs.goreal.com
  license:
    name: MIT
    url: https://opensource.org/licenses/MIT

servers:
  - url: https://api.goreal.com/v1
    description: Production server
  - url: https://staging-api.goreal.com/v1
    description: Staging server
  - url: http://localhost:8080/api/v1
    description: Development server

security:
  - BearerAuth: []

paths:
  # Health Check
  /health:
    get:
      tags:
        - System
      summary: Health check endpoint
      description: Returns the health status of the API
      security: []
      responses:
        '200':
          description: Service is healthy
          content:
            application/json:
              schema:
                type: object
                properties:
                  status:
                    type: string
                    example: healthy
                  service:
                    type: string
                    example: goreal-backend
                  timestamp:
                    type: string
                    format: date-time
                  version:
                    type: string
                    example: 1.0.0

  # Authentication Endpoints
  /auth/login:
    post:
      tags:
        - Authentication
      summary: User login
      description: Authenticate user with email and password
      security: []
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              required:
                - email
                - password
              properties:
                email:
                  type: string
                  format: email
                  example: <EMAIL>
                password:
                  type: string
                  format: password
                  example: SecurePassword123!
                rememberMe:
                  type: boolean
                  default: false
      responses:
        '200':
          description: Login successful
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/AuthResponse'
        '400':
          $ref: '#/components/responses/BadRequest'
        '401':
          $ref: '#/components/responses/Unauthorized'
        '429':
          $ref: '#/components/responses/TooManyRequests'

  /auth/register:
    post:
      tags:
        - Authentication
      summary: User registration
      description: Register a new user account
      security: []
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              required:
                - email
                - password
                - fullName
                - username
              properties:
                email:
                  type: string
                  format: email
                  example: <EMAIL>
                password:
                  type: string
                  format: password
                  minLength: 8
                  example: SecurePassword123!
                fullName:
                  type: string
                  minLength: 2
                  maxLength: 100
                  example: John Doe
                username:
                  type: string
                  pattern: '^[a-zA-Z0-9_-]{3,30}$'
                  example: johndoe
                acceptTerms:
                  type: boolean
                  example: true
      responses:
        '201':
          description: Registration successful
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/AuthResponse'
        '400':
          $ref: '#/components/responses/BadRequest'
        '409':
          $ref: '#/components/responses/Conflict'
        '429':
          $ref: '#/components/responses/TooManyRequests'

  /auth/refresh:
    post:
      tags:
        - Authentication
      summary: Refresh access token
      description: Get a new access token using a valid refresh token
      security: []
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              required:
                - refreshToken
              properties:
                refreshToken:
                  type: string
                  example: eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...
      responses:
        '200':
          description: Token refreshed successfully
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/AuthResponse'
        '400':
          $ref: '#/components/responses/BadRequest'
        '401':
          $ref: '#/components/responses/Unauthorized'

  /auth/logout:
    post:
      tags:
        - Authentication
      summary: User logout
      description: Logout user and invalidate tokens
      responses:
        '200':
          description: Logout successful
          content:
            application/json:
              schema:
                type: object
                properties:
                  message:
                    type: string
                    example: Logout successful
        '401':
          $ref: '#/components/responses/Unauthorized'

  # User Management
  /users/profile:
    get:
      tags:
        - Users
      summary: Get current user profile
      description: Retrieve the authenticated user's profile information
      responses:
        '200':
          description: User profile retrieved successfully
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/User'
        '401':
          $ref: '#/components/responses/Unauthorized'

    put:
      tags:
        - Users
      summary: Update user profile
      description: Update the authenticated user's profile information
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              properties:
                fullName:
                  type: string
                  minLength: 2
                  maxLength: 100
                  example: John Smith
                username:
                  type: string
                  pattern: '^[a-zA-Z0-9_-]{3,30}$'
                  example: johnsmith
                bio:
                  type: string
                  maxLength: 500
                  example: Software developer passionate about clean code
                avatarUrl:
                  type: string
                  format: uri
                  example: https://example.com/avatar.jpg
      responses:
        '200':
          description: Profile updated successfully
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/User'
        '400':
          $ref: '#/components/responses/BadRequest'
        '401':
          $ref: '#/components/responses/Unauthorized'

  # Task Management
  /tasks:
    get:
      tags:
        - Tasks
      summary: List tasks
      description: Retrieve a paginated list of tasks with optional filtering
      parameters:
        - name: page
          in: query
          description: Page number (1-based)
          schema:
            type: integer
            minimum: 1
            default: 1
        - name: limit
          in: query
          description: Number of items per page
          schema:
            type: integer
            minimum: 1
            maximum: 100
            default: 20
        - name: status
          in: query
          description: Filter by task status
          schema:
            $ref: '#/components/schemas/TaskStatus'
        - name: priority
          in: query
          description: Filter by task priority
          schema:
            $ref: '#/components/schemas/TaskPriority'
        - name: assigneeId
          in: query
          description: Filter by assignee ID
          schema:
            type: string
            format: uuid
        - name: search
          in: query
          description: Search in task title and description
          schema:
            type: string
            maxLength: 100
      responses:
        '200':
          description: Tasks retrieved successfully
          content:
            application/json:
              schema:
                type: object
                properties:
                  data:
                    type: array
                    items:
                      $ref: '#/components/schemas/Task'
                  pagination:
                    $ref: '#/components/schemas/Pagination'
        '400':
          $ref: '#/components/responses/BadRequest'
        '401':
          $ref: '#/components/responses/Unauthorized'

    post:
      tags:
        - Tasks
      summary: Create task
      description: Create a new task
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              required:
                - title
              properties:
                title:
                  type: string
                  minLength: 1
                  maxLength: 200
                  example: Implement user authentication
                description:
                  type: string
                  maxLength: 2000
                  example: Add JWT-based authentication with login and registration
                priority:
                  $ref: '#/components/schemas/TaskPriority'
                assigneeId:
                  type: string
                  format: uuid
                  example: 123e4567-e89b-12d3-a456-************
                dueDate:
                  type: string
                  format: date-time
                  example: 2024-02-15T10:00:00Z
                tags:
                  type: array
                  items:
                    type: string
                  example: ["backend", "security", "authentication"]
      responses:
        '201':
          description: Task created successfully
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Task'
        '400':
          $ref: '#/components/responses/BadRequest'
        '401':
          $ref: '#/components/responses/Unauthorized'

components:
  securitySchemes:
    BearerAuth:
      type: http
      scheme: bearer
      bearerFormat: JWT

  schemas:
    User:
      type: object
      properties:
        id:
          type: string
          format: uuid
          example: 123e4567-e89b-12d3-a456-************
        email:
          type: string
          format: email
          example: <EMAIL>
        username:
          type: string
          example: johndoe
        fullName:
          type: string
          example: John Doe
        role:
          type: string
          enum: [admin, manager, employee, user, client]
          example: user
        avatarUrl:
          type: string
          format: uri
          nullable: true
          example: https://example.com/avatar.jpg
        bio:
          type: string
          nullable: true
          example: Software developer passionate about clean code
        isActive:
          type: boolean
          example: true
        emailVerified:
          type: boolean
          example: true
        createdAt:
          type: string
          format: date-time
          example: 2024-01-15T10:30:00Z
        updatedAt:
          type: string
          format: date-time
          example: 2024-01-15T10:30:00Z
        lastLoginAt:
          type: string
          format: date-time
          nullable: true
          example: 2024-01-15T10:30:00Z

    AuthResponse:
      type: object
      properties:
        user:
          $ref: '#/components/schemas/User'
        accessToken:
          type: string
          example: eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...
        refreshToken:
          type: string
          example: eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...
        expiresIn:
          type: integer
          description: Access token expiration time in seconds
          example: 3600

    Task:
      type: object
      properties:
        id:
          type: string
          format: uuid
          example: 123e4567-e89b-12d3-a456-************
        title:
          type: string
          example: Implement user authentication
        description:
          type: string
          nullable: true
          example: Add JWT-based authentication with login and registration
        status:
          $ref: '#/components/schemas/TaskStatus'
        priority:
          $ref: '#/components/schemas/TaskPriority'
        assigneeId:
          type: string
          format: uuid
          nullable: true
          example: 123e4567-e89b-12d3-a456-************
        assignee:
          $ref: '#/components/schemas/User'
          nullable: true
        createdBy:
          type: string
          format: uuid
          example: 123e4567-e89b-12d3-a456-************
        creator:
          $ref: '#/components/schemas/User'
          nullable: true
        dueDate:
          type: string
          format: date-time
          nullable: true
          example: 2024-02-15T10:00:00Z
        completedAt:
          type: string
          format: date-time
          nullable: true
          example: 2024-02-10T15:30:00Z
        createdAt:
          type: string
          format: date-time
          example: 2024-01-15T10:30:00Z
        updatedAt:
          type: string
          format: date-time
          example: 2024-01-15T10:30:00Z
        tags:
          type: array
          items:
            type: string
          example: ["backend", "security", "authentication"]
        attachments:
          type: array
          items:
            type: string
          example: ["https://example.com/file1.pdf"]

    TaskStatus:
      type: string
      enum: [pending, in_progress, completed, cancelled]
      example: pending

    TaskPriority:
      type: string
      enum: [low, medium, high, urgent]
      example: medium

    Lead:
      type: object
      properties:
        id:
          type: string
          format: uuid
          example: 123e4567-e89b-12d3-a456-************
        name:
          type: string
          example: John Smith
        email:
          type: string
          format: email
          example: <EMAIL>
        phone:
          type: string
          nullable: true
          example: ******-123-4567
        company:
          type: string
          nullable: true
          example: Acme Corporation
        status:
          $ref: '#/components/schemas/LeadStatus'
        source:
          $ref: '#/components/schemas/LeadSource'
        assigneeId:
          type: string
          format: uuid
          nullable: true
          example: 123e4567-e89b-12d3-a456-************
        assignee:
          $ref: '#/components/schemas/User'
          nullable: true
        estimatedValue:
          type: number
          nullable: true
          example: 50000
        notes:
          type: string
          nullable: true
          example: Interested in enterprise solution
        createdAt:
          type: string
          format: date-time
          example: 2024-01-15T10:30:00Z
        updatedAt:
          type: string
          format: date-time
          example: 2024-01-15T10:30:00Z

    LeadStatus:
      type: string
      enum: [new, contacted, qualified, proposal, negotiation, closed_won, closed_lost]
      example: new

    LeadSource:
      type: string
      enum: [website, referral, social_media, email_campaign, cold_call, trade_show, other]
      example: website

    Notification:
      type: object
      properties:
        id:
          type: string
          format: uuid
          example: 123e4567-e89b-12d3-a456-************
        title:
          type: string
          example: New task assigned
        message:
          type: string
          example: You have been assigned a new task "Implement authentication"
        type:
          type: string
          enum: [info, success, warning, error]
          example: info
        isRead:
          type: boolean
          example: false
        userId:
          type: string
          format: uuid
          example: 123e4567-e89b-12d3-a456-************
        createdAt:
          type: string
          format: date-time
          example: 2024-01-15T10:30:00Z
        readAt:
          type: string
          format: date-time
          nullable: true
          example: 2024-01-15T11:00:00Z

    Pagination:
      type: object
      properties:
        page:
          type: integer
          example: 1
        limit:
          type: integer
          example: 20
        total:
          type: integer
          example: 150
        totalPages:
          type: integer
          example: 8
        hasNext:
          type: boolean
          example: true
        hasPrev:
          type: boolean
          example: false

    Error:
      type: object
      properties:
        error:
          type: string
          example: Validation failed
        code:
          type: string
          example: VALIDATION_ERROR
        details:
          type: object
          additionalProperties: true
          example:
            field: email
            message: Invalid email format
        timestamp:
          type: string
          format: date-time
          example: 2024-01-15T10:30:00Z

  responses:
    BadRequest:
      description: Bad request - Invalid input data
      content:
        application/json:
          schema:
            $ref: '#/components/schemas/Error'
          example:
            error: Validation failed
            code: VALIDATION_ERROR
            details:
              field: email
              message: Invalid email format
            timestamp: 2024-01-15T10:30:00Z

    Unauthorized:
      description: Unauthorized - Invalid or missing authentication
      content:
        application/json:
          schema:
            $ref: '#/components/schemas/Error'
          example:
            error: Invalid or expired token
            code: UNAUTHORIZED
            timestamp: 2024-01-15T10:30:00Z

    Forbidden:
      description: Forbidden - Insufficient permissions
      content:
        application/json:
          schema:
            $ref: '#/components/schemas/Error'
          example:
            error: Insufficient permissions
            code: FORBIDDEN
            timestamp: 2024-01-15T10:30:00Z

    NotFound:
      description: Not found - Resource does not exist
      content:
        application/json:
          schema:
            $ref: '#/components/schemas/Error'
          example:
            error: Resource not found
            code: NOT_FOUND
            timestamp: 2024-01-15T10:30:00Z

    Conflict:
      description: Conflict - Resource already exists
      content:
        application/json:
          schema:
            $ref: '#/components/schemas/Error'
          example:
            error: Email already exists
            code: CONFLICT
            details:
              field: email
              message: User with this email already exists
            timestamp: 2024-01-15T10:30:00Z

    TooManyRequests:
      description: Too many requests - Rate limit exceeded
      headers:
        X-RateLimit-Limit:
          description: Request limit per window
          schema:
            type: integer
            example: 100
        X-RateLimit-Remaining:
          description: Remaining requests in current window
          schema:
            type: integer
            example: 0
        X-RateLimit-Reset:
          description: Time when the rate limit resets (Unix timestamp)
          schema:
            type: integer
            example: 1705320600
        Retry-After:
          description: Seconds to wait before retrying
          schema:
            type: integer
            example: 60
      content:
        application/json:
          schema:
            $ref: '#/components/schemas/Error'
          example:
            error: Rate limit exceeded
            code: RATE_LIMIT_EXCEEDED
            timestamp: 2024-01-15T10:30:00Z

    InternalServerError:
      description: Internal server error
      content:
        application/json:
          schema:
            $ref: '#/components/schemas/Error'
          example:
            error: Internal server error
            code: INTERNAL_ERROR
            timestamp: 2024-01-15T10:30:00Z

tags:
  - name: System
    description: System health and status endpoints
  - name: Authentication
    description: User authentication and authorization
  - name: Users
    description: User management and profiles
  - name: Tasks
    description: Task management and tracking
  - name: Leads
    description: Lead management and CRM
  - name: Analytics
    description: Business analytics and reporting
  - name: Notifications
    description: Notification management
