# GoReal Platform - Production Deployment Guide

This comprehensive guide covers deploying the GoReal platform with enterprise-grade security, observability, and performance optimizations.

## Table of Contents

- [Architecture Overview](#architecture-overview)
- [Prerequisites](#prerequisites)
- [Security Configuration](#security-configuration)
- [Production Environment Setup](#production-environment-setup)
- [Docker Production Deployment](#docker-production-deployment)
- [Monitoring and Observability](#monitoring-and-observability)
- [Performance Optimization](#performance-optimization)
- [Backup and Recovery](#backup-and-recovery)
- [Troubleshooting](#troubleshooting)

## Architecture Overview

### Production Architecture

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   Load Balancer │    │   Web Servers   │    │   Database      │
│   (nginx/HAProxy│◄──►│   (Multiple     │◄──►│   (PostgreSQL   │
│   SSL/TLS)      │    │    Instances)   │    │    Primary +    │
└─────────────────┘    └─────────────────┘    │    Replicas)    │
         │                       │             └─────────────────┘
         │              ┌─────────────────┐              │
         │              │     Redis       │              │
         └──────────────┤   (Cluster)     │──────────────┘
                        └─────────────────┘
                                 │
                        ┌─────────────────┐
                        │   Monitoring    │
                        │   (Prometheus,  │
                        │    Grafana,     │
                        │    Jaeger)      │
                        └─────────────────┘
```

### Technology Stack
- **Backend**: Go 1.22+ with standard library HTTP server
- **Frontend**: Next.js 15 with App Router and TypeScript
- **Database**: PostgreSQL 15+ with connection pooling
- **Cache**: Redis 7+ for rate limiting and session management
- **Reverse Proxy**: nginx with SSL termination
- **Monitoring**: OpenTelemetry, Prometheus, Grafana, Jaeger
- **Security**: Comprehensive middleware stack with audit logging

## Prerequisites

### System Requirements

#### Production Server Specifications
- **CPU**: 4+ cores (8+ recommended)
- **RAM**: 8GB minimum (16GB+ recommended)
- **Storage**: 100GB+ SSD with backup storage
- **Network**: 1 Gbps connection with DDoS protection
- **OS**: Ubuntu 22.04 LTS or similar

#### Required Software
- **Docker**: 24.0+ with Docker Compose
- **nginx**: Latest stable version
- **SSL Certificate**: Let's Encrypt or commercial certificate
- **Monitoring Tools**: Prometheus, Grafana, Jaeger

### Domain and DNS Setup
- Primary domain (e.g., `goreal.com`)
- API subdomain (e.g., `api.goreal.com`)
- Monitoring subdomain (e.g., `monitoring.goreal.com`)
- CDN setup for static assets (optional but recommended)

## Security Configuration

### SSL/TLS Setup

1. **Install Certbot for Let's Encrypt**:
```bash
sudo apt update
sudo apt install certbot python3-certbot-nginx
```

2. **Obtain SSL certificates**:
```bash
sudo certbot --nginx -d goreal.com -d www.goreal.com -d api.goreal.com
```

3. **Configure automatic renewal**:
```bash
sudo crontab -e
# Add: 0 12 * * * /usr/bin/certbot renew --quiet
```

### Firewall Configuration

```bash
# Configure UFW firewall
sudo ufw default deny incoming
sudo ufw default allow outgoing
sudo ufw allow ssh
sudo ufw allow 80/tcp
sudo ufw allow 443/tcp
sudo ufw allow from 10.0.0.0/8 to any port 5432  # PostgreSQL (internal only)
sudo ufw allow from 10.0.0.0/8 to any port 6379  # Redis (internal only)
sudo ufw enable
```

### Security Headers Configuration

Create `/etc/nginx/conf.d/security-headers.conf`:
```nginx
# Security headers
add_header Strict-Transport-Security "max-age=31536000; includeSubDomains; preload" always;
add_header X-Content-Type-Options "nosniff" always;
add_header X-Frame-Options "DENY" always;
add_header X-XSS-Protection "1; mode=block" always;
add_header Referrer-Policy "strict-origin-when-cross-origin" always;
add_header Content-Security-Policy "default-src 'self'; script-src 'self' 'unsafe-inline' 'unsafe-eval'; style-src 'self' 'unsafe-inline'; img-src 'self' data: https:; font-src 'self'; connect-src 'self' wss:; frame-ancestors 'none';" always;
add_header Permissions-Policy "camera=(), microphone=(), geolocation=(), payment=()" always;

# Remove server tokens
server_tokens off;
```

## Production Environment Setup

### Environment Variables

#### Backend Production Environment (`.env.production`)
```bash
# Server Configuration
PORT=8080
HOST=0.0.0.0
ENVIRONMENT=production
SERVICE_NAME=goreal-backend

# Database Configuration
DB_HOST=postgres-primary.internal
DB_PORT=5432
DB_NAME=goreal_prod
DB_USER=goreal_user
DB_PASSWORD=${DB_PASSWORD}  # Use secrets management
DB_SSL_MODE=require
DB_MAX_OPEN_CONNS=25
DB_MAX_IDLE_CONNS=5
DB_CONN_MAX_LIFETIME=5m

# Redis Configuration
REDIS_HOST=redis-cluster.internal
REDIS_PORT=6379
REDIS_PASSWORD=${REDIS_PASSWORD}  # Use secrets management
REDIS_DB=0
REDIS_POOL_SIZE=20

# JWT Configuration (Use strong secrets)
JWT_ACCESS_SECRET=${JWT_ACCESS_SECRET}
JWT_REFRESH_SECRET=${JWT_REFRESH_SECRET}
JWT_ACCESS_DURATION=15m
JWT_REFRESH_DURATION=7d

# Security Configuration
CSP_ENABLED=true
CSP_DEFAULT_SRC='self'
CSP_SCRIPT_SRC='self'
CSP_STYLE_SRC='self' 'unsafe-inline'
CSP_IMG_SRC='self' data: https:
CSP_CONNECT_SRC='self' wss:

# Rate Limiting (Enhanced for production)
RATE_LIMIT_REQUESTS_PER_WINDOW=1000
RATE_LIMIT_WINDOW_SECONDS=60
RATE_LIMIT_REDIS_ENABLED=true

# CORS Configuration
CORS_ALLOWED_ORIGINS=https://goreal.com,https://www.goreal.com
CORS_ALLOWED_METHODS=GET,POST,PUT,DELETE,OPTIONS
CORS_ALLOWED_HEADERS=Content-Type,Authorization,X-Requested-With
CORS_ALLOW_CREDENTIALS=true

# Observability
JAEGER_ENDPOINT=http://jaeger-collector:14268/api/traces
LOG_LEVEL=info
ENABLE_AUDIT_LOG=true

# Email Configuration
SMTP_HOST=${SMTP_HOST}
SMTP_PORT=587
SMTP_USERNAME=${SMTP_USERNAME}
SMTP_PASSWORD=${SMTP_PASSWORD}
SMTP_FROM=<EMAIL>
```

#### Frontend Production Environment (`.env.production`)
```bash
# API Configuration
NEXT_PUBLIC_API_URL=https://api.goreal.com
NEXT_PUBLIC_WS_URL=wss://api.goreal.com

# Application Configuration
NEXT_PUBLIC_APP_NAME=GoReal Platform
NEXT_PUBLIC_APP_VERSION=1.0.0
NEXT_PUBLIC_ENVIRONMENT=production

# Feature Flags
NEXT_PUBLIC_ENABLE_ANALYTICS=true
NEXT_PUBLIC_ENABLE_NOTIFICATIONS=true
NEXT_PUBLIC_ENABLE_DARK_MODE=true

# Analytics (if using)
NEXT_PUBLIC_GA_ID=${GA_ID}
NEXT_PUBLIC_HOTJAR_ID=${HOTJAR_ID}

# Security
NEXT_PUBLIC_CSP_NONCE=auto
```

### Secrets Management

Use a secrets management system like HashiCorp Vault, AWS Secrets Manager, or Kubernetes secrets:

```bash
# Example using environment variables with external secrets
export DB_PASSWORD=$(vault kv get -field=password secret/goreal/db)
export REDIS_PASSWORD=$(vault kv get -field=password secret/goreal/redis)
export JWT_ACCESS_SECRET=$(vault kv get -field=access_secret secret/goreal/jwt)
export JWT_REFRESH_SECRET=$(vault kv get -field=refresh_secret secret/goreal/jwt)
```

## Docker Production Deployment

### Production Docker Compose

Create `docker-compose.prod.yml`:
```yaml
version: '3.8'

services:
  # PostgreSQL Primary
  postgres-primary:
    image: postgres:15-alpine
    environment:
      POSTGRES_DB: goreal_prod
      POSTGRES_USER: goreal_user
      POSTGRES_PASSWORD_FILE: /run/secrets/db_password
      POSTGRES_INITDB_ARGS: "--auth-host=scram-sha-256"
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./postgres/postgresql.conf:/etc/postgresql/postgresql.conf
      - ./postgres/pg_hba.conf:/etc/postgresql/pg_hba.conf
    secrets:
      - db_password
    networks:
      - backend
    restart: unless-stopped
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U goreal_user -d goreal_prod"]
      interval: 10s
      timeout: 5s
      retries: 5

  # Redis Cluster
  redis:
    image: redis:7-alpine
    command: >
      redis-server 
      --requirepass $$(cat /run/secrets/redis_password)
      --appendonly yes
      --appendfsync everysec
      --maxmemory 1gb
      --maxmemory-policy allkeys-lru
    volumes:
      - redis_data:/data
    secrets:
      - redis_password
    networks:
      - backend
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "redis-cli", "--raw", "incr", "ping"]
      interval: 10s
      timeout: 3s
      retries: 5

  # Backend API
  backend:
    build:
      context: ./backend
      dockerfile: Dockerfile.prod
      args:
        - BUILD_VERSION=${BUILD_VERSION:-latest}
    environment:
      - DB_HOST=postgres-primary
      - REDIS_HOST=redis
    env_file:
      - .env.production
    depends_on:
      postgres-primary:
        condition: service_healthy
      redis:
        condition: service_healthy
    networks:
      - backend
      - frontend
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8080/health"]
      interval: 30s
      timeout: 10s
      retries: 3
    deploy:
      replicas: 2
      resources:
        limits:
          cpus: '2'
          memory: 2G
        reservations:
          cpus: '1'
          memory: 1G

  # Frontend
  frontend:
    build:
      context: ./client
      dockerfile: Dockerfile.prod
      args:
        - BUILD_VERSION=${BUILD_VERSION:-latest}
    env_file:
      - .env.production
    depends_on:
      - backend
    networks:
      - frontend
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:3000/api/health"]
      interval: 30s
      timeout: 10s
      retries: 3
    deploy:
      replicas: 2
      resources:
        limits:
          cpus: '1'
          memory: 1G
        reservations:
          cpus: '0.5'
          memory: 512M

  # nginx Reverse Proxy
  nginx:
    image: nginx:alpine
    volumes:
      - ./nginx/nginx.conf:/etc/nginx/nginx.conf:ro
      - ./nginx/conf.d:/etc/nginx/conf.d:ro
      - ./ssl:/etc/nginx/ssl:ro
      - nginx_cache:/var/cache/nginx
    ports:
      - "80:80"
      - "443:443"
    depends_on:
      - frontend
      - backend
    networks:
      - frontend
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "nginx", "-t"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Monitoring Stack
  prometheus:
    image: prom/prometheus:latest
    volumes:
      - ./monitoring/prometheus.yml:/etc/prometheus/prometheus.yml:ro
      - prometheus_data:/prometheus
    command:
      - '--config.file=/etc/prometheus/prometheus.yml'
      - '--storage.tsdb.path=/prometheus'
      - '--web.console.libraries=/etc/prometheus/console_libraries'
      - '--web.console.templates=/etc/prometheus/consoles'
      - '--storage.tsdb.retention.time=30d'
      - '--web.enable-lifecycle'
    networks:
      - monitoring
    restart: unless-stopped

  grafana:
    image: grafana/grafana:latest
    environment:
      - GF_SECURITY_ADMIN_PASSWORD=${GRAFANA_PASSWORD}
      - GF_USERS_ALLOW_SIGN_UP=false
    volumes:
      - grafana_data:/var/lib/grafana
      - ./monitoring/grafana/dashboards:/etc/grafana/provisioning/dashboards:ro
      - ./monitoring/grafana/datasources:/etc/grafana/provisioning/datasources:ro
    ports:
      - "3001:3000"
    networks:
      - monitoring
    restart: unless-stopped

  jaeger:
    image: jaegertracing/all-in-one:latest
    environment:
      - COLLECTOR_OTLP_ENABLED=true
    ports:
      - "16686:16686"
      - "14268:14268"
    networks:
      - monitoring
    restart: unless-stopped

networks:
  backend:
    driver: bridge
  frontend:
    driver: bridge
  monitoring:
    driver: bridge

volumes:
  postgres_data:
    driver: local
  redis_data:
    driver: local
  prometheus_data:
    driver: local
  grafana_data:
    driver: local
  nginx_cache:
    driver: local

secrets:
  db_password:
    file: ./secrets/db_password.txt
  redis_password:
    file: ./secrets/redis_password.txt
```

### Production Dockerfiles

#### Backend Production Dockerfile (`backend/Dockerfile.prod`)
```dockerfile
# Build stage
FROM golang:1.22-alpine AS builder

# Install build dependencies
RUN apk add --no-cache git ca-certificates tzdata

WORKDIR /app

# Copy go mod files
COPY go.mod go.sum ./
RUN go mod download

# Copy source code
COPY . .

# Build the application
RUN CGO_ENABLED=0 GOOS=linux go build \
    -a -installsuffix cgo \
    -ldflags '-extldflags "-static" -s -w' \
    -o main cmd/api/main.go

# Production stage
FROM scratch

# Copy timezone data and certificates
COPY --from=builder /usr/share/zoneinfo /usr/share/zoneinfo
COPY --from=builder /etc/ssl/certs/ca-certificates.crt /etc/ssl/certs/

# Copy the binary
COPY --from=builder /app/main /main

# Create non-root user
USER 65534:65534

EXPOSE 8080

HEALTHCHECK --interval=30s --timeout=10s --start-period=5s --retries=3 \
  CMD ["/main", "healthcheck"]

CMD ["/main"]
```

#### Frontend Production Dockerfile (`client/Dockerfile.prod`)
```dockerfile
# Build stage
FROM node:18-alpine AS builder

WORKDIR /app

# Copy package files
COPY package*.json ./
RUN npm ci --only=production --frozen-lockfile

# Copy source code
COPY . .

# Build the application
RUN npm run build

# Production stage
FROM node:18-alpine

# Create app user
RUN addgroup -g 1001 -S nodejs
RUN adduser -S nextjs -u 1001

WORKDIR /app

# Copy built application
COPY --from=builder /app/next.config.js ./
COPY --from=builder /app/public ./public
COPY --from=builder /app/package.json ./package.json

# Copy built files with correct permissions
COPY --from=builder --chown=nextjs:nodejs /app/.next/standalone ./
COPY --from=builder --chown=nextjs:nodejs /app/.next/static ./.next/static

USER nextjs

EXPOSE 3000

ENV PORT 3000
ENV NODE_ENV production

HEALTHCHECK --interval=30s --timeout=10s --start-period=5s --retries=3 \
  CMD curl -f http://localhost:3000/api/health || exit 1

CMD ["node", "server.js"]
```

## Monitoring and Observability

### Prometheus Configuration (`monitoring/prometheus.yml`)
```yaml
global:
  scrape_interval: 15s
  evaluation_interval: 15s

rule_files:
  - "rules/*.yml"

scrape_configs:
  - job_name: 'goreal-backend'
    static_configs:
      - targets: ['backend:8080']
    metrics_path: '/metrics'
    scrape_interval: 5s

  - job_name: 'goreal-frontend'
    static_configs:
      - targets: ['frontend:3000']
    metrics_path: '/api/metrics'
    scrape_interval: 15s

  - job_name: 'postgres'
    static_configs:
      - targets: ['postgres-exporter:9187']

  - job_name: 'redis'
    static_configs:
      - targets: ['redis-exporter:9121']

  - job_name: 'nginx'
    static_configs:
      - targets: ['nginx-exporter:9113']

  - job_name: 'node-exporter'
    static_configs:
      - targets: ['node-exporter:9100']

alerting:
  alertmanagers:
    - static_configs:
        - targets:
          - alertmanager:9093
```

### Grafana Dashboard Configuration
Create dashboards for:
- **Application Metrics**: Request rates, response times, error rates
- **Infrastructure Metrics**: CPU, memory, disk, network usage
- **Database Metrics**: Connection pools, query performance, locks
- **Security Metrics**: Authentication failures, rate limit hits, suspicious activity

### Alert Rules (`monitoring/rules/alerts.yml`)
```yaml
groups:
  - name: goreal-alerts
    rules:
      - alert: HighErrorRate
        expr: rate(http_requests_total{status=~"5.."}[5m]) > 0.1
        for: 5m
        labels:
          severity: critical
        annotations:
          summary: "High error rate detected"
          description: "Error rate is {{ $value }} errors per second"

      - alert: HighResponseTime
        expr: histogram_quantile(0.95, rate(http_request_duration_seconds_bucket[5m])) > 1
        for: 5m
        labels:
          severity: warning
        annotations:
          summary: "High response time detected"
          description: "95th percentile response time is {{ $value }} seconds"

      - alert: DatabaseConnectionsHigh
        expr: pg_stat_activity_count > 80
        for: 5m
        labels:
          severity: warning
        annotations:
          summary: "High database connections"
          description: "Database has {{ $value }} active connections"
```

## Performance Optimization

### Database Optimization

1. **PostgreSQL Configuration** (`postgres/postgresql.conf`):
```ini
# Memory settings
shared_buffers = 2GB
effective_cache_size = 6GB
work_mem = 64MB
maintenance_work_mem = 512MB

# Connection settings
max_connections = 100
shared_preload_libraries = 'pg_stat_statements'

# WAL settings
wal_buffers = 64MB
checkpoint_completion_target = 0.9
wal_compression = on

# Query optimization
random_page_cost = 1.1
effective_io_concurrency = 200
```

2. **Database Indexing Strategy**:
```sql
-- User lookup optimization
CREATE INDEX CONCURRENTLY idx_users_email ON users(email);
CREATE INDEX CONCURRENTLY idx_users_username ON users(username);
CREATE INDEX CONCURRENTLY idx_users_active ON users(is_active) WHERE is_active = true;

-- Task optimization
CREATE INDEX CONCURRENTLY idx_tasks_assignee ON tasks(assignee_id);
CREATE INDEX CONCURRENTLY idx_tasks_status ON tasks(status);
CREATE INDEX CONCURRENTLY idx_tasks_created_at ON tasks(created_at DESC);
CREATE INDEX CONCURRENTLY idx_tasks_due_date ON tasks(due_date) WHERE due_date IS NOT NULL;

-- Audit log optimization
CREATE INDEX CONCURRENTLY idx_audit_logs_timestamp ON audit_logs(timestamp DESC);
CREATE INDEX CONCURRENTLY idx_audit_logs_user_id ON audit_logs(user_id);
CREATE INDEX CONCURRENTLY idx_audit_logs_event_type ON audit_logs(event_type);
```

### Redis Optimization

1. **Redis Configuration**:
```ini
# Memory optimization
maxmemory 2gb
maxmemory-policy allkeys-lru

# Persistence
save 900 1
save 300 10
save 60 10000

# Network optimization
tcp-keepalive 300
timeout 0

# Security
requirepass your-secure-redis-password
```

### nginx Optimization

1. **nginx Configuration** (`nginx/nginx.conf`):
```nginx
user nginx;
worker_processes auto;
worker_rlimit_nofile 65535;

events {
    worker_connections 4096;
    use epoll;
    multi_accept on;
}

http {
    # Basic settings
    sendfile on;
    tcp_nopush on;
    tcp_nodelay on;
    keepalive_timeout 65;
    types_hash_max_size 2048;
    client_max_body_size 10M;

    # Gzip compression
    gzip on;
    gzip_vary on;
    gzip_min_length 1024;
    gzip_comp_level 6;
    gzip_types
        text/plain
        text/css
        text/xml
        text/javascript
        application/javascript
        application/xml+rss
        application/json;

    # Rate limiting
    limit_req_zone $binary_remote_addr zone=api:10m rate=10r/s;
    limit_req_zone $binary_remote_addr zone=login:10m rate=1r/s;

    # Caching
    proxy_cache_path /var/cache/nginx levels=1:2 keys_zone=app_cache:10m max_size=1g inactive=60m use_temp_path=off;

    include /etc/nginx/conf.d/*.conf;
}
```

## Backup and Recovery

### Database Backup Strategy

1. **Automated Backup Script** (`scripts/backup-db.sh`):
```bash
#!/bin/bash

BACKUP_DIR="/backups/postgres"
DATE=$(date +%Y%m%d_%H%M%S)
DB_NAME="goreal_prod"

# Create backup directory
mkdir -p $BACKUP_DIR

# Full database backup
pg_dump -h postgres-primary -U goreal_user -d $DB_NAME \
    --format=custom --compress=9 \
    --file="$BACKUP_DIR/goreal_full_$DATE.dump"

# Cleanup old backups (keep 30 days)
find $BACKUP_DIR -name "goreal_full_*.dump" -mtime +30 -delete

# Upload to cloud storage (optional)
# aws s3 cp "$BACKUP_DIR/goreal_full_$DATE.dump" s3://your-backup-bucket/
```

2. **Backup Cron Job**:
```bash
# Add to crontab
0 2 * * * /opt/goreal/scripts/backup-db.sh
```

### Disaster Recovery Plan

1. **Database Recovery**:
```bash
# Restore from backup
pg_restore -h postgres-primary -U goreal_user -d goreal_prod \
    --clean --if-exists /backups/postgres/goreal_full_20240115_020000.dump
```

2. **Application Recovery**:
```bash
# Pull latest images and restart services
docker-compose -f docker-compose.prod.yml pull
docker-compose -f docker-compose.prod.yml up -d
```

## Troubleshooting

### Common Issues and Solutions

1. **High Memory Usage**:
```bash
# Check memory usage
free -h
docker stats

# Optimize PostgreSQL memory settings
# Reduce shared_buffers and work_mem if needed
```

2. **Database Connection Issues**:
```bash
# Check PostgreSQL status
docker-compose exec postgres-primary pg_isready

# Check connection pool
docker-compose logs backend | grep "connection pool"

# Monitor active connections
docker-compose exec postgres-primary psql -U goreal_user -d goreal_prod \
    -c "SELECT count(*) FROM pg_stat_activity;"
```

3. **Redis Connection Issues**:
```bash
# Test Redis connectivity
docker-compose exec redis redis-cli ping

# Check Redis memory usage
docker-compose exec redis redis-cli info memory

# Monitor Redis commands
docker-compose exec redis redis-cli monitor
```

4. **Application Performance Issues**:
```bash
# Check application logs
docker-compose logs -f backend
docker-compose logs -f frontend

# Monitor resource usage
docker stats

# Check nginx access logs
docker-compose exec nginx tail -f /var/log/nginx/access.log
```

### Health Check Endpoints

- **Backend Health**: `https://api.goreal.com/health`
- **Frontend Health**: `https://goreal.com/api/health`
- **Database Health**: Check via monitoring dashboard
- **Redis Health**: Check via monitoring dashboard

### Log Analysis

1. **Centralized Logging** (optional with ELK stack):
```yaml
# Add to docker-compose.prod.yml
  elasticsearch:
    image: docker.elastic.co/elasticsearch/elasticsearch:8.5.0
    environment:
      - discovery.type=single-node
      - "ES_JAVA_OPTS=-Xms1g -Xmx1g"
    volumes:
      - elasticsearch_data:/usr/share/elasticsearch/data

  logstash:
    image: docker.elastic.co/logstash/logstash:8.5.0
    volumes:
      - ./logstash/pipeline:/usr/share/logstash/pipeline:ro

  kibana:
    image: docker.elastic.co/kibana/kibana:8.5.0
    ports:
      - "5601:5601"
    environment:
      - ELASTICSEARCH_HOSTS=http://elasticsearch:9200
```

---

This production deployment guide provides enterprise-grade deployment strategies with comprehensive security, monitoring, and performance optimizations. For additional support or customization, refer to the troubleshooting section or contact the development team.
