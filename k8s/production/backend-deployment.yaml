# ============================================================================
# BACKEND KUBERNETES DEPLOYMENT
# ============================================================================

apiVersion: apps/v1
kind: Deployment
metadata:
  name: goreal-backend
  namespace: goreal-production
  labels:
    app: goreal-backend
    component: backend
    environment: production
spec:
  replicas: 3
  strategy:
    type: RollingUpdate
    rollingUpdate:
      maxSurge: 1
      maxUnavailable: 0
  selector:
    matchLabels:
      app: goreal-backend
  template:
    metadata:
      labels:
        app: goreal-backend
        component: backend
        environment: production
      annotations:
        prometheus.io/scrape: "true"
        prometheus.io/port: "8080"
        prometheus.io/path: "/metrics"
    spec:
      serviceAccountName: goreal-backend
      securityContext:
        runAsNonRoot: true
        runAsUser: 65534
        fsGroup: 65534
      containers:
      - name: backend
        image: ghcr.io/your-org/goreal-backend:latest
        imagePullPolicy: Always
        ports:
        - containerPort: 8080
          name: http
          protocol: TCP
        env:
        - name: PORT
          value: "8080"
        - name: ENVIRONMENT
          value: "production"
        - name: DB_HOST
          valueFrom:
            secretKeyRef:
              name: goreal-secrets
              key: db-host
        - name: DB_PORT
          value: "5432"
        - name: DB_NAME
          valueFrom:
            secretKeyRef:
              name: goreal-secrets
              key: db-name
        - name: DB_USER
          valueFrom:
            secretKeyRef:
              name: goreal-secrets
              key: db-user
        - name: DB_PASSWORD
          valueFrom:
            secretKeyRef:
              name: goreal-secrets
              key: db-password
        - name: REDIS_HOST
          valueFrom:
            secretKeyRef:
              name: goreal-secrets
              key: redis-host
        - name: REDIS_PORT
          value: "6379"
        - name: REDIS_PASSWORD
          valueFrom:
            secretKeyRef:
              name: goreal-secrets
              key: redis-password
        - name: JWT_ACCESS_SECRET
          valueFrom:
            secretKeyRef:
              name: goreal-secrets
              key: jwt-access-secret
        - name: JWT_REFRESH_SECRET
          valueFrom:
            secretKeyRef:
              name: goreal-secrets
              key: jwt-refresh-secret
        resources:
          requests:
            memory: "256Mi"
            cpu: "250m"
          limits:
            memory: "512Mi"
            cpu: "500m"
        livenessProbe:
          httpGet:
            path: /health
            port: 8080
          initialDelaySeconds: 30
          periodSeconds: 10
          timeoutSeconds: 5
          failureThreshold: 3
        readinessProbe:
          httpGet:
            path: /health
            port: 8080
          initialDelaySeconds: 5
          periodSeconds: 5
          timeoutSeconds: 3
          failureThreshold: 3
        startupProbe:
          httpGet:
            path: /health
            port: 8080
          initialDelaySeconds: 10
          periodSeconds: 10
          timeoutSeconds: 5
          failureThreshold: 30
        securityContext:
          allowPrivilegeEscalation: false
          readOnlyRootFilesystem: true
          runAsNonRoot: true
          runAsUser: 65534
          capabilities:
            drop:
            - ALL

---
apiVersion: v1
kind: Service
metadata:
  name: goreal-backend-service
  namespace: goreal-production
  labels:
    app: goreal-backend
    component: backend
spec:
  type: ClusterIP
  ports:
  - port: 80
    targetPort: 8080
    protocol: TCP
    name: http
  selector:
    app: goreal-backend

---
apiVersion: v1
kind: ServiceAccount
metadata:
  name: goreal-backend
  namespace: goreal-production
  labels:
    app: goreal-backend
    component: backend

---
apiVersion: policy/v1
kind: PodDisruptionBudget
metadata:
  name: goreal-backend-pdb
  namespace: goreal-production
spec:
  minAvailable: 2
  selector:
    matchLabels:
      app: goreal-backend

---
apiVersion: autoscaling/v2
kind: HorizontalPodAutoscaler
metadata:
  name: goreal-backend-hpa
  namespace: goreal-production
spec:
  scaleTargetRef:
    apiVersion: apps/v1
    kind: Deployment
    name: goreal-backend
  minReplicas: 3
  maxReplicas: 10
  metrics:
  - type: Resource
    resource:
      name: cpu
      target:
        type: Utilization
        averageUtilization: 70
  - type: Resource
    resource:
      name: memory
      target:
        type: Utilization
        averageUtilization: 80
  behavior:
    scaleDown:
      stabilizationWindowSeconds: 300
      policies:
      - type: Percent
        value: 10
        periodSeconds: 60
    scaleUp:
      stabilizationWindowSeconds: 60
      policies:
      - type: Percent
        value: 50
        periodSeconds: 60
