# ============================================================================
# FRONTEND KUBERNETES DEPLOYMENT
# ============================================================================

apiVersion: apps/v1
kind: Deployment
metadata:
  name: goreal-frontend
  namespace: goreal-production
  labels:
    app: goreal-frontend
    component: frontend
    environment: production
spec:
  replicas: 3
  strategy:
    type: RollingUpdate
    rollingUpdate:
      maxSurge: 1
      maxUnavailable: 0
  selector:
    matchLabels:
      app: goreal-frontend
  template:
    metadata:
      labels:
        app: goreal-frontend
        component: frontend
        environment: production
    spec:
      serviceAccountName: goreal-frontend
      securityContext:
        runAsNonRoot: true
        runAsUser: 1001
        fsGroup: 1001
      containers:
      - name: frontend
        image: ghcr.io/your-org/goreal-frontend:latest
        imagePullPolicy: Always
        ports:
        - containerPort: 3000
          name: http
          protocol: TCP
        env:
        - name: NODE_ENV
          value: "production"
        - name: PORT
          value: "3000"
        - name: HOSTNAME
          value: "0.0.0.0"
        - name: NEXT_PUBLIC_API_URL
          value: "https://api.goreal.com"
        - name: NEXT_PUBLIC_APP_NAME
          value: "GoReal Platform"
        - name: NEXT_PUBLIC_ENVIRONMENT
          value: "production"
        resources:
          requests:
            memory: "128Mi"
            cpu: "100m"
          limits:
            memory: "256Mi"
            cpu: "200m"
        livenessProbe:
          httpGet:
            path: /api/health
            port: 3000
          initialDelaySeconds: 30
          periodSeconds: 10
          timeoutSeconds: 5
          failureThreshold: 3
        readinessProbe:
          httpGet:
            path: /api/health
            port: 3000
          initialDelaySeconds: 5
          periodSeconds: 5
          timeoutSeconds: 3
          failureThreshold: 3
        startupProbe:
          httpGet:
            path: /api/health
            port: 3000
          initialDelaySeconds: 10
          periodSeconds: 10
          timeoutSeconds: 5
          failureThreshold: 30
        securityContext:
          allowPrivilegeEscalation: false
          readOnlyRootFilesystem: true
          runAsNonRoot: true
          runAsUser: 1001
          capabilities:
            drop:
            - ALL

---
apiVersion: v1
kind: Service
metadata:
  name: goreal-frontend-service
  namespace: goreal-production
  labels:
    app: goreal-frontend
    component: frontend
spec:
  type: ClusterIP
  ports:
  - port: 80
    targetPort: 3000
    protocol: TCP
    name: http
  selector:
    app: goreal-frontend

---
apiVersion: v1
kind: ServiceAccount
metadata:
  name: goreal-frontend
  namespace: goreal-production
  labels:
    app: goreal-frontend
    component: frontend

---
apiVersion: policy/v1
kind: PodDisruptionBudget
metadata:
  name: goreal-frontend-pdb
  namespace: goreal-production
spec:
  minAvailable: 2
  selector:
    matchLabels:
      app: goreal-frontend

---
apiVersion: autoscaling/v2
kind: HorizontalPodAutoscaler
metadata:
  name: goreal-frontend-hpa
  namespace: goreal-production
spec:
  scaleTargetRef:
    apiVersion: apps/v1
    kind: Deployment
    name: goreal-frontend
  minReplicas: 3
  maxReplicas: 8
  metrics:
  - type: Resource
    resource:
      name: cpu
      target:
        type: Utilization
        averageUtilization: 70
  - type: Resource
    resource:
      name: memory
      target:
        type: Utilization
        averageUtilization: 80
  behavior:
    scaleDown:
      stabilizationWindowSeconds: 300
      policies:
      - type: Percent
        value: 10
        periodSeconds: 60
    scaleUp:
      stabilizationWindowSeconds: 60
      policies:
      - type: Percent
        value: 50
        periodSeconds: 60
