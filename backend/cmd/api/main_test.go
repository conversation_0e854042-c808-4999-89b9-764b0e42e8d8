package main

import (
	"encoding/json"
	"net/http"
	"net/http/httptest"
	"testing"

	"goreal-backend/internal/config"
	"goreal-backend/internal/middleware"
)

// TestHealthEndpoint tests the health check endpoint
func TestHealthEndpoint(t *testing.T) {
	// Create a test server
	mux := http.NewServeMux()
	
	// Add health endpoint
	mux.HandleFunc("GET /health", func(w http.ResponseWriter, r *http.Request) {
		w.Header().Set("Content-Type", "application/json")
		w.WriteHeader(http.StatusOK)
		w.Write([]byte(`{"status":"healthy","service":"goreal-backend"}`))
	})

	// Create test request
	req := httptest.NewRequest("GET", "/health", nil)
	w := httptest.NewRecorder()

	// Execute request
	mux.ServeHTTP(w, req)

	// Check response
	if w.Code != http.StatusOK {
		t.<PERSON>rf("Expected status code %d, got %d", http.StatusOK, w.Code)
	}

	var response map[string]interface{}
	if err := json.Unmarshal(w.Body.Bytes(), &response); err != nil {
		t.Errorf("Failed to parse response: %v", err)
	}

	if response["status"] != "healthy" {
		t.Errorf("Expected status 'healthy', got %v", response["status"])
	}
}

// TestObservabilityMiddleware tests the enhanced observability middleware
func TestObservabilityMiddleware(t *testing.T) {
	// Create a simple handler
	handler := http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		w.WriteHeader(http.StatusOK)
		w.Write([]byte("test response"))
	})

	// Wrap with observability middleware
	wrappedHandler := middleware.Observability(handler)

	// Create test request
	req := httptest.NewRequest("GET", "/test", nil)
	w := httptest.NewRecorder()

	// Execute request
	wrappedHandler.ServeHTTP(w, req)

	// Check response
	if w.Code != http.StatusOK {
		t.Errorf("Expected status code %d, got %d", http.StatusOK, w.Code)
	}

	// Check that correlation ID was added
	correlationID := w.Header().Get("X-Correlation-ID")
	if correlationID == "" {
		t.Error("Expected X-Correlation-ID header to be set")
	}

	// Check that trace ID was added (if tracing is enabled)
	traceID := w.Header().Get("X-Trace-ID")
	if traceID != "" {
		t.Logf("Trace ID: %s", traceID)
	}
}

// TestCORSMiddleware tests the CORS middleware
func TestCORSMiddleware(t *testing.T) {
	// Create CORS config
	corsConfig := config.CORSConfig{
		AllowedOrigins: []string{"http://localhost:3000", "https://example.com"},
	}

	// Create a simple handler
	handler := http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		w.WriteHeader(http.StatusOK)
		w.Write([]byte("test response"))
	})

	// Wrap with CORS middleware
	wrappedHandler := middleware.CORS(corsConfig)(handler)

	// Test allowed origin
	req := httptest.NewRequest("GET", "/test", nil)
	req.Header.Set("Origin", "http://localhost:3000")
	w := httptest.NewRecorder()

	wrappedHandler.ServeHTTP(w, req)

	// Check CORS headers
	allowOrigin := w.Header().Get("Access-Control-Allow-Origin")
	if allowOrigin != "http://localhost:3000" {
		t.Errorf("Expected Access-Control-Allow-Origin to be 'http://localhost:3000', got '%s'", allowOrigin)
	}

	// Test preflight request
	req = httptest.NewRequest("OPTIONS", "/test", nil)
	req.Header.Set("Origin", "http://localhost:3000")
	w = httptest.NewRecorder()

	wrappedHandler.ServeHTTP(w, req)

	if w.Code != http.StatusOK {
		t.Errorf("Expected status code %d for OPTIONS request, got %d", http.StatusOK, w.Code)
	}
}

// TestJSONContentTypeMiddleware tests the JSON content type middleware
func TestJSONContentTypeMiddleware(t *testing.T) {
	// Create a simple handler
	handler := http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		w.WriteHeader(http.StatusOK)
		w.Write([]byte(`{"message": "test"}`))
	})

	// Wrap with JSON content type middleware
	wrappedHandler := middleware.JSONContentType(handler)

	// Create test request
	req := httptest.NewRequest("GET", "/test", nil)
	w := httptest.NewRecorder()

	// Execute request
	wrappedHandler.ServeHTTP(w, req)

	// Check content type
	contentType := w.Header().Get("Content-Type")
	if contentType != "application/json" {
		t.Errorf("Expected Content-Type to be 'application/json', got '%s'", contentType)
	}
}

// TestAuthHandlerStdlib tests the standard library auth handler
func TestAuthHandlerStdlib(t *testing.T) {
	// This test would require a full container setup
	// For now, we'll test the basic structure
	t.Skip("Skipping auth handler test - requires full container setup")
}

// TestTaskHandlerStdlib tests the standard library task handler
func TestTaskHandlerStdlib(t *testing.T) {
	// This test would require a full container setup
	// For now, we'll test the basic structure
	t.Skip("Skipping task handler test - requires full container setup")
}

// TestUserHandlerStdlib tests the standard library user handler
func TestUserHandlerStdlib(t *testing.T) {
	// This test would require a full container setup
	// For now, we'll test the basic structure
	t.Skip("Skipping user handler test - requires full container setup")
}

// TestNotificationHandlerStdlib tests the standard library notification handler
func TestNotificationHandlerStdlib(t *testing.T) {
	// This test would require a full container setup
	// For now, we'll test the basic structure
	t.Skip("Skipping notification handler test - requires full container setup")
}

// TestAnalyticsHandlerStdlib tests the standard library analytics handler
func TestAnalyticsHandlerStdlib(t *testing.T) {
	// This test would require a full container setup
	// For now, we'll test the basic structure
	t.Skip("Skipping analytics handler test - requires full container setup")
}

// TestMiddlewareChain tests the complete middleware chain
func TestMiddlewareChain(t *testing.T) {
	// Create a simple handler
	handler := http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		w.WriteHeader(http.StatusOK)
		w.Write([]byte(`{"message": "success"}`))
	})

	// Create middleware chain
	var wrappedHandler http.Handler = handler
	wrappedHandler = middleware.JSONContentType(wrappedHandler)
	wrappedHandler = middleware.Observability(wrappedHandler)
	wrappedHandler = middleware.ErrorHandler(wrappedHandler)
	wrappedHandler = middleware.RequestLogger(wrappedHandler)

	// Create test request
	req := httptest.NewRequest("GET", "/test", nil)
	w := httptest.NewRecorder()

	// Execute request
	wrappedHandler.ServeHTTP(w, req)

	// Check response
	if w.Code != http.StatusOK {
		t.Errorf("Expected status code %d, got %d", http.StatusOK, w.Code)
	}

	// Check content type
	contentType := w.Header().Get("Content-Type")
	if contentType != "application/json" {
		t.Errorf("Expected Content-Type to be 'application/json', got '%s'", contentType)
	}

	// Check correlation ID
	correlationID := w.Header().Get("X-Correlation-ID")
	if correlationID == "" {
		t.Error("Expected X-Correlation-ID header to be set")
	}
}

// BenchmarkObservabilityMiddleware benchmarks the observability middleware
func BenchmarkObservabilityMiddleware(b *testing.B) {
	// Create a simple handler
	handler := http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		w.WriteHeader(http.StatusOK)
		w.Write([]byte("test response"))
	})

	// Wrap with observability middleware
	wrappedHandler := middleware.Observability(handler)

	// Create test request
	req := httptest.NewRequest("GET", "/test", nil)

	b.ResetTimer()
	for i := 0; i < b.N; i++ {
		w := httptest.NewRecorder()
		wrappedHandler.ServeHTTP(w, req)
	}
}

// BenchmarkMiddlewareChain benchmarks the complete middleware chain
func BenchmarkMiddlewareChain(b *testing.B) {
	// Create a simple handler
	handler := http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		w.WriteHeader(http.StatusOK)
		w.Write([]byte(`{"message": "success"}`))
	})

	// Create middleware chain
	var wrappedHandler http.Handler = handler
	wrappedHandler = middleware.JSONContentType(wrappedHandler)
	wrappedHandler = middleware.Observability(wrappedHandler)
	wrappedHandler = middleware.ErrorHandler(wrappedHandler)
	wrappedHandler = middleware.RequestLogger(wrappedHandler)

	// Create test request
	req := httptest.NewRequest("GET", "/test", nil)

	b.ResetTimer()
	for i := 0; i < b.N; i++ {
		w := httptest.NewRecorder()
		wrappedHandler.ServeHTTP(w, req)
	}
}
