package test

import (
	"encoding/json"
	"net/http"
	"net/http/httptest"
	"os"
	"testing"
	"time"

	"goreal-backend/internal/config"
	"goreal-backend/internal/container"
	"goreal-backend/internal/domain"
	"goreal-backend/internal/handlers"
	"goreal-backend/internal/middleware"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
)

// TestIntegration runs comprehensive integration tests
func TestIntegration(t *testing.T) {
	// Skip integration tests if not in CI or if explicitly disabled
	if os.Getenv("SKIP_INTEGRATION") == "true" {
		t.Skip("Integration tests disabled")
	}

	// Setup test configuration
	cfg := &config.Config{
		Port:        "8080",
		Environment: "test",
		ServiceName: "goreal-backend-test",

		// Database (Supabase)
		SupabaseURL:       "http://localhost:54321",
		SupabaseKey:       "test-anon-key",
		SupabaseSecretKey: "test-service-role-key",

		// JWT
		JWT: config.JWTConfig{
			AccessSecret:       "test-access-secret-key",
			RefreshSecret:      "test-refresh-secret-key",
			AccessTokenExpiry:  time.Hour,
			RefreshTokenExpiry: 24 * time.Hour,
		},

		// CORS
		CORS: config.CORSConfig{
			AllowedOrigins: []string{"http://localhost:3000"},
		},

		// Rate limiting
		RateLimit: config.RateLimitConfig{
			RequestsPerMinute: 100,
			BurstSize:         10,
		},

		// Observability
		JaegerEndpoint: "http://localhost:14268/api/traces",
		LogLevel:       "debug",
	}

	// Create container (this would normally connect to a test database)
	// For this test, we'll use mocks or skip database-dependent operations
	t.Run("Container Creation", func(t *testing.T) {
		// Test that container can be created without errors
		_, err := container.NewContainer()
		if err != nil {
			// If database connection fails, that's expected in test environment
			t.Logf("Container creation failed (expected in test env): %v", err)
		}
	})

	// Test middleware functionality
	t.Run("Middleware Tests", func(t *testing.T) {
		testMiddleware(t, cfg)
	})

	// Test handler creation
	t.Run("Handler Creation", func(t *testing.T) {
		testHandlerCreation(t)
	})

	// Test API endpoints (with mocked services)
	t.Run("API Endpoints", func(t *testing.T) {
		testAPIEndpoints(t, cfg)
	})
}

func testMiddleware(t *testing.T, cfg *config.Config) {
	// Test CORS middleware
	t.Run("CORS Middleware", func(t *testing.T) {
		handler := http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
			w.WriteHeader(http.StatusOK)
		})

		corsHandler := middleware.CORS(cfg.CORS)(handler)

		req := httptest.NewRequest("GET", "/test", nil)
		req.Header.Set("Origin", "http://localhost:3000")
		w := httptest.NewRecorder()

		corsHandler.ServeHTTP(w, req)

		assert.Equal(t, http.StatusOK, w.Code)
		assert.Equal(t, "http://localhost:3000", w.Header().Get("Access-Control-Allow-Origin"))
	})

	// Test Observability middleware
	t.Run("Observability Middleware", func(t *testing.T) {
		handler := http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
			w.WriteHeader(http.StatusOK)
			w.Write([]byte("test response"))
		})

		obsHandler := middleware.Observability(handler)

		req := httptest.NewRequest("GET", "/test", nil)
		w := httptest.NewRecorder()

		obsHandler.ServeHTTP(w, req)

		assert.Equal(t, http.StatusOK, w.Code)
		assert.NotEmpty(t, w.Header().Get("X-Correlation-ID"))
	})

	// Test Rate Limiting middleware
	t.Run("Rate Limiting Middleware", func(t *testing.T) {
		handler := http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
			w.WriteHeader(http.StatusOK)
		})

		rateLimitHandler := middleware.RateLimiter(cfg.RateLimit)(handler)

		// First request should succeed
		req := httptest.NewRequest("GET", "/test", nil)
		w := httptest.NewRecorder()
		rateLimitHandler.ServeHTTP(w, req)
		assert.Equal(t, http.StatusOK, w.Code)

		// Multiple rapid requests should eventually be rate limited
		// (This test might be flaky depending on rate limit implementation)
		for i := 0; i < 20; i++ {
			req = httptest.NewRequest("GET", "/test", nil)
			w = httptest.NewRecorder()
			rateLimitHandler.ServeHTTP(w, req)
			if w.Code == http.StatusTooManyRequests {
				t.Logf("Rate limiting triggered after %d requests", i+1)
				break
			}
		}
	})
}

func testHandlerCreation(t *testing.T) {
	// Test that handlers can be created with nil services (for structure testing)
	t.Run("Auth Handler", func(t *testing.T) {
		handler := handlers.NewAuthHandlerStdlib(nil)
		assert.NotNil(t, handler)
	})

	t.Run("Task Handler", func(t *testing.T) {
		handler := handlers.NewTaskHandlerStdlib(nil)
		assert.NotNil(t, handler)
	})

	t.Run("User Handler", func(t *testing.T) {
		handler := handlers.NewUserHandlerStdlib(nil)
		assert.NotNil(t, handler)
	})

	t.Run("Notification Handler", func(t *testing.T) {
		handler := handlers.NewNotificationHandlerStdlib(nil)
		assert.NotNil(t, handler)
	})

	t.Run("Analytics Handler", func(t *testing.T) {
		handler := handlers.NewAnalyticsHandlerStdlib(nil)
		assert.NotNil(t, handler)
	})
}

func testAPIEndpoints(t *testing.T, cfg *config.Config) {
	// Create a test server with basic routes
	mux := http.NewServeMux()

	// Add health endpoint
	mux.HandleFunc("GET /health", func(w http.ResponseWriter, r *http.Request) {
		w.Header().Set("Content-Type", "application/json")
		w.WriteHeader(http.StatusOK)
		json.NewEncoder(w).Encode(map[string]interface{}{
			"status":    "healthy",
			"service":   "goreal-backend",
			"timestamp": time.Now().Unix(),
		})
	})

	// Add version endpoint
	mux.HandleFunc("GET /version", func(w http.ResponseWriter, r *http.Request) {
		w.Header().Set("Content-Type", "application/json")
		w.WriteHeader(http.StatusOK)
		json.NewEncoder(w).Encode(map[string]interface{}{
			"version": "1.0.0",
			"build":   "test",
		})
	})

	// Add middleware
	var handler http.Handler = mux
	handler = middleware.JSONContentType(handler)
	handler = middleware.Observability(handler)
	handler = middleware.CORS(cfg.CORS)(handler)

	// Test health endpoint
	t.Run("Health Endpoint", func(t *testing.T) {
		req := httptest.NewRequest("GET", "/health", nil)
		w := httptest.NewRecorder()

		handler.ServeHTTP(w, req)

		assert.Equal(t, http.StatusOK, w.Code)
		assert.Equal(t, "application/json", w.Header().Get("Content-Type"))

		var response map[string]interface{}
		err := json.Unmarshal(w.Body.Bytes(), &response)
		require.NoError(t, err)
		assert.Equal(t, "healthy", response["status"])
		assert.Equal(t, "goreal-backend", response["service"])
	})

	// Test version endpoint
	t.Run("Version Endpoint", func(t *testing.T) {
		req := httptest.NewRequest("GET", "/version", nil)
		w := httptest.NewRecorder()

		handler.ServeHTTP(w, req)

		assert.Equal(t, http.StatusOK, w.Code)
		assert.Equal(t, "application/json", w.Header().Get("Content-Type"))

		var response map[string]interface{}
		err := json.Unmarshal(w.Body.Bytes(), &response)
		require.NoError(t, err)
		assert.Equal(t, "1.0.0", response["version"])
	})

	// Test 404 handling
	t.Run("404 Not Found", func(t *testing.T) {
		req := httptest.NewRequest("GET", "/nonexistent", nil)
		w := httptest.NewRecorder()

		handler.ServeHTTP(w, req)

		assert.Equal(t, http.StatusNotFound, w.Code)
	})

	// Test CORS headers
	t.Run("CORS Headers", func(t *testing.T) {
		req := httptest.NewRequest("GET", "/health", nil)
		req.Header.Set("Origin", "http://localhost:3000")
		w := httptest.NewRecorder()

		handler.ServeHTTP(w, req)

		assert.Equal(t, http.StatusOK, w.Code)
		assert.Equal(t, "http://localhost:3000", w.Header().Get("Access-Control-Allow-Origin"))
	})

	// Test OPTIONS preflight request
	t.Run("OPTIONS Preflight", func(t *testing.T) {
		req := httptest.NewRequest("OPTIONS", "/health", nil)
		req.Header.Set("Origin", "http://localhost:3000")
		req.Header.Set("Access-Control-Request-Method", "GET")
		w := httptest.NewRecorder()

		handler.ServeHTTP(w, req)

		// Should handle OPTIONS request
		assert.Contains(t, []int{http.StatusOK, http.StatusNoContent}, w.Code)
	})
}

// BenchmarkIntegration runs performance benchmarks
func BenchmarkIntegration(b *testing.B) {
	// Setup
	mux := http.NewServeMux()
	mux.HandleFunc("GET /health", func(w http.ResponseWriter, r *http.Request) {
		w.Header().Set("Content-Type", "application/json")
		w.WriteHeader(http.StatusOK)
		w.Write([]byte(`{"status":"healthy"}`))
	})

	var handler http.Handler = mux
	handler = middleware.JSONContentType(handler)
	handler = middleware.Observability(handler)

	req := httptest.NewRequest("GET", "/health", nil)

	b.ResetTimer()
	for i := 0; i < b.N; i++ {
		w := httptest.NewRecorder()
		handler.ServeHTTP(w, req)
	}
}

// TestDomainModels tests domain model validation and behavior
func TestDomainModels(t *testing.T) {
	t.Run("User Model", func(t *testing.T) {
		user := &domain.User{
			Email:    "<EMAIL>",
			Username: "testuser",
			FullName: "Test User",
			Role:     domain.RoleUser,
		}

		assert.Equal(t, "<EMAIL>", user.Email)
		assert.Equal(t, domain.RoleUser, user.Role)
	})

	t.Run("Task Model", func(t *testing.T) {
		task := &domain.Task{
			Title:    "Test Task",
			Priority: domain.TaskPriorityHigh,
			Status:   domain.TaskStatusPending,
		}

		assert.Equal(t, "Test Task", task.Title)
		assert.Equal(t, domain.TaskPriorityHigh, task.Priority)
		assert.Equal(t, domain.TaskStatusPending, task.Status)
	})
}

// TestErrorHandling tests error handling across the application
func TestErrorHandling(t *testing.T) {
	t.Run("Domain Errors", func(t *testing.T) {
		assert.NotNil(t, domain.ErrNotFound)
		assert.NotNil(t, domain.ErrInvalidCredentials)
		assert.NotNil(t, domain.ErrEmailAlreadyExists)
		assert.NotNil(t, domain.ErrRequiredField)
	})

	t.Run("Error Response Format", func(t *testing.T) {
		mux := http.NewServeMux()
		mux.HandleFunc("GET /error", func(w http.ResponseWriter, r *http.Request) {
			http.Error(w, `{"error": "Test error"}`, http.StatusBadRequest)
		})

		req := httptest.NewRequest("GET", "/error", nil)
		w := httptest.NewRecorder()

		mux.ServeHTTP(w, req)

		assert.Equal(t, http.StatusBadRequest, w.Code)
		assert.Contains(t, w.Body.String(), "Test error")
	})
}
