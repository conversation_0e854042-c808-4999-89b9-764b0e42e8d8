# ============================================================================
# PERFORMANCE CONFIGURATION
# ============================================================================

# Memory Management Configuration
memory:
  # Object pooling settings
  pools:
    buffer_pool:
      enabled: true
      sizes: [1024, 4096, 8192, 16384, 32768]
      max_objects_per_size: 1000
    
    object_pool:
      enabled: true
      cleanup_interval: "5m"
      max_idle_objects: 500

  # Garbage collection optimization
  gc:
    enabled: true
    target_percent: 100
    max_heap_size: "1GB"
    optimization_interval: "30s"
    
  # Memory monitoring
  monitoring:
    enabled: true
    collection_interval: "10s"
    alert_threshold_percent: 80

# HTTP Performance Configuration
http:
  # Server settings
  server:
    read_timeout: "15s"
    write_timeout: "15s"
    idle_timeout: "60s"
    max_header_bytes: 1048576  # 1MB
    
  # Compression settings
  compression:
    enabled: true
    level: 6  # Default compression level
    min_size: 1024  # Minimum response size to compress (bytes)
    types:
      - "text/html"
      - "text/css"
      - "text/javascript"
      - "text/plain"
      - "application/json"
      - "application/javascript"
      - "application/xml"
      - "application/rss+xml"
      - "application/atom+xml"
      - "image/svg+xml"
  
  # Caching configuration
  cache_control:
    static_max_age: 31536000  # 1 year for static assets
    api_max_age: 300          # 5 minutes for API responses
    enable_etags: true
    enable_vary: true
  
  # Connection settings
  client:
    max_idle_conns: 100
    max_idle_conns_per_host: 10
    idle_conn_timeout: "90s"
    dial_timeout: "30s"
    request_timeout: "30s"
    keep_alive: "30s"

# Database Performance Configuration
database:
  # Connection pool settings
  connection_pool:
    max_open_conns: 25
    max_idle_conns: 10
    conn_max_lifetime: "5m"
    conn_max_idle_time: "2m"
    
  # Query optimization
  queries:
    # Query timeout settings
    default_timeout: "30s"
    slow_query_threshold: "1s"
    
    # Pagination settings
    default_page_size: 20
    max_page_size: 100
    use_cursor_pagination: false
    
    # Query caching
    cache:
      enabled: true
      default_ttl: "5m"
      max_size: 1000
      cleanup_period: "10m"
  
  # Index optimization
  indexes:
    analysis_enabled: true
    suggestion_threshold: 10  # Minimum query count before suggesting index
    
# Caching Configuration
cache:
  # In-memory cache settings
  memory:
    enabled: true
    default_ttl: "5m"
    max_size: 1000
    cleanup_period: "10m"
    
  # Redis cache settings
  redis:
    enabled: true
    pool_size: 20
    min_idle_conns: 5
    dial_timeout: "5s"
    read_timeout: "3s"
    write_timeout: "3s"
    pool_timeout: "4s"
    idle_timeout: "5m"
    idle_check_frequency: "1m"
    max_retries: 3
    
    # Cache strategies
    strategies:
      user_data:
        ttl: "15m"
        prefix: "user:"
      session_data:
        ttl: "30m"
        prefix: "session:"
      query_results:
        ttl: "5m"
        prefix: "query:"

# Worker Pool Configuration
workers:
  # CPU-intensive task processing
  cpu_pool:
    enabled: true
    worker_count: 8  # Number of CPU cores
    queue_size: 1000
    task_timeout: "30s"
    
  # I/O intensive task processing
  io_pool:
    enabled: true
    worker_count: 20
    queue_size: 2000
    task_timeout: "60s"
    
  # Background job processing
  background_pool:
    enabled: true
    worker_count: 4
    queue_size: 500
    task_timeout: "300s"

# Batch Processing Configuration
batch:
  # Database batch operations
  database:
    enabled: true
    batch_size: 100
    flush_timeout: "5s"
    max_retries: 3
    
  # API request batching
  api:
    enabled: true
    batch_size: 50
    flush_timeout: "1s"
    max_concurrent_batches: 10

# Monitoring and Metrics Configuration
monitoring:
  # Performance metrics collection
  metrics:
    enabled: true
    collection_interval: "10s"
    retention_period: "24h"
    
    # Metric types to collect
    types:
      - "memory"
      - "cpu"
      - "http"
      - "database"
      - "cache"
      - "gc"
    
  # Performance alerts
  alerts:
    enabled: true
    
    # Memory alerts
    memory:
      high_usage_threshold: 80  # Percentage
      gc_frequency_threshold: 10  # GC cycles per minute
      
    # HTTP alerts
    http:
      slow_request_threshold: "2s"
      error_rate_threshold: 5  # Percentage
      
    # Database alerts
    database:
      slow_query_threshold: "1s"
      connection_pool_threshold: 80  # Percentage of max connections
      
    # Cache alerts
    cache:
      low_hit_rate_threshold: 70  # Percentage
      high_eviction_rate_threshold: 100  # Evictions per minute

# Profiling Configuration
profiling:
  enabled: false  # Enable only for debugging
  
  # CPU profiling
  cpu:
    enabled: false
    duration: "30s"
    output_dir: "/tmp/profiles"
    
  # Memory profiling
  memory:
    enabled: false
    rate: 1
    output_dir: "/tmp/profiles"
    
  # Block profiling
  block:
    enabled: false
    rate: 1
    output_dir: "/tmp/profiles"
    
  # Mutex profiling
  mutex:
    enabled: false
    fraction: 1
    output_dir: "/tmp/profiles"

# Environment-specific overrides
environments:
  development:
    memory:
      gc:
        target_percent: 200  # Less aggressive GC in development
    http:
      compression:
        enabled: false  # Disable compression for easier debugging
    profiling:
      enabled: true  # Enable profiling in development
      
  staging:
    database:
      connection_pool:
        max_open_conns: 15  # Fewer connections in staging
    cache:
      memory:
        max_size: 500  # Smaller cache in staging
        
  production:
    memory:
      gc:
        target_percent: 50  # More aggressive GC in production
    http:
      compression:
        enabled: true
        level: 9  # Maximum compression in production
    monitoring:
      alerts:
        enabled: true
    profiling:
      enabled: false  # Disable profiling in production

# Feature flags for performance optimizations
features:
  # Experimental features
  experimental:
    adaptive_gc: false  # Adaptive garbage collection
    smart_caching: false  # AI-driven cache optimization
    predictive_scaling: false  # Predictive resource scaling
    
  # Stable features
  stable:
    response_compression: true
    query_caching: true
    connection_pooling: true
    object_pooling: true
    batch_processing: true
    performance_monitoring: true

# Performance testing configuration
testing:
  benchmarks:
    enabled: true
    duration: "30s"
    concurrent_users: 100
    
  load_tests:
    enabled: false  # Enable only when needed
    duration: "5m"
    ramp_up_time: "1m"
    max_users: 1000
    
  stress_tests:
    enabled: false  # Enable only when needed
    duration: "10m"
    max_users: 5000
