// ============================================================================
// COMPREHENSIVE INPUT VALIDATION SYSTEM
// ============================================================================

package validation

import (
	"errors"
	"fmt"
	"net/mail"
	"regexp"
	"strings"
	"time"
	"unicode"

	"github.com/google/uuid"
)

// ValidationError represents a validation error with field context
type ValidationError struct {
	Field   string `json:"field"`
	Message string `json:"message"`
	Code    string `json:"code"`
	Value   interface{} `json:"value,omitempty"`
}

func (e ValidationError) Error() string {
	return fmt.Sprintf("validation error on field '%s': %s", e.Field, e.Message)
}

// ValidationErrors represents multiple validation errors
type ValidationErrors []ValidationError

func (e ValidationErrors) Error() string {
	if len(e) == 0 {
		return "no validation errors"
	}
	if len(e) == 1 {
		return e[0].Error()
	}
	return fmt.Sprintf("%d validation errors occurred", len(e))
}

// HasErrors returns true if there are validation errors
func (e ValidationErrors) HasErrors() bool {
	return len(e) > 0
}

// Validator provides comprehensive input validation
type Validator struct {
	errors ValidationErrors
}

// New creates a new validator instance
func New() *Validator {
	return &Validator{
		errors: make(ValidationErrors, 0),
	}
}

// AddError adds a validation error
func (v *Validator) AddError(field, message, code string, value interface{}) {
	v.errors = append(v.errors, ValidationError{
		Field:   field,
		Message: message,
		Code:    code,
		Value:   value,
	})
}

// HasErrors returns true if there are validation errors
func (v *Validator) HasErrors() bool {
	return len(v.errors) > 0
}

// Errors returns all validation errors
func (v *Validator) Errors() ValidationErrors {
	return v.errors
}

// Clear clears all validation errors
func (v *Validator) Clear() {
	v.errors = make(ValidationErrors, 0)
}

// ============================================================================
// STRING VALIDATION
// ============================================================================

// Required validates that a string is not empty
func (v *Validator) Required(field, value string) *Validator {
	if strings.TrimSpace(value) == "" {
		v.AddError(field, "This field is required", "required", value)
	}
	return v
}

// MinLength validates minimum string length
func (v *Validator) MinLength(field, value string, min int) *Validator {
	if len(value) < min {
		v.AddError(field, fmt.Sprintf("Must be at least %d characters long", min), "min_length", value)
	}
	return v
}

// MaxLength validates maximum string length
func (v *Validator) MaxLength(field, value string, max int) *Validator {
	if len(value) > max {
		v.AddError(field, fmt.Sprintf("Must be no more than %d characters long", max), "max_length", value)
	}
	return v
}

// Email validates email format
func (v *Validator) Email(field, value string) *Validator {
	if value == "" {
		return v // Skip validation for empty values (use Required separately)
	}
	
	// Use Go's built-in email validation
	if _, err := mail.ParseAddress(value); err != nil {
		v.AddError(field, "Must be a valid email address", "invalid_email", value)
		return v
	}
	
	// Additional checks for security
	if len(value) > 254 { // RFC 5321 limit
		v.AddError(field, "Email address is too long", "email_too_long", value)
	}
	
	// Check for dangerous characters
	if containsDangerousChars(value) {
		v.AddError(field, "Email contains invalid characters", "invalid_characters", value)
	}
	
	return v
}

// Password validates password strength
func (v *Validator) Password(field, value string) *Validator {
	if value == "" {
		return v // Skip validation for empty values
	}
	
	if len(value) < 8 {
		v.AddError(field, "Password must be at least 8 characters long", "password_too_short", nil)
	}
	
	if len(value) > 128 {
		v.AddError(field, "Password must be no more than 128 characters long", "password_too_long", nil)
	}
	
	var hasUpper, hasLower, hasDigit, hasSpecial bool
	for _, char := range value {
		switch {
		case unicode.IsUpper(char):
			hasUpper = true
		case unicode.IsLower(char):
			hasLower = true
		case unicode.IsDigit(char):
			hasDigit = true
		case unicode.IsPunct(char) || unicode.IsSymbol(char):
			hasSpecial = true
		}
	}
	
	if !hasUpper {
		v.AddError(field, "Password must contain at least one uppercase letter", "password_no_upper", nil)
	}
	if !hasLower {
		v.AddError(field, "Password must contain at least one lowercase letter", "password_no_lower", nil)
	}
	if !hasDigit {
		v.AddError(field, "Password must contain at least one digit", "password_no_digit", nil)
	}
	if !hasSpecial {
		v.AddError(field, "Password must contain at least one special character", "password_no_special", nil)
	}
	
	// Check for common weak passwords
	if isCommonPassword(value) {
		v.AddError(field, "Password is too common, please choose a stronger password", "password_too_common", nil)
	}
	
	return v
}

// Username validates username format
func (v *Validator) Username(field, value string) *Validator {
	if value == "" {
		return v
	}
	
	// Username should be 3-30 characters, alphanumeric + underscore/hyphen
	usernameRegex := regexp.MustCompile(`^[a-zA-Z0-9_-]{3,30}$`)
	if !usernameRegex.MatchString(value) {
		v.AddError(field, "Username must be 3-30 characters long and contain only letters, numbers, underscores, and hyphens", "invalid_username", value)
	}
	
	// Check for reserved usernames
	if isReservedUsername(value) {
		v.AddError(field, "This username is reserved", "reserved_username", value)
	}
	
	return v
}

// Phone validates phone number format
func (v *Validator) Phone(field, value string) *Validator {
	if value == "" {
		return v
	}
	
	// Remove all non-digit characters except + at the beginning
	cleaned := regexp.MustCompile(`[^\d+]`).ReplaceAllString(value, "")
	
	// Basic validation - should start with + and have 10-15 digits
	phoneRegex := regexp.MustCompile(`^\+?[1-9]\d{9,14}$`)
	if !phoneRegex.MatchString(cleaned) {
		v.AddError(field, "Must be a valid phone number", "invalid_phone", value)
	}
	
	return v
}

// URL validates URL format
func (v *Validator) URL(field, value string) *Validator {
	if value == "" {
		return v
	}
	
	urlRegex := regexp.MustCompile(`^https?://[^\s/$.?#].[^\s]*$`)
	if !urlRegex.MatchString(value) {
		v.AddError(field, "Must be a valid URL", "invalid_url", value)
	}
	
	return v
}

// ============================================================================
// NUMERIC VALIDATION
// ============================================================================

// Min validates minimum numeric value
func (v *Validator) Min(field string, value, min float64) *Validator {
	if value < min {
		v.AddError(field, fmt.Sprintf("Must be at least %g", min), "min_value", value)
	}
	return v
}

// Max validates maximum numeric value
func (v *Validator) Max(field string, value, max float64) *Validator {
	if value > max {
		v.AddError(field, fmt.Sprintf("Must be no more than %g", max), "max_value", value)
	}
	return v
}

// Range validates numeric range
func (v *Validator) Range(field string, value, min, max float64) *Validator {
	if value < min || value > max {
		v.AddError(field, fmt.Sprintf("Must be between %g and %g", min, max), "out_of_range", value)
	}
	return v
}

// ============================================================================
// UUID VALIDATION
// ============================================================================

// UUID validates UUID format
func (v *Validator) UUID(field, value string) *Validator {
	if value == "" {
		return v
	}
	
	if _, err := uuid.Parse(value); err != nil {
		v.AddError(field, "Must be a valid UUID", "invalid_uuid", value)
	}
	
	return v
}

// ============================================================================
// DATE/TIME VALIDATION
// ============================================================================

// Date validates date format
func (v *Validator) Date(field, value, layout string) *Validator {
	if value == "" {
		return v
	}
	
	if _, err := time.Parse(layout, value); err != nil {
		v.AddError(field, "Must be a valid date", "invalid_date", value)
	}
	
	return v
}

// DateAfter validates that date is after specified date
func (v *Validator) DateAfter(field, value, after, layout string) *Validator {
	if value == "" {
		return v
	}
	
	valueTime, err := time.Parse(layout, value)
	if err != nil {
		v.AddError(field, "Must be a valid date", "invalid_date", value)
		return v
	}
	
	afterTime, err := time.Parse(layout, after)
	if err != nil {
		v.AddError(field, "Invalid comparison date", "invalid_comparison_date", after)
		return v
	}
	
	if !valueTime.After(afterTime) {
		v.AddError(field, fmt.Sprintf("Must be after %s", after), "date_not_after", value)
	}
	
	return v
}

// ============================================================================
// SECURITY VALIDATION
// ============================================================================

// NoSQL validates against NoSQL injection
func (v *Validator) NoSQL(field, value string) *Validator {
	if value == "" {
		return v
	}
	
	// Check for common NoSQL injection patterns
	dangerousPatterns := []string{
		"$where", "$ne", "$in", "$nin", "$gt", "$gte", "$lt", "$lte",
		"$regex", "$exists", "$type", "$mod", "$all", "$size",
		"javascript:", "eval(", "function(", "return",
	}
	
	lowerValue := strings.ToLower(value)
	for _, pattern := range dangerousPatterns {
		if strings.Contains(lowerValue, pattern) {
			v.AddError(field, "Contains potentially dangerous content", "security_violation", nil)
			break
		}
	}
	
	return v
}

// XSS validates against XSS attacks
func (v *Validator) XSS(field, value string) *Validator {
	if value == "" {
		return v
	}
	
	// Check for common XSS patterns
	xssPatterns := []string{
		"<script", "</script>", "javascript:", "onload=", "onerror=",
		"onclick=", "onmouseover=", "onfocus=", "onblur=", "onchange=",
		"onsubmit=", "onreset=", "onselect=", "onkeydown=", "onkeyup=",
		"onkeypress=", "onmousedown=", "onmouseup=", "onmousemove=",
		"onmouseout=", "ondblclick=", "oncontextmenu=",
	}
	
	lowerValue := strings.ToLower(value)
	for _, pattern := range xssPatterns {
		if strings.Contains(lowerValue, pattern) {
			v.AddError(field, "Contains potentially dangerous content", "security_violation", nil)
			break
		}
	}
	
	return v
}

// ============================================================================
// HELPER FUNCTIONS
// ============================================================================

// containsDangerousChars checks for dangerous characters
func containsDangerousChars(value string) bool {
	dangerousChars := []string{"<", ">", "\"", "'", "&", ";", "(", ")", "{", "}", "[", "]"}
	for _, char := range dangerousChars {
		if strings.Contains(value, char) {
			return true
		}
	}
	return false
}

// isCommonPassword checks against common weak passwords
func isCommonPassword(password string) bool {
	commonPasswords := []string{
		"password", "123456", "password123", "admin", "qwerty",
		"letmein", "welcome", "monkey", "dragon", "master",
		"123456789", "12345678", "12345", "1234567890",
	}
	
	lowerPassword := strings.ToLower(password)
	for _, common := range commonPasswords {
		if lowerPassword == common {
			return true
		}
	}
	return false
}

// isReservedUsername checks against reserved usernames
func isReservedUsername(username string) bool {
	reserved := []string{
		"admin", "administrator", "root", "system", "api", "www",
		"mail", "email", "support", "help", "info", "contact",
		"about", "privacy", "terms", "legal", "security",
		"null", "undefined", "true", "false",
	}
	
	lowerUsername := strings.ToLower(username)
	for _, res := range reserved {
		if lowerUsername == res {
			return true
		}
	}
	return false
}

// ============================================================================
// VALIDATION FUNCTIONS
// ============================================================================

// ValidateStruct validates a struct using reflection and tags
func ValidateStruct(s interface{}) ValidationErrors {
	// This would implement struct validation using reflection
	// For now, return empty errors
	return ValidationErrors{}
}

// SanitizeString removes dangerous characters from string
func SanitizeString(input string) string {
	// Remove null bytes
	input = strings.ReplaceAll(input, "\x00", "")
	
	// Trim whitespace
	input = strings.TrimSpace(input)
	
	// Remove control characters except tab, newline, and carriage return
	var result strings.Builder
	for _, r := range input {
		if r == '\t' || r == '\n' || r == '\r' || !unicode.IsControl(r) {
			result.WriteRune(r)
		}
	}
	
	return result.String()
}

// ValidateAndSanitize validates and sanitizes input
func ValidateAndSanitize(field, value string, validators ...func(*Validator, string, string) *Validator) (string, error) {
	// Sanitize first
	sanitized := SanitizeString(value)
	
	// Then validate
	v := New()
	for _, validator := range validators {
		validator(v, field, sanitized)
	}
	
	if v.HasErrors() {
		return sanitized, v.Errors()
	}
	
	return sanitized, nil
}
