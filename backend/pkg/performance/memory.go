// ============================================================================
// MEMORY AND CPU PERFORMANCE OPTIMIZATION
// ============================================================================

package performance

import (
	"context"
	"fmt"
	"runtime"
	"runtime/debug"
	"sync"
	"sync/atomic"
	"time"

	"go.opentelemetry.io/otel"
	"go.opentelemetry.io/otel/attribute"
	"go.opentelemetry.io/otel/metric"
)

var memoryTracer = otel.Tracer("goreal-backend/performance/memory")

// ============================================================================
// MEMORY POOL MANAGEMENT
// ============================================================================

// MemoryPool provides efficient memory allocation and reuse
type MemoryPool struct {
	pools map[int]*sync.Pool
	mu    sync.RWMutex
}

// NewMemoryPool creates a new memory pool
func NewMemoryPool() *MemoryPool {
	return &MemoryPool{
		pools: make(map[int]*sync.Pool),
	}
}

// GetBuffer gets a buffer of the specified size from the pool
func (mp *MemoryPool) GetBuffer(size int) []byte {
	mp.mu.RLock()
	pool, exists := mp.pools[size]
	mp.mu.RUnlock()

	if !exists {
		mp.mu.Lock()
		// Double-check after acquiring write lock
		if pool, exists = mp.pools[size]; !exists {
			pool = &sync.Pool{
				New: func() interface{} {
					return make([]byte, size)
				},
			}
			mp.pools[size] = pool
		}
		mp.mu.Unlock()
	}

	return pool.Get().([]byte)
}

// PutBuffer returns a buffer to the pool
func (mp *MemoryPool) PutBuffer(buf []byte) {
	size := cap(buf)
	mp.mu.RLock()
	pool, exists := mp.pools[size]
	mp.mu.RUnlock()

	if exists {
		// Clear the buffer before returning to pool
		buf = buf[:0]
		pool.Put(buf)
	}
}

// ============================================================================
// OBJECT POOLING
// ============================================================================

// ObjectPool provides generic object pooling
type ObjectPool[T any] struct {
	pool    sync.Pool
	newFunc func() T
	resetFunc func(T)
}

// NewObjectPool creates a new object pool
func NewObjectPool[T any](newFunc func() T, resetFunc func(T)) *ObjectPool[T] {
	return &ObjectPool[T]{
		pool: sync.Pool{
			New: func() interface{} {
				return newFunc()
			},
		},
		newFunc:   newFunc,
		resetFunc: resetFunc,
	}
}

// Get retrieves an object from the pool
func (op *ObjectPool[T]) Get() T {
	return op.pool.Get().(T)
}

// Put returns an object to the pool
func (op *ObjectPool[T]) Put(obj T) {
	if op.resetFunc != nil {
		op.resetFunc(obj)
	}
	op.pool.Put(obj)
}

// ============================================================================
// MEMORY MONITORING
// ============================================================================

// MemoryMonitor tracks memory usage and performance
type MemoryMonitor struct {
	meter           metric.Meter
	memoryUsage     metric.Int64ObservableGauge
	gcDuration      metric.Float64Histogram
	allocations     metric.Int64Counter
	deallocations   metric.Int64Counter
	poolHits        metric.Int64Counter
	poolMisses      metric.Int64Counter
	
	// Internal tracking
	totalAllocations   int64
	totalDeallocations int64
	poolHitCount       int64
	poolMissCount      int64
}

// NewMemoryMonitor creates a new memory monitor
func NewMemoryMonitor() (*MemoryMonitor, error) {
	meter := otel.Meter("goreal-backend/memory")

	memoryUsage, err := meter.Int64ObservableGauge(
		"memory_usage_bytes",
		metric.WithDescription("Current memory usage in bytes"),
		metric.WithUnit("By"),
	)
	if err != nil {
		return nil, fmt.Errorf("failed to create memory usage gauge: %w", err)
	}

	gcDuration, err := meter.Float64Histogram(
		"gc_duration_seconds",
		metric.WithDescription("Garbage collection duration in seconds"),
		metric.WithUnit("s"),
	)
	if err != nil {
		return nil, fmt.Errorf("failed to create GC duration histogram: %w", err)
	}

	allocations, err := meter.Int64Counter(
		"memory_allocations_total",
		metric.WithDescription("Total number of memory allocations"),
	)
	if err != nil {
		return nil, fmt.Errorf("failed to create allocations counter: %w", err)
	}

	deallocations, err := meter.Int64Counter(
		"memory_deallocations_total",
		metric.WithDescription("Total number of memory deallocations"),
	)
	if err != nil {
		return nil, fmt.Errorf("failed to create deallocations counter: %w", err)
	}

	poolHits, err := meter.Int64Counter(
		"memory_pool_hits_total",
		metric.WithDescription("Total number of memory pool hits"),
	)
	if err != nil {
		return nil, fmt.Errorf("failed to create pool hits counter: %w", err)
	}

	poolMisses, err := meter.Int64Counter(
		"memory_pool_misses_total",
		metric.WithDescription("Total number of memory pool misses"),
	)
	if err != nil {
		return nil, fmt.Errorf("failed to create pool misses counter: %w", err)
	}

	monitor := &MemoryMonitor{
		meter:         meter,
		memoryUsage:   memoryUsage,
		gcDuration:    gcDuration,
		allocations:   allocations,
		deallocations: deallocations,
		poolHits:      poolHits,
		poolMisses:    poolMisses,
	}

	// Register memory usage callback
	_, err = meter.RegisterCallback(monitor.recordMemoryUsage, memoryUsage)
	if err != nil {
		return nil, fmt.Errorf("failed to register memory usage callback: %w", err)
	}

	return monitor, nil
}

// recordMemoryUsage records current memory usage
func (mm *MemoryMonitor) recordMemoryUsage(ctx context.Context, observer metric.Observer) error {
	var m runtime.MemStats
	runtime.ReadMemStats(&m)

	observer.ObserveInt64(mm.memoryUsage, int64(m.Alloc),
		metric.WithAttributes(attribute.String("type", "heap_alloc")))
	observer.ObserveInt64(mm.memoryUsage, int64(m.Sys),
		metric.WithAttributes(attribute.String("type", "sys")))
	observer.ObserveInt64(mm.memoryUsage, int64(m.HeapInuse),
		metric.WithAttributes(attribute.String("type", "heap_inuse")))
	observer.ObserveInt64(mm.memoryUsage, int64(m.StackInuse),
		metric.WithAttributes(attribute.String("type", "stack_inuse")))

	return nil
}

// TrackAllocation tracks a memory allocation
func (mm *MemoryMonitor) TrackAllocation(ctx context.Context, size int64) {
	atomic.AddInt64(&mm.totalAllocations, 1)
	mm.allocations.Add(ctx, 1, metric.WithAttributes(
		attribute.Int64("size", size),
	))
}

// TrackDeallocation tracks a memory deallocation
func (mm *MemoryMonitor) TrackDeallocation(ctx context.Context, size int64) {
	atomic.AddInt64(&mm.totalDeallocations, 1)
	mm.deallocations.Add(ctx, 1, metric.WithAttributes(
		attribute.Int64("size", size),
	))
}

// TrackPoolHit tracks a memory pool hit
func (mm *MemoryMonitor) TrackPoolHit(ctx context.Context, poolType string) {
	atomic.AddInt64(&mm.poolHitCount, 1)
	mm.poolHits.Add(ctx, 1, metric.WithAttributes(
		attribute.String("pool_type", poolType),
	))
}

// TrackPoolMiss tracks a memory pool miss
func (mm *MemoryMonitor) TrackPoolMiss(ctx context.Context, poolType string) {
	atomic.AddInt64(&mm.poolMissCount, 1)
	mm.poolMisses.Add(ctx, 1, metric.WithAttributes(
		attribute.String("pool_type", poolType),
	))
}

// TrackGC tracks garbage collection duration
func (mm *MemoryMonitor) TrackGC(ctx context.Context, duration time.Duration) {
	mm.gcDuration.Record(ctx, duration.Seconds())
}

// GetMemoryStats returns current memory statistics
func (mm *MemoryMonitor) GetMemoryStats() map[string]interface{} {
	var m runtime.MemStats
	runtime.ReadMemStats(&m)

	poolHitRate := float64(0)
	totalPoolRequests := atomic.LoadInt64(&mm.poolHitCount) + atomic.LoadInt64(&mm.poolMissCount)
	if totalPoolRequests > 0 {
		poolHitRate = float64(atomic.LoadInt64(&mm.poolHitCount)) / float64(totalPoolRequests) * 100
	}

	return map[string]interface{}{
		"heap_alloc":         m.Alloc,
		"heap_sys":           m.HeapSys,
		"heap_inuse":         m.HeapInuse,
		"heap_released":      m.HeapReleased,
		"stack_inuse":        m.StackInuse,
		"stack_sys":          m.StackSys,
		"num_gc":             m.NumGC,
		"gc_cpu_fraction":    m.GCCPUFraction,
		"total_allocations":  atomic.LoadInt64(&mm.totalAllocations),
		"total_deallocations": atomic.LoadInt64(&mm.totalDeallocations),
		"pool_hit_rate":      poolHitRate,
		"goroutines":         runtime.NumGoroutine(),
	}
}

// ============================================================================
// CPU OPTIMIZATION
// ============================================================================

// CPUMonitor tracks CPU usage and performance
type CPUMonitor struct {
	meter          metric.Meter
	cpuUsage       metric.Float64ObservableGauge
	goroutineCount metric.Int64ObservableGauge
	
	lastCPUTime    time.Time
	lastCPUUsage   time.Duration
}

// NewCPUMonitor creates a new CPU monitor
func NewCPUMonitor() (*CPUMonitor, error) {
	meter := otel.Meter("goreal-backend/cpu")

	cpuUsage, err := meter.Float64ObservableGauge(
		"cpu_usage_percent",
		metric.WithDescription("CPU usage percentage"),
		metric.WithUnit("%"),
	)
	if err != nil {
		return nil, fmt.Errorf("failed to create CPU usage gauge: %w", err)
	}

	goroutineCount, err := meter.Int64ObservableGauge(
		"goroutine_count",
		metric.WithDescription("Number of active goroutines"),
	)
	if err != nil {
		return nil, fmt.Errorf("failed to create goroutine count gauge: %w", err)
	}

	monitor := &CPUMonitor{
		meter:          meter,
		cpuUsage:       cpuUsage,
		goroutineCount: goroutineCount,
		lastCPUTime:    time.Now(),
	}

	// Register callbacks
	_, err = meter.RegisterCallback(monitor.recordCPUMetrics, cpuUsage, goroutineCount)
	if err != nil {
		return nil, fmt.Errorf("failed to register CPU metrics callback: %w", err)
	}

	return monitor, nil
}

// recordCPUMetrics records CPU-related metrics
func (cm *CPUMonitor) recordCPUMetrics(ctx context.Context, observer metric.Observer) error {
	// Record goroutine count
	observer.ObserveInt64(cm.goroutineCount, int64(runtime.NumGoroutine()))

	// Calculate CPU usage (simplified approach)
	now := time.Now()
	if !cm.lastCPUTime.IsZero() {
		elapsed := now.Sub(cm.lastCPUTime)
		if elapsed > 0 {
			// This is a simplified CPU usage calculation
			// In production, you might want to use more sophisticated methods
			var m runtime.MemStats
			runtime.ReadMemStats(&m)
			
			cpuPercent := float64(m.GCCPUFraction) * 100
			observer.ObserveFloat64(cm.cpuUsage, cpuPercent)
		}
	}
	cm.lastCPUTime = now

	return nil
}

// ============================================================================
// GARBAGE COLLECTION OPTIMIZATION
// ============================================================================

// GCOptimizer provides garbage collection optimization
type GCOptimizer struct {
	monitor       *MemoryMonitor
	gcPercent     int
	maxHeapSize   int64
	lastGCTime    time.Time
	gcStats       []GCStats
	mu            sync.RWMutex
}

// GCStats represents garbage collection statistics
type GCStats struct {
	Timestamp    time.Time     `json:"timestamp"`
	Duration     time.Duration `json:"duration"`
	HeapBefore   uint64        `json:"heap_before"`
	HeapAfter    uint64        `json:"heap_after"`
	Freed        uint64        `json:"freed"`
	GCPercent    int           `json:"gc_percent"`
}

// NewGCOptimizer creates a new GC optimizer
func NewGCOptimizer(monitor *MemoryMonitor) *GCOptimizer {
	return &GCOptimizer{
		monitor:     monitor,
		gcPercent:   100, // Default GC target percentage
		maxHeapSize: 1 << 30, // 1GB default max heap
		gcStats:     make([]GCStats, 0),
	}
}

// OptimizeGC optimizes garbage collection settings
func (gco *GCOptimizer) OptimizeGC() {
	var m runtime.MemStats
	runtime.ReadMemStats(&m)

	// Adjust GC target percentage based on heap usage
	heapUsagePercent := float64(m.HeapInuse) / float64(gco.maxHeapSize) * 100

	newGCPercent := gco.gcPercent
	switch {
	case heapUsagePercent > 80:
		// High memory usage - more aggressive GC
		newGCPercent = 50
	case heapUsagePercent > 60:
		// Medium memory usage - moderate GC
		newGCPercent = 75
	case heapUsagePercent < 30:
		// Low memory usage - less aggressive GC
		newGCPercent = 200
	default:
		// Normal memory usage - default GC
		newGCPercent = 100
	}

	if newGCPercent != gco.gcPercent {
		debug.SetGCPercent(newGCPercent)
		gco.gcPercent = newGCPercent
	}
}

// TrackGC tracks a garbage collection cycle
func (gco *GCOptimizer) TrackGC(ctx context.Context) {
	var m runtime.MemStats
	runtime.ReadMemStats(&m)

	now := time.Now()
	duration := now.Sub(gco.lastGCTime)

	if !gco.lastGCTime.IsZero() && gco.monitor != nil {
		gco.monitor.TrackGC(ctx, duration)
	}

	// Record GC stats
	gco.mu.Lock()
	gcStat := GCStats{
		Timestamp: now,
		Duration:  duration,
		GCPercent: gco.gcPercent,
	}
	gco.gcStats = append(gco.gcStats, gcStat)
	
	// Keep only last 100 GC cycles
	if len(gco.gcStats) > 100 {
		gco.gcStats = gco.gcStats[1:]
	}
	gco.mu.Unlock()

	gco.lastGCTime = now
}

// GetGCStats returns garbage collection statistics
func (gco *GCOptimizer) GetGCStats() []GCStats {
	gco.mu.RLock()
	defer gco.mu.RUnlock()
	
	// Return a copy to avoid race conditions
	stats := make([]GCStats, len(gco.gcStats))
	copy(stats, gco.gcStats)
	return stats
}

// ============================================================================
// WORKER POOL FOR CPU-INTENSIVE TASKS
// ============================================================================

// WorkerPool provides efficient task processing
type WorkerPool struct {
	workers    int
	taskQueue  chan Task
	resultChan chan TaskResult
	quit       chan bool
	wg         sync.WaitGroup
}

// Task represents a unit of work
type Task struct {
	ID       string
	Data     interface{}
	Function func(interface{}) (interface{}, error)
}

// TaskResult represents the result of a task
type TaskResult struct {
	TaskID string
	Result interface{}
	Error  error
}

// NewWorkerPool creates a new worker pool
func NewWorkerPool(workers int, queueSize int) *WorkerPool {
	return &WorkerPool{
		workers:    workers,
		taskQueue:  make(chan Task, queueSize),
		resultChan: make(chan TaskResult, queueSize),
		quit:       make(chan bool),
	}
}

// Start starts the worker pool
func (wp *WorkerPool) Start() {
	for i := 0; i < wp.workers; i++ {
		wp.wg.Add(1)
		go wp.worker(i)
	}
}

// Stop stops the worker pool
func (wp *WorkerPool) Stop() {
	close(wp.quit)
	wp.wg.Wait()
	close(wp.taskQueue)
	close(wp.resultChan)
}

// Submit submits a task to the worker pool
func (wp *WorkerPool) Submit(task Task) {
	wp.taskQueue <- task
}

// Results returns the result channel
func (wp *WorkerPool) Results() <-chan TaskResult {
	return wp.resultChan
}

// worker processes tasks from the queue
func (wp *WorkerPool) worker(id int) {
	defer wp.wg.Done()
	
	for {
		select {
		case task := <-wp.taskQueue:
			result, err := task.Function(task.Data)
			wp.resultChan <- TaskResult{
				TaskID: task.ID,
				Result: result,
				Error:  err,
			}
		case <-wp.quit:
			return
		}
	}
}

// ============================================================================
// PERFORMANCE PROFILER
// ============================================================================

// Profiler provides runtime performance profiling
type Profiler struct {
	cpuProfile    bool
	memProfile    bool
	blockProfile  bool
	mutexProfile  bool
	profileDir    string
}

// NewProfiler creates a new profiler
func NewProfiler(profileDir string) *Profiler {
	return &Profiler{
		profileDir: profileDir,
	}
}

// EnableCPUProfile enables CPU profiling
func (p *Profiler) EnableCPUProfile() {
	p.cpuProfile = true
}

// EnableMemoryProfile enables memory profiling
func (p *Profiler) EnableMemoryProfile() {
	p.memProfile = true
	runtime.MemProfileRate = 1
}

// EnableBlockProfile enables block profiling
func (p *Profiler) EnableBlockProfile() {
	p.blockProfile = true
	runtime.SetBlockProfileRate(1)
}

// EnableMutexProfile enables mutex profiling
func (p *Profiler) EnableMutexProfile() {
	p.mutexProfile = true
	runtime.SetMutexProfileFraction(1)
}

// GetRuntimeStats returns current runtime statistics
func (p *Profiler) GetRuntimeStats() map[string]interface{} {
	var m runtime.MemStats
	runtime.ReadMemStats(&m)

	return map[string]interface{}{
		"goroutines":     runtime.NumGoroutine(),
		"cgo_calls":      runtime.NumCgoCall(),
		"heap_objects":   m.HeapObjects,
		"gc_cycles":      m.NumGC,
		"gc_pause_total": time.Duration(m.PauseTotalNs),
		"next_gc":        m.NextGC,
		"last_gc":        time.Unix(0, int64(m.LastGC)),
	}
}
