// ============================================================================
// HTTP PERFORMANCE OPTIMIZATION
// ============================================================================

package performance

import (
	"compress/gzip"
	"context"
	"fmt"
	"io"
	"net/http"
	"strconv"
	"strings"
	"sync"
	"time"

	"go.opentelemetry.io/otel"
	"go.opentelemetry.io/otel/attribute"
	"go.opentelemetry.io/otel/metric"
)

var httpTracer = otel.Tracer("goreal-backend/performance/http")

// ============================================================================
// RESPONSE COMPRESSION MIDDLEWARE
// ============================================================================

// CompressionConfig holds compression configuration
type CompressionConfig struct {
	Level            int      `json:"level"`              // Compression level (1-9)
	MinSize          int      `json:"min_size"`           // Minimum response size to compress
	CompressibleTypes []string `json:"compressible_types"` // MIME types to compress
}

// DefaultCompressionConfig returns default compression settings
func DefaultCompressionConfig() CompressionConfig {
	return CompressionConfig{
		Level:   gzip.DefaultCompression,
		MinSize: 1024, // 1KB minimum
		CompressibleTypes: []string{
			"text/html",
			"text/css",
			"text/javascript",
			"text/plain",
			"application/json",
			"application/javascript",
			"application/xml",
			"application/rss+xml",
			"application/atom+xml",
			"image/svg+xml",
		},
	}
}

// CompressionMiddleware provides response compression
func CompressionMiddleware(config CompressionConfig) func(http.Handler) http.Handler {
	return func(next http.Handler) http.Handler {
		return http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
			// Check if client accepts gzip
			if !strings.Contains(r.Header.Get("Accept-Encoding"), "gzip") {
				next.ServeHTTP(w, r)
				return
			}

			// Wrap response writer with compression
			gzw := &gzipResponseWriter{
				ResponseWriter: w,
				config:         config,
			}
			defer gzw.Close()

			// Set compression header
			w.Header().Set("Content-Encoding", "gzip")
			w.Header().Set("Vary", "Accept-Encoding")

			next.ServeHTTP(gzw, r)
		})
	}
}

// gzipResponseWriter wraps http.ResponseWriter with gzip compression
type gzipResponseWriter struct {
	http.ResponseWriter
	gzipWriter *gzip.Writer
	config     CompressionConfig
	written    bool
}

func (gzw *gzipResponseWriter) Write(data []byte) (int, error) {
	if !gzw.written {
		gzw.written = true
		
		// Check if response should be compressed
		if !gzw.shouldCompress(data) {
			// Remove compression headers and write directly
			gzw.Header().Del("Content-Encoding")
			gzw.Header().Del("Vary")
			return gzw.ResponseWriter.Write(data)
		}

		// Initialize gzip writer
		gzw.gzipWriter = gzip.NewWriter(gzw.ResponseWriter)
	}

	if gzw.gzipWriter != nil {
		return gzw.gzipWriter.Write(data)
	}
	return gzw.ResponseWriter.Write(data)
}

func (gzw *gzipResponseWriter) shouldCompress(data []byte) bool {
	// Check minimum size
	if len(data) < gzw.config.MinSize {
		return false
	}

	// Check content type
	contentType := gzw.Header().Get("Content-Type")
	if contentType == "" {
		contentType = http.DetectContentType(data)
	}

	for _, compressibleType := range gzw.config.CompressibleTypes {
		if strings.Contains(contentType, compressibleType) {
			return true
		}
	}

	return false
}

func (gzw *gzipResponseWriter) Close() error {
	if gzw.gzipWriter != nil {
		return gzw.gzipWriter.Close()
	}
	return nil
}

// ============================================================================
// HTTP CACHING MIDDLEWARE
// ============================================================================

// CacheControlConfig holds cache control configuration
type CacheControlConfig struct {
	StaticMaxAge  int `json:"static_max_age"`  // Cache duration for static assets (seconds)
	APIMaxAge     int `json:"api_max_age"`     // Cache duration for API responses (seconds)
	EnableETags   bool `json:"enable_etags"`   // Enable ETag generation
	EnableVary    bool `json:"enable_vary"`    // Enable Vary header
}

// DefaultCacheControlConfig returns default cache control settings
func DefaultCacheControlConfig() CacheControlConfig {
	return CacheControlConfig{
		StaticMaxAge: 31536000, // 1 year for static assets
		APIMaxAge:    300,      // 5 minutes for API responses
		EnableETags:  true,
		EnableVary:   true,
	}
}

// CacheControlMiddleware adds appropriate cache headers
func CacheControlMiddleware(config CacheControlConfig) func(http.Handler) http.Handler {
	return func(next http.Handler) http.Handler {
		return http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
			// Determine cache duration based on path
			maxAge := config.APIMaxAge
			if isStaticAsset(r.URL.Path) {
				maxAge = config.StaticMaxAge
			}

			// Set cache headers
			if maxAge > 0 {
				w.Header().Set("Cache-Control", fmt.Sprintf("public, max-age=%d", maxAge))
			} else {
				w.Header().Set("Cache-Control", "no-cache, no-store, must-revalidate")
			}

			// Add Vary header for content negotiation
			if config.EnableVary {
				w.Header().Set("Vary", "Accept-Encoding, Accept")
			}

			next.ServeHTTP(w, r)
		})
	}
}

// isStaticAsset checks if the path is for a static asset
func isStaticAsset(path string) bool {
	staticExtensions := []string{".css", ".js", ".png", ".jpg", ".jpeg", ".gif", ".svg", ".ico", ".woff", ".woff2", ".ttf", ".eot"}
	for _, ext := range staticExtensions {
		if strings.HasSuffix(path, ext) {
			return true
		}
	}
	return false
}

// ============================================================================
// HTTP METRICS AND MONITORING
// ============================================================================

// HTTPMetrics tracks HTTP performance metrics
type HTTPMetrics struct {
	meter            metric.Meter
	requestDuration  metric.Float64Histogram
	requestCount     metric.Int64Counter
	responseSize     metric.Int64Histogram
	activeRequests   metric.Int64UpDownCounter
}

// NewHTTPMetrics creates a new HTTP metrics tracker
func NewHTTPMetrics() (*HTTPMetrics, error) {
	meter := otel.Meter("goreal-backend/http")

	requestDuration, err := meter.Float64Histogram(
		"http_request_duration_seconds",
		metric.WithDescription("HTTP request duration in seconds"),
		metric.WithUnit("s"),
	)
	if err != nil {
		return nil, fmt.Errorf("failed to create request duration histogram: %w", err)
	}

	requestCount, err := meter.Int64Counter(
		"http_requests_total",
		metric.WithDescription("Total number of HTTP requests"),
	)
	if err != nil {
		return nil, fmt.Errorf("failed to create request counter: %w", err)
	}

	responseSize, err := meter.Int64Histogram(
		"http_response_size_bytes",
		metric.WithDescription("HTTP response size in bytes"),
		metric.WithUnit("By"),
	)
	if err != nil {
		return nil, fmt.Errorf("failed to create response size histogram: %w", err)
	}

	activeRequests, err := meter.Int64UpDownCounter(
		"http_active_requests",
		metric.WithDescription("Number of active HTTP requests"),
	)
	if err != nil {
		return nil, fmt.Errorf("failed to create active requests counter: %w", err)
	}

	return &HTTPMetrics{
		meter:            meter,
		requestDuration:  requestDuration,
		requestCount:     requestCount,
		responseSize:     responseSize,
		activeRequests:   activeRequests,
	}, nil
}

// MetricsMiddleware tracks HTTP request metrics
func (hm *HTTPMetrics) MetricsMiddleware() func(http.Handler) http.Handler {
	return func(next http.Handler) http.Handler {
		return http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
			ctx, span := httpTracer.Start(r.Context(), "HTTPMetrics.MetricsMiddleware")
			defer span.End()

			start := time.Now()
			
			// Increment active requests
			hm.activeRequests.Add(ctx, 1)
			defer hm.activeRequests.Add(ctx, -1)

			// Wrap response writer to capture response size and status
			mrw := &metricsResponseWriter{
				ResponseWriter: w,
				statusCode:     200,
			}

			// Process request
			next.ServeHTTP(mrw, r.WithContext(ctx))

			// Calculate duration
			duration := time.Since(start)

			// Record metrics
			labels := metric.WithAttributes(
				attribute.String("method", r.Method),
				attribute.String("path", r.URL.Path),
				attribute.Int("status_code", mrw.statusCode),
			)

			hm.requestDuration.Record(ctx, duration.Seconds(), labels)
			hm.requestCount.Add(ctx, 1, labels)
			hm.responseSize.Record(ctx, int64(mrw.bytesWritten), labels)

			// Set span attributes
			span.SetAttributes(
				attribute.String("http.method", r.Method),
				attribute.String("http.url", r.URL.String()),
				attribute.Int("http.status_code", mrw.statusCode),
				attribute.Int64("http.response_size", int64(mrw.bytesWritten)),
				attribute.Float64("http.duration_seconds", duration.Seconds()),
			)
		})
	}
}

// metricsResponseWriter wraps http.ResponseWriter to capture metrics
type metricsResponseWriter struct {
	http.ResponseWriter
	statusCode   int
	bytesWritten int
}

func (mrw *metricsResponseWriter) WriteHeader(statusCode int) {
	mrw.statusCode = statusCode
	mrw.ResponseWriter.WriteHeader(statusCode)
}

func (mrw *metricsResponseWriter) Write(data []byte) (int, error) {
	n, err := mrw.ResponseWriter.Write(data)
	mrw.bytesWritten += n
	return n, err
}

// ============================================================================
// CONNECTION POOLING AND KEEP-ALIVE
// ============================================================================

// HTTPClientConfig holds HTTP client configuration for optimal performance
type HTTPClientConfig struct {
	MaxIdleConns        int           `json:"max_idle_conns"`
	MaxIdleConnsPerHost int           `json:"max_idle_conns_per_host"`
	IdleConnTimeout     time.Duration `json:"idle_conn_timeout"`
	DialTimeout         time.Duration `json:"dial_timeout"`
	RequestTimeout      time.Duration `json:"request_timeout"`
	KeepAlive           time.Duration `json:"keep_alive"`
}

// DefaultHTTPClientConfig returns optimized HTTP client settings
func DefaultHTTPClientConfig() HTTPClientConfig {
	return HTTPClientConfig{
		MaxIdleConns:        100,
		MaxIdleConnsPerHost: 10,
		IdleConnTimeout:     90 * time.Second,
		DialTimeout:         30 * time.Second,
		RequestTimeout:      30 * time.Second,
		KeepAlive:           30 * time.Second,
	}
}

// ============================================================================
// REQUEST BATCHING AND DEBOUNCING
// ============================================================================

// BatchProcessor handles request batching for improved performance
type BatchProcessor struct {
	mu           sync.Mutex
	batches      map[string]*batch
	batchSize    int
	flushTimeout time.Duration
}

type batch struct {
	requests []interface{}
	timer    *time.Timer
	callback func([]interface{}) error
}

// NewBatchProcessor creates a new batch processor
func NewBatchProcessor(batchSize int, flushTimeout time.Duration) *BatchProcessor {
	return &BatchProcessor{
		batches:      make(map[string]*batch),
		batchSize:    batchSize,
		flushTimeout: flushTimeout,
	}
}

// AddToBatch adds a request to a batch
func (bp *BatchProcessor) AddToBatch(batchKey string, request interface{}, callback func([]interface{}) error) {
	bp.mu.Lock()
	defer bp.mu.Unlock()

	b, exists := bp.batches[batchKey]
	if !exists {
		b = &batch{
			requests: make([]interface{}, 0, bp.batchSize),
			callback: callback,
		}
		bp.batches[batchKey] = b
	}

	b.requests = append(b.requests, request)

	// Flush if batch is full
	if len(b.requests) >= bp.batchSize {
		bp.flushBatch(batchKey, b)
		return
	}

	// Set timer for timeout flush
	if b.timer == nil {
		b.timer = time.AfterFunc(bp.flushTimeout, func() {
			bp.mu.Lock()
			defer bp.mu.Unlock()
			if batch, exists := bp.batches[batchKey]; exists {
				bp.flushBatch(batchKey, batch)
			}
		})
	}
}

// flushBatch processes and removes a batch
func (bp *BatchProcessor) flushBatch(batchKey string, b *batch) {
	if b.timer != nil {
		b.timer.Stop()
	}

	// Process batch
	if len(b.requests) > 0 {
		go b.callback(b.requests)
	}

	// Remove batch
	delete(bp.batches, batchKey)
}

// ============================================================================
// RESPONSE STREAMING
// ============================================================================

// StreamingResponse provides efficient response streaming
type StreamingResponse struct {
	writer http.ResponseWriter
	flusher http.Flusher
}

// NewStreamingResponse creates a new streaming response
func NewStreamingResponse(w http.ResponseWriter) *StreamingResponse {
	flusher, ok := w.(http.Flusher)
	if !ok {
		flusher = nil
	}

	return &StreamingResponse{
		writer:  w,
		flusher: flusher,
	}
}

// WriteChunk writes a chunk of data and flushes if possible
func (sr *StreamingResponse) WriteChunk(data []byte) error {
	_, err := sr.writer.Write(data)
	if err != nil {
		return err
	}

	if sr.flusher != nil {
		sr.flusher.Flush()
	}

	return nil
}

// WriteJSON writes JSON data as a chunk
func (sr *StreamingResponse) WriteJSON(data interface{}) error {
	// Implementation would serialize data to JSON and write
	// This is a placeholder for the actual JSON serialization
	return sr.WriteChunk([]byte(fmt.Sprintf("%v\n", data)))
}

// ============================================================================
// PERFORMANCE MONITORING
// ============================================================================

// PerformanceMonitor tracks overall HTTP performance
type PerformanceMonitor struct {
	mu                sync.RWMutex
	requestCounts     map[string]int64
	responseTimes     map[string][]time.Duration
	errorCounts       map[string]int64
	slowRequests      []SlowRequest
	startTime         time.Time
}

// SlowRequest represents a slow HTTP request
type SlowRequest struct {
	Method    string        `json:"method"`
	Path      string        `json:"path"`
	Duration  time.Duration `json:"duration"`
	Timestamp time.Time     `json:"timestamp"`
	UserAgent string        `json:"user_agent"`
	IP        string        `json:"ip"`
}

// NewPerformanceMonitor creates a new performance monitor
func NewPerformanceMonitor() *PerformanceMonitor {
	return &PerformanceMonitor{
		requestCounts: make(map[string]int64),
		responseTimes: make(map[string][]time.Duration),
		errorCounts:   make(map[string]int64),
		slowRequests:  make([]SlowRequest, 0),
		startTime:     time.Now(),
	}
}

// TrackRequest tracks an HTTP request
func (pm *PerformanceMonitor) TrackRequest(method, path string, duration time.Duration, statusCode int, userAgent, ip string) {
	pm.mu.Lock()
	defer pm.mu.Unlock()

	key := fmt.Sprintf("%s %s", method, path)
	
	// Track request count
	pm.requestCounts[key]++
	
	// Track response time
	pm.responseTimes[key] = append(pm.responseTimes[key], duration)
	
	// Track errors (4xx and 5xx)
	if statusCode >= 400 {
		pm.errorCounts[key]++
	}
	
	// Track slow requests (>2 seconds)
	if duration > 2*time.Second {
		slowReq := SlowRequest{
			Method:    method,
			Path:      path,
			Duration:  duration,
			Timestamp: time.Now(),
			UserAgent: userAgent,
			IP:        ip,
		}
		pm.slowRequests = append(pm.slowRequests, slowReq)
		
		// Keep only last 100 slow requests
		if len(pm.slowRequests) > 100 {
			pm.slowRequests = pm.slowRequests[1:]
		}
	}
}

// GetStats returns performance statistics
func (pm *PerformanceMonitor) GetStats() map[string]interface{} {
	pm.mu.RLock()
	defer pm.mu.RUnlock()

	// Calculate average response times
	avgResponseTimes := make(map[string]time.Duration)
	for endpoint, times := range pm.responseTimes {
		if len(times) > 0 {
			var total time.Duration
			for _, t := range times {
				total += t
			}
			avgResponseTimes[endpoint] = total / time.Duration(len(times))
		}
	}

	// Calculate total requests
	var totalRequests int64
	for _, count := range pm.requestCounts {
		totalRequests += count
	}

	// Calculate uptime
	uptime := time.Since(pm.startTime)

	return map[string]interface{}{
		"uptime":              uptime.String(),
		"total_requests":      totalRequests,
		"request_counts":      pm.requestCounts,
		"avg_response_times":  avgResponseTimes,
		"error_counts":        pm.errorCounts,
		"slow_requests":       pm.slowRequests,
		"requests_per_second": float64(totalRequests) / uptime.Seconds(),
	}
}
