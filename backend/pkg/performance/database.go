// ============================================================================
// DATABASE PERFORMANCE OPTIMIZATION
// ============================================================================

package performance

import (
	"context"
	"fmt"
	"sync"
	"time"

	"go.opentelemetry.io/otel"
	"go.opentelemetry.io/otel/attribute"
	"go.opentelemetry.io/otel/metric"
	"go.opentelemetry.io/otel/trace"
)

var dbTracer = otel.Tracer("goreal-backend/performance/database")

// ============================================================================
// DATABASE CONNECTION POOL OPTIMIZATION
// ============================================================================

// ConnectionPoolConfig holds database connection pool configuration
type ConnectionPoolConfig struct {
	MaxOpenConns    int           `json:"max_open_conns"`
	MaxIdleConns    int           `json:"max_idle_conns"`
	ConnMaxLifetime time.Duration `json:"conn_max_lifetime"`
	ConnMaxIdleTime time.Duration `json:"conn_max_idle_time"`
}

// DefaultConnectionPoolConfig returns optimized connection pool settings
func DefaultConnectionPoolConfig() ConnectionPoolConfig {
	return ConnectionPoolConfig{
		MaxOpenConns:    25,  // Limit concurrent connections
		MaxIdleConns:    10,  // Keep some connections idle for reuse
		ConnMaxLifetime: 5 * time.Minute,  // Rotate connections regularly
		ConnMaxIdleTime: 2 * time.Minute,  // Close idle connections
	}
}

// HighTrafficConnectionPoolConfig returns settings for high-traffic scenarios
func HighTrafficConnectionPoolConfig() ConnectionPoolConfig {
	return ConnectionPoolConfig{
		MaxOpenConns:    50,
		MaxIdleConns:    20,
		ConnMaxLifetime: 10 * time.Minute,
		ConnMaxIdleTime: 5 * time.Minute,
	}
}

// ============================================================================
// QUERY PERFORMANCE MONITORING
// ============================================================================

// QueryMetrics tracks database query performance
type QueryMetrics struct {
	mu              sync.RWMutex
	queryDurations  map[string][]time.Duration
	queryErrors     map[string]int
	slowQueries     []SlowQuery
	totalQueries    int64
	totalErrors     int64
	meter           metric.Meter
	durationHist    metric.Float64Histogram
	errorCounter    metric.Int64Counter
}

// SlowQuery represents a slow database query
type SlowQuery struct {
	Query     string        `json:"query"`
	Duration  time.Duration `json:"duration"`
	Timestamp time.Time     `json:"timestamp"`
	Table     string        `json:"table"`
	Operation string        `json:"operation"`
}

// NewQueryMetrics creates a new query metrics tracker
func NewQueryMetrics() (*QueryMetrics, error) {
	meter := otel.Meter("goreal-backend/database")
	
	durationHist, err := meter.Float64Histogram(
		"db_query_duration_seconds",
		metric.WithDescription("Database query duration in seconds"),
		metric.WithUnit("s"),
	)
	if err != nil {
		return nil, fmt.Errorf("failed to create duration histogram: %w", err)
	}

	errorCounter, err := meter.Int64Counter(
		"db_query_errors_total",
		metric.WithDescription("Total number of database query errors"),
	)
	if err != nil {
		return nil, fmt.Errorf("failed to create error counter: %w", err)
	}

	return &QueryMetrics{
		queryDurations: make(map[string][]time.Duration),
		queryErrors:    make(map[string]int),
		slowQueries:    make([]SlowQuery, 0),
		meter:          meter,
		durationHist:   durationHist,
		errorCounter:   errorCounter,
	}, nil
}

// TrackQuery tracks a database query execution
func (qm *QueryMetrics) TrackQuery(ctx context.Context, table, operation string, duration time.Duration, err error) {
	ctx, span := dbTracer.Start(ctx, "QueryMetrics.TrackQuery")
	defer span.End()

	qm.mu.Lock()
	defer qm.mu.Unlock()

	queryKey := fmt.Sprintf("%s.%s", table, operation)
	
	// Track duration
	qm.queryDurations[queryKey] = append(qm.queryDurations[queryKey], duration)
	qm.totalQueries++

	// Record metrics
	qm.durationHist.Record(ctx, duration.Seconds(),
		metric.WithAttributes(
			attribute.String("table", table),
			attribute.String("operation", operation),
		),
	)

	// Track errors
	if err != nil {
		qm.queryErrors[queryKey]++
		qm.totalErrors++
		qm.errorCounter.Add(ctx, 1,
			metric.WithAttributes(
				attribute.String("table", table),
				attribute.String("operation", operation),
				attribute.String("error", err.Error()),
			),
		)
	}

	// Track slow queries (>1 second)
	if duration > time.Second {
		slowQuery := SlowQuery{
			Query:     queryKey,
			Duration:  duration,
			Timestamp: time.Now(),
			Table:     table,
			Operation: operation,
		}
		qm.slowQueries = append(qm.slowQueries, slowQuery)
		
		// Keep only last 100 slow queries
		if len(qm.slowQueries) > 100 {
			qm.slowQueries = qm.slowQueries[1:]
		}
	}

	span.SetAttributes(
		attribute.String("db.table", table),
		attribute.String("db.operation", operation),
		attribute.Float64("db.duration_seconds", duration.Seconds()),
		attribute.Bool("db.error", err != nil),
	)
}

// GetQueryStats returns query performance statistics
func (qm *QueryMetrics) GetQueryStats() map[string]interface{} {
	qm.mu.RLock()
	defer qm.mu.RUnlock()

	stats := make(map[string]interface{})
	
	// Calculate average durations
	avgDurations := make(map[string]time.Duration)
	for query, durations := range qm.queryDurations {
		if len(durations) > 0 {
			var total time.Duration
			for _, d := range durations {
				total += d
			}
			avgDurations[query] = total / time.Duration(len(durations))
		}
	}

	stats["total_queries"] = qm.totalQueries
	stats["total_errors"] = qm.totalErrors
	stats["average_durations"] = avgDurations
	stats["error_counts"] = qm.queryErrors
	stats["slow_queries"] = qm.slowQueries

	return stats
}

// ============================================================================
// QUERY OPTIMIZATION HELPERS
// ============================================================================

// QueryOptimizer provides query optimization utilities
type QueryOptimizer struct {
	metrics *QueryMetrics
}

// NewQueryOptimizer creates a new query optimizer
func NewQueryOptimizer(metrics *QueryMetrics) *QueryOptimizer {
	return &QueryOptimizer{
		metrics: metrics,
	}
}

// OptimizeSelect provides optimized SELECT query patterns
func (qo *QueryOptimizer) OptimizeSelect(table string, columns []string, conditions map[string]interface{}) string {
	// Build optimized SELECT query
	query := fmt.Sprintf("SELECT %s FROM %s", joinColumns(columns), table)
	
	if len(conditions) > 0 {
		query += " WHERE " + buildWhereClause(conditions)
	}
	
	return query
}

// OptimizePagination provides optimized pagination patterns
func (qo *QueryOptimizer) OptimizePagination(baseQuery string, limit, offset int) string {
	// Use LIMIT and OFFSET for pagination
	// For better performance with large offsets, consider cursor-based pagination
	return fmt.Sprintf("%s LIMIT %d OFFSET %d", baseQuery, limit, offset)
}

// OptimizeJoin provides optimized JOIN patterns
func (qo *QueryOptimizer) OptimizeJoin(mainTable, joinTable, joinCondition string, joinType string) string {
	if joinType == "" {
		joinType = "INNER"
	}
	return fmt.Sprintf("%s JOIN %s ON %s", joinType, joinTable, joinCondition)
}

// ============================================================================
// CACHING LAYER
// ============================================================================

// CacheConfig holds caching configuration
type CacheConfig struct {
	DefaultTTL    time.Duration `json:"default_ttl"`
	MaxSize       int           `json:"max_size"`
	CleanupPeriod time.Duration `json:"cleanup_period"`
}

// DefaultCacheConfig returns default caching settings
func DefaultCacheConfig() CacheConfig {
	return CacheConfig{
		DefaultTTL:    5 * time.Minute,
		MaxSize:       1000,
		CleanupPeriod: 10 * time.Minute,
	}
}

// CacheEntry represents a cached item
type CacheEntry struct {
	Data      interface{} `json:"data"`
	ExpiresAt time.Time   `json:"expires_at"`
	CreatedAt time.Time   `json:"created_at"`
	HitCount  int64       `json:"hit_count"`
}

// InMemoryCache provides in-memory caching with TTL
type InMemoryCache struct {
	mu      sync.RWMutex
	data    map[string]*CacheEntry
	config  CacheConfig
	metrics *CacheMetrics
}

// CacheMetrics tracks cache performance
type CacheMetrics struct {
	hits   int64
	misses int64
	evictions int64
}

// NewInMemoryCache creates a new in-memory cache
func NewInMemoryCache(config CacheConfig) *InMemoryCache {
	cache := &InMemoryCache{
		data:    make(map[string]*CacheEntry),
		config:  config,
		metrics: &CacheMetrics{},
	}
	
	// Start cleanup goroutine
	go cache.cleanup()
	
	return cache
}

// Get retrieves an item from cache
func (c *InMemoryCache) Get(key string) (interface{}, bool) {
	c.mu.RLock()
	defer c.mu.RUnlock()

	entry, exists := c.data[key]
	if !exists {
		c.metrics.misses++
		return nil, false
	}

	if time.Now().After(entry.ExpiresAt) {
		c.metrics.misses++
		return nil, false
	}

	entry.HitCount++
	c.metrics.hits++
	return entry.Data, true
}

// Set stores an item in cache
func (c *InMemoryCache) Set(key string, value interface{}, ttl time.Duration) {
	c.mu.Lock()
	defer c.mu.Unlock()

	if ttl == 0 {
		ttl = c.config.DefaultTTL
	}

	// Evict if cache is full
	if len(c.data) >= c.config.MaxSize {
		c.evictLRU()
	}

	c.data[key] = &CacheEntry{
		Data:      value,
		ExpiresAt: time.Now().Add(ttl),
		CreatedAt: time.Now(),
		HitCount:  0,
	}
}

// Delete removes an item from cache
func (c *InMemoryCache) Delete(key string) {
	c.mu.Lock()
	defer c.mu.Unlock()
	delete(c.data, key)
}

// GetStats returns cache statistics
func (c *InMemoryCache) GetStats() map[string]interface{} {
	c.mu.RLock()
	defer c.mu.RUnlock()

	hitRate := float64(0)
	total := c.metrics.hits + c.metrics.misses
	if total > 0 {
		hitRate = float64(c.metrics.hits) / float64(total) * 100
	}

	return map[string]interface{}{
		"hits":       c.metrics.hits,
		"misses":     c.metrics.misses,
		"evictions":  c.metrics.evictions,
		"hit_rate":   hitRate,
		"size":       len(c.data),
		"max_size":   c.config.MaxSize,
	}
}

// cleanup removes expired entries
func (c *InMemoryCache) cleanup() {
	ticker := time.NewTicker(c.config.CleanupPeriod)
	defer ticker.Stop()

	for range ticker.C {
		c.mu.Lock()
		now := time.Now()
		for key, entry := range c.data {
			if now.After(entry.ExpiresAt) {
				delete(c.data, key)
			}
		}
		c.mu.Unlock()
	}
}

// evictLRU removes the least recently used item
func (c *InMemoryCache) evictLRU() {
	var oldestKey string
	var oldestTime time.Time
	var lowestHits int64 = -1

	for key, entry := range c.data {
		if lowestHits == -1 || entry.HitCount < lowestHits {
			oldestKey = key
			oldestTime = entry.CreatedAt
			lowestHits = entry.HitCount
		} else if entry.HitCount == lowestHits && entry.CreatedAt.Before(oldestTime) {
			oldestKey = key
			oldestTime = entry.CreatedAt
		}
	}

	if oldestKey != "" {
		delete(c.data, oldestKey)
		c.metrics.evictions++
	}
}

// ============================================================================
// HELPER FUNCTIONS
// ============================================================================

// joinColumns joins column names for SELECT queries
func joinColumns(columns []string) string {
	if len(columns) == 0 {
		return "*"
	}
	
	result := ""
	for i, col := range columns {
		if i > 0 {
			result += ", "
		}
		result += col
	}
	return result
}

// buildWhereClause builds WHERE clause from conditions
func buildWhereClause(conditions map[string]interface{}) string {
	if len(conditions) == 0 {
		return ""
	}
	
	clause := ""
	i := 0
	for key, value := range conditions {
		if i > 0 {
			clause += " AND "
		}
		clause += fmt.Sprintf("%s = '%v'", key, value)
		i++
	}
	return clause
}
