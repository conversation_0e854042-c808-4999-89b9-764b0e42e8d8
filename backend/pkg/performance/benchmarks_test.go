// ============================================================================
// PERFORMANCE BENCHMARKS AND TESTS
// ============================================================================

package performance

import (
	"context"
	"fmt"
	"runtime"
	"sync"
	"testing"
	"time"
)

// ============================================================================
// MEMORY POOL BENCHMARKS
// ============================================================================

func BenchmarkMemoryPool_GetBuffer(b *testing.B) {
	pool := NewMemoryPool()
	sizes := []int{1024, 4096, 8192, 16384}

	for _, size := range sizes {
		b.Run(fmt.Sprintf("size_%d", size), func(b *testing.B) {
			b.ResetTimer()
			b.RunParallel(func(pb *testing.PB) {
				for pb.Next() {
					buf := pool.GetBuffer(size)
					pool.PutBuffer(buf)
				}
			})
		})
	}
}

func BenchmarkMemoryPool_vs_MakeSlice(b *testing.B) {
	pool := NewMemoryPool()
	size := 4096

	b.Run("MemoryPool", func(b *testing.B) {
		b.ResetTimer()
		for i := 0; i < b.N; i++ {
			buf := pool.GetBuffer(size)
			pool.PutBuffer(buf)
		}
	})

	b.Run("MakeSlice", func(b *testing.B) {
		b.ResetTimer()
		for i := 0; i < b.N; i++ {
			_ = make([]byte, size)
		}
	})
}

// ============================================================================
// OBJECT POOL BENCHMARKS
// ============================================================================

type TestObject struct {
	ID   int
	Name string
	Data []byte
}

func BenchmarkObjectPool(b *testing.B) {
	pool := NewObjectPool(
		func() *TestObject {
			return &TestObject{
				Data: make([]byte, 1024),
			}
		},
		func(obj *TestObject) {
			obj.ID = 0
			obj.Name = ""
			obj.Data = obj.Data[:0]
		},
	)

	b.Run("ObjectPool", func(b *testing.B) {
		b.ResetTimer()
		b.RunParallel(func(pb *testing.PB) {
			for pb.Next() {
				obj := pool.Get()
				obj.ID = 123
				obj.Name = "test"
				pool.Put(obj)
			}
		})
	})

	b.Run("NewObject", func(b *testing.B) {
		b.ResetTimer()
		b.RunParallel(func(pb *testing.PB) {
			for pb.Next() {
				obj := &TestObject{
					ID:   123,
					Name: "test",
					Data: make([]byte, 1024),
				}
				_ = obj
			}
		})
	})
}

// ============================================================================
// CACHE BENCHMARKS
// ============================================================================

func BenchmarkInMemoryCache(b *testing.B) {
	cache := NewInMemoryCache(DefaultCacheConfig())
	
	// Pre-populate cache
	for i := 0; i < 1000; i++ {
		cache.Set(fmt.Sprintf("key_%d", i), fmt.Sprintf("value_%d", i), 5*time.Minute)
	}

	b.Run("Get", func(b *testing.B) {
		b.ResetTimer()
		b.RunParallel(func(pb *testing.PB) {
			i := 0
			for pb.Next() {
				key := fmt.Sprintf("key_%d", i%1000)
				cache.Get(key)
				i++
			}
		})
	})

	b.Run("Set", func(b *testing.B) {
		b.ResetTimer()
		b.RunParallel(func(pb *testing.PB) {
			i := 0
			for pb.Next() {
				key := fmt.Sprintf("new_key_%d", i)
				value := fmt.Sprintf("new_value_%d", i)
				cache.Set(key, value, 5*time.Minute)
				i++
			}
		})
	})

	b.Run("Mixed", func(b *testing.B) {
		b.ResetTimer()
		b.RunParallel(func(pb *testing.PB) {
			i := 0
			for pb.Next() {
				if i%4 == 0 {
					// 25% writes
					key := fmt.Sprintf("mixed_key_%d", i)
					value := fmt.Sprintf("mixed_value_%d", i)
					cache.Set(key, value, 5*time.Minute)
				} else {
					// 75% reads
					key := fmt.Sprintf("key_%d", i%1000)
					cache.Get(key)
				}
				i++
			}
		})
	})
}

// ============================================================================
// QUERY BUILDER BENCHMARKS
// ============================================================================

func BenchmarkQueryBuilder(b *testing.B) {
	b.Run("SimpleQuery", func(b *testing.B) {
		b.ResetTimer()
		for i := 0; i < b.N; i++ {
			qb := NewQueryBuilder("users")
			qb.Select("id", "name", "email").
				Where("status = ?", "active").
				OrderBy("created_at", "DESC").
				Limit(10)
			_, _ = qb.Build()
		}
	})

	b.Run("ComplexQuery", func(b *testing.B) {
		b.ResetTimer()
		for i := 0; i < b.N; i++ {
			qb := NewQueryBuilder("users")
			qb.Select("u.id", "u.name", "u.email", "p.title").
				InnerJoin("profiles p", "p.user_id = u.id").
				LeftJoin("companies c", "c.id = u.company_id").
				Where("u.status = ?", "active").
				Where("u.created_at > ?", time.Now().AddDate(0, -1, 0)).
				WhereIn("u.role", []interface{}{"admin", "manager", "user"}).
				GroupBy("u.id", "p.title").
				Having("COUNT(*) > ?", 1).
				OrderBy("u.created_at", "DESC").
				Limit(50).
				Offset(100)
			_, _ = qb.Build()
		}
	})
}

// ============================================================================
// WORKER POOL BENCHMARKS
// ============================================================================

func BenchmarkWorkerPool(b *testing.B) {
	workerCounts := []int{1, 2, 4, 8, 16}
	
	for _, workers := range workerCounts {
		b.Run(fmt.Sprintf("workers_%d", workers), func(b *testing.B) {
			pool := NewWorkerPool(workers, 1000)
			pool.Start()
			defer pool.Stop()

			b.ResetTimer()
			
			var wg sync.WaitGroup
			for i := 0; i < b.N; i++ {
				wg.Add(1)
				task := Task{
					ID:   fmt.Sprintf("task_%d", i),
					Data: i,
					Function: func(data interface{}) (interface{}, error) {
						// Simulate some work
						n := data.(int)
						result := 0
						for j := 0; j < n%1000; j++ {
							result += j
						}
						return result, nil
					},
				}
				
				go func() {
					defer wg.Done()
					pool.Submit(task)
				}()
			}
			
			// Collect results
			go func() {
				for i := 0; i < b.N; i++ {
					<-pool.Results()
				}
				wg.Done()
			}()
			wg.Add(1)
			
			wg.Wait()
		})
	}
}

// ============================================================================
// BATCH EXECUTOR BENCHMARKS
// ============================================================================

func BenchmarkBatchExecutor(b *testing.B) {
	batchSizes := []int{10, 50, 100, 500}
	
	for _, batchSize := range batchSizes {
		b.Run(fmt.Sprintf("batch_size_%d", batchSize), func(b *testing.B) {
			executor := NewBatchExecutor(batchSize, 5*time.Second)
			
			b.ResetTimer()
			for i := 0; i < b.N; i++ {
				operations := make([]BatchOperation, 1000)
				for j := range operations {
					operations[j] = BatchOperation{
						Query:  "INSERT INTO test (id, value) VALUES (?, ?)",
						Params: []interface{}{j, fmt.Sprintf("value_%d", j)},
					}
				}
				
				err := executor.ExecuteBatch(context.Background(), operations, func(batch []BatchOperation) error {
					// Simulate batch execution
					time.Sleep(time.Microsecond * time.Duration(len(batch)))
					return nil
				})
				
				if err != nil {
					b.Fatal(err)
				}
			}
		})
	}
}

// ============================================================================
// PERFORMANCE TESTS
// ============================================================================

func TestMemoryPoolConcurrency(t *testing.T) {
	pool := NewMemoryPool()
	const goroutines = 100
	const operations = 1000
	
	var wg sync.WaitGroup
	wg.Add(goroutines)
	
	for i := 0; i < goroutines; i++ {
		go func() {
			defer wg.Done()
			for j := 0; j < operations; j++ {
				size := 1024 + (j % 4096)
				buf := pool.GetBuffer(size)
				// Use the buffer
				for k := 0; k < len(buf) && k < 10; k++ {
					buf[k] = byte(k)
				}
				pool.PutBuffer(buf)
			}
		}()
	}
	
	wg.Wait()
}

func TestCacheEviction(t *testing.T) {
	config := CacheConfig{
		DefaultTTL:    100 * time.Millisecond,
		MaxSize:       10,
		CleanupPeriod: 50 * time.Millisecond,
	}
	cache := NewInMemoryCache(config)
	
	// Fill cache beyond capacity
	for i := 0; i < 15; i++ {
		cache.Set(fmt.Sprintf("key_%d", i), fmt.Sprintf("value_%d", i), 0)
	}
	
	// Check that cache size is limited
	stats := cache.GetStats()
	if stats["size"].(int) > config.MaxSize {
		t.Errorf("Cache size %d exceeds max size %d", stats["size"], config.MaxSize)
	}
	
	// Wait for TTL expiration
	time.Sleep(150 * time.Millisecond)
	
	// Check that expired items are cleaned up
	if cache.Get("key_0") != nil {
		t.Error("Expected expired item to be cleaned up")
	}
}

func TestQueryBuilderCorrectness(t *testing.T) {
	tests := []struct {
		name     string
		builder  func() *QueryBuilder
		expected string
	}{
		{
			name: "Simple SELECT",
			builder: func() *QueryBuilder {
				return NewQueryBuilder("users").
					Select("id", "name").
					Where("status = ?", "active").
					Limit(10)
			},
			expected: "SELECT id, name FROM users WHERE status = ? LIMIT 10",
		},
		{
			name: "Complex JOIN query",
			builder: func() *QueryBuilder {
				return NewQueryBuilder("users u").
					Select("u.id", "u.name", "p.title").
					InnerJoin("profiles p", "p.user_id = u.id").
					Where("u.status = ?", "active").
					OrderBy("u.created_at", "DESC")
			},
			expected: "SELECT u.id, u.name, p.title FROM users u INNER JOIN profiles p ON p.user_id = u.id WHERE u.status = ? ORDER BY u.created_at DESC",
		},
	}
	
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			query, _ := tt.builder().Build()
			if query != tt.expected {
				t.Errorf("Expected query: %s, got: %s", tt.expected, query)
			}
		})
	}
}

// ============================================================================
// MEMORY USAGE TESTS
// ============================================================================

func TestMemoryUsage(t *testing.T) {
	var m1, m2 runtime.MemStats
	
	// Measure initial memory
	runtime.GC()
	runtime.ReadMemStats(&m1)
	
	// Create and use memory pool
	pool := NewMemoryPool()
	buffers := make([][]byte, 1000)
	
	for i := range buffers {
		buffers[i] = pool.GetBuffer(4096)
	}
	
	for _, buf := range buffers {
		pool.PutBuffer(buf)
	}
	
	// Force GC and measure memory
	runtime.GC()
	runtime.ReadMemStats(&m2)
	
	allocDiff := m2.Alloc - m1.Alloc
	t.Logf("Memory allocated: %d bytes", allocDiff)
	
	// Memory usage should be reasonable
	if allocDiff > 10*1024*1024 { // 10MB threshold
		t.Errorf("Memory usage too high: %d bytes", allocDiff)
	}
}

// ============================================================================
// BENCHMARK UTILITIES
// ============================================================================

func BenchmarkGCOptimizer(b *testing.B) {
	monitor, err := NewMemoryMonitor()
	if err != nil {
		b.Skip("Memory monitor not available")
	}
	
	optimizer := NewGCOptimizer(monitor)
	
	b.ResetTimer()
	for i := 0; i < b.N; i++ {
		optimizer.OptimizeGC()
	}
}

func BenchmarkPerformanceMonitor(b *testing.B) {
	monitor, err := NewMemoryMonitor()
	if err != nil {
		b.Skip("Memory monitor not available")
	}
	
	ctx := context.Background()
	
	b.ResetTimer()
	b.RunParallel(func(pb *testing.PB) {
		for pb.Next() {
			monitor.TrackAllocation(ctx, 1024)
			monitor.TrackDeallocation(ctx, 1024)
		}
	})
}

// ============================================================================
// LOAD TESTING UTILITIES
// ============================================================================

func TestLoadTesting(t *testing.T) {
	if testing.Short() {
		t.Skip("Skipping load test in short mode")
	}
	
	cache := NewInMemoryCache(DefaultCacheConfig())
	const goroutines = 50
	const duration = 5 * time.Second
	
	var wg sync.WaitGroup
	stop := make(chan struct{})
	
	// Start load generators
	for i := 0; i < goroutines; i++ {
		wg.Add(1)
		go func(id int) {
			defer wg.Done()
			counter := 0
			for {
				select {
				case <-stop:
					return
				default:
					key := fmt.Sprintf("load_key_%d_%d", id, counter)
					value := fmt.Sprintf("load_value_%d_%d", id, counter)
					
					// Mix of operations
					switch counter % 4 {
					case 0, 1, 2:
						// 75% reads
						cache.Get(key)
					case 3:
						// 25% writes
						cache.Set(key, value, time.Minute)
					}
					counter++
				}
			}
		}(i)
	}
	
	// Run for specified duration
	time.Sleep(duration)
	close(stop)
	wg.Wait()
	
	// Check cache stats
	stats := cache.GetStats()
	t.Logf("Cache stats after load test: %+v", stats)
}
