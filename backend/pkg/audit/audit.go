// ============================================================================
// COMPREHENSIVE AUDIT LOGGING SYSTEM
// ============================================================================

package audit

import (
	"context"
	"encoding/json"
	"fmt"
	"net/http"
	"time"

	"github.com/google/uuid"
	"go.opentelemetry.io/otel"
	"go.opentelemetry.io/otel/attribute"
	"go.opentelemetry.io/otel/trace"
)

var auditTracer = otel.Tracer("goreal-backend/audit")

// ============================================================================
// AUDIT EVENT TYPES
// ============================================================================

// EventType represents the type of audit event
type EventType string

const (
	// Authentication events
	EventLogin          EventType = "auth.login"
	EventLoginFailed    EventType = "auth.login_failed"
	EventLogout         EventType = "auth.logout"
	EventPasswordReset  EventType = "auth.password_reset"
	EventTokenRefresh   EventType = "auth.token_refresh"
	
	// User management events
	EventUserCreated    EventType = "user.created"
	EventUserUpdated    EventType = "user.updated"
	EventUserDeleted    EventType = "user.deleted"
	EventUserRoleChange EventType = "user.role_changed"
	
	// Data access events
	EventDataAccess     EventType = "data.access"
	EventDataExport     EventType = "data.export"
	EventDataImport     EventType = "data.import"
	
	// Security events
	EventSecurityViolation EventType = "security.violation"
	EventRateLimitExceeded EventType = "security.rate_limit_exceeded"
	EventIPBlocked         EventType = "security.ip_blocked"
	EventSuspiciousActivity EventType = "security.suspicious_activity"
	
	// System events
	EventSystemError    EventType = "system.error"
	EventSystemStartup  EventType = "system.startup"
	EventSystemShutdown EventType = "system.shutdown"
	
	// Business events
	EventTaskCreated    EventType = "task.created"
	EventTaskUpdated    EventType = "task.updated"
	EventTaskDeleted    EventType = "task.deleted"
	EventTaskCompleted  EventType = "task.completed"
	
	EventLeadCreated    EventType = "lead.created"
	EventLeadUpdated    EventType = "lead.updated"
	EventLeadConverted  EventType = "lead.converted"
)

// Severity represents the severity level of an audit event
type Severity string

const (
	SeverityInfo     Severity = "info"
	SeverityWarning  Severity = "warning"
	SeverityError    Severity = "error"
	SeverityCritical Severity = "critical"
)

// ============================================================================
// AUDIT EVENT STRUCTURE
// ============================================================================

// Event represents an audit event
type Event struct {
	ID          string                 `json:"id"`
	Timestamp   time.Time              `json:"timestamp"`
	EventType   EventType              `json:"event_type"`
	Severity    Severity               `json:"severity"`
	UserID      string                 `json:"user_id,omitempty"`
	UserEmail   string                 `json:"user_email,omitempty"`
	UserRole    string                 `json:"user_role,omitempty"`
	IPAddress   string                 `json:"ip_address,omitempty"`
	UserAgent   string                 `json:"user_agent,omitempty"`
	RequestID   string                 `json:"request_id,omitempty"`
	TraceID     string                 `json:"trace_id,omitempty"`
	Resource    string                 `json:"resource,omitempty"`
	Action      string                 `json:"action,omitempty"`
	Result      string                 `json:"result,omitempty"`
	Message     string                 `json:"message"`
	Details     map[string]interface{} `json:"details,omitempty"`
	Metadata    map[string]string      `json:"metadata,omitempty"`
}

// ============================================================================
// AUDIT LOGGER INTERFACE
// ============================================================================

// Logger defines the interface for audit logging
type Logger interface {
	Log(ctx context.Context, event *Event) error
	LogEvent(ctx context.Context, eventType EventType, severity Severity, message string, details map[string]interface{}) error
	LogHTTPRequest(ctx context.Context, r *http.Request, statusCode int, duration time.Duration) error
	LogSecurityEvent(ctx context.Context, eventType EventType, message string, details map[string]interface{}) error
	LogBusinessEvent(ctx context.Context, eventType EventType, message string, details map[string]interface{}) error
}

// ============================================================================
// CONSOLE AUDIT LOGGER
// ============================================================================

// ConsoleLogger implements audit logging to console/stdout
type ConsoleLogger struct {
	enabled bool
}

// NewConsoleLogger creates a new console audit logger
func NewConsoleLogger(enabled bool) *ConsoleLogger {
	return &ConsoleLogger{
		enabled: enabled,
	}
}

// Log logs an audit event to console
func (l *ConsoleLogger) Log(ctx context.Context, event *Event) error {
	if !l.enabled {
		return nil
	}
	
	ctx, span := auditTracer.Start(ctx, "audit.Log")
	defer span.End()
	
	// Add trace information
	if span.SpanContext().IsValid() {
		event.TraceID = span.SpanContext().TraceID().String()
	}
	
	// Serialize event to JSON
	eventJSON, err := json.Marshal(event)
	if err != nil {
		span.RecordError(err)
		return fmt.Errorf("failed to marshal audit event: %w", err)
	}
	
	// Log to console with structured format
	fmt.Printf("[AUDIT] %s\n", string(eventJSON))
	
	span.SetAttributes(
		attribute.String("audit.event_type", string(event.EventType)),
		attribute.String("audit.severity", string(event.Severity)),
		attribute.String("audit.user_id", event.UserID),
	)
	
	return nil
}

// LogEvent logs a generic audit event
func (l *ConsoleLogger) LogEvent(ctx context.Context, eventType EventType, severity Severity, message string, details map[string]interface{}) error {
	event := &Event{
		ID:        uuid.New().String(),
		Timestamp: time.Now().UTC(),
		EventType: eventType,
		Severity:  severity,
		Message:   message,
		Details:   details,
	}
	
	// Extract user information from context
	l.enrichEventFromContext(ctx, event)
	
	return l.Log(ctx, event)
}

// LogHTTPRequest logs HTTP request audit events
func (l *ConsoleLogger) LogHTTPRequest(ctx context.Context, r *http.Request, statusCode int, duration time.Duration) error {
	severity := SeverityInfo
	if statusCode >= 400 && statusCode < 500 {
		severity = SeverityWarning
	} else if statusCode >= 500 {
		severity = SeverityError
	}
	
	details := map[string]interface{}{
		"method":      r.Method,
		"url":         r.URL.String(),
		"status_code": statusCode,
		"duration_ms": duration.Milliseconds(),
		"user_agent":  r.UserAgent(),
		"referer":     r.Referer(),
	}
	
	message := fmt.Sprintf("HTTP %s %s - %d (%dms)", r.Method, r.URL.Path, statusCode, duration.Milliseconds())
	
	event := &Event{
		ID:        uuid.New().String(),
		Timestamp: time.Now().UTC(),
		EventType: EventDataAccess,
		Severity:  severity,
		IPAddress: getClientIP(r),
		UserAgent: r.UserAgent(),
		Resource:  r.URL.Path,
		Action:    r.Method,
		Result:    fmt.Sprintf("%d", statusCode),
		Message:   message,
		Details:   details,
	}
	
	// Extract request ID and user info from context
	l.enrichEventFromContext(ctx, event)
	if requestID := ctx.Value("request_id"); requestID != nil {
		event.RequestID = requestID.(string)
	}
	
	return l.Log(ctx, event)
}

// LogSecurityEvent logs security-related audit events
func (l *ConsoleLogger) LogSecurityEvent(ctx context.Context, eventType EventType, message string, details map[string]interface{}) error {
	return l.LogEvent(ctx, eventType, SeverityWarning, message, details)
}

// LogBusinessEvent logs business-related audit events
func (l *ConsoleLogger) LogBusinessEvent(ctx context.Context, eventType EventType, message string, details map[string]interface{}) error {
	return l.LogEvent(ctx, eventType, SeverityInfo, message, details)
}

// enrichEventFromContext extracts user and request information from context
func (l *ConsoleLogger) enrichEventFromContext(ctx context.Context, event *Event) {
	// Extract user information
	if userID := ctx.Value("user_id"); userID != nil {
		event.UserID = userID.(string)
	}
	if userEmail := ctx.Value("user_email"); userEmail != nil {
		event.UserEmail = userEmail.(string)
	}
	if userRole := ctx.Value("user_role"); userRole != nil {
		event.UserRole = userRole.(string)
	}
	if requestID := ctx.Value("request_id"); requestID != nil {
		event.RequestID = requestID.(string)
	}
}

// ============================================================================
// AUDIT MIDDLEWARE
// ============================================================================

// Middleware creates HTTP middleware for audit logging
func Middleware(logger Logger) func(http.Handler) http.Handler {
	return func(next http.Handler) http.Handler {
		return http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
			start := time.Now()
			
			// Create a response writer wrapper to capture status code
			wrapper := &responseWriter{
				ResponseWriter: w,
				statusCode:     http.StatusOK,
			}
			
			// Process request
			next.ServeHTTP(wrapper, r)
			
			// Log the request
			duration := time.Since(start)
			if err := logger.LogHTTPRequest(r.Context(), r, wrapper.statusCode, duration); err != nil {
				// Log error but don't fail the request
				fmt.Printf("Failed to log audit event: %v\n", err)
			}
		})
	}
}

// responseWriter wraps http.ResponseWriter to capture status code
type responseWriter struct {
	http.ResponseWriter
	statusCode int
}

func (rw *responseWriter) WriteHeader(code int) {
	rw.statusCode = code
	rw.ResponseWriter.WriteHeader(code)
}

// ============================================================================
// HELPER FUNCTIONS
// ============================================================================

// getClientIP extracts the real client IP address
func getClientIP(r *http.Request) string {
	// Check X-Forwarded-For header (from load balancers/proxies)
	if xff := r.Header.Get("X-Forwarded-For"); xff != "" {
		return xff
	}
	
	// Check X-Real-IP header (from nginx)
	if xri := r.Header.Get("X-Real-IP"); xri != "" {
		return xri
	}
	
	// Check CF-Connecting-IP header (from Cloudflare)
	if cfip := r.Header.Get("CF-Connecting-IP"); cfip != "" {
		return cfip
	}
	
	// Fall back to RemoteAddr
	return r.RemoteAddr
}

// ============================================================================
// CONVENIENCE FUNCTIONS
// ============================================================================

// LogAuthSuccess logs successful authentication
func LogAuthSuccess(ctx context.Context, logger Logger, userID, userEmail string) error {
	return logger.LogEvent(ctx, EventLogin, SeverityInfo, 
		fmt.Sprintf("User %s logged in successfully", userEmail),
		map[string]interface{}{
			"user_id": userID,
			"user_email": userEmail,
		})
}

// LogAuthFailure logs failed authentication
func LogAuthFailure(ctx context.Context, logger Logger, email, reason string) error {
	return logger.LogEvent(ctx, EventLoginFailed, SeverityWarning,
		fmt.Sprintf("Login failed for %s: %s", email, reason),
		map[string]interface{}{
			"email": email,
			"reason": reason,
		})
}

// LogSecurityViolation logs security violations
func LogSecurityViolation(ctx context.Context, logger Logger, violation, details string) error {
	return logger.LogSecurityEvent(ctx, EventSecurityViolation,
		fmt.Sprintf("Security violation: %s", violation),
		map[string]interface{}{
			"violation": violation,
			"details": details,
		})
}

// LogRateLimitExceeded logs rate limit violations
func LogRateLimitExceeded(ctx context.Context, logger Logger, clientID string, limit int) error {
	return logger.LogSecurityEvent(ctx, EventRateLimitExceeded,
		fmt.Sprintf("Rate limit exceeded for client %s (limit: %d)", clientID, limit),
		map[string]interface{}{
			"client_id": clientID,
			"limit": limit,
		})
}

// LogDataAccess logs data access events
func LogDataAccess(ctx context.Context, logger Logger, resource, action string) error {
	return logger.LogBusinessEvent(ctx, EventDataAccess,
		fmt.Sprintf("Data access: %s %s", action, resource),
		map[string]interface{}{
			"resource": resource,
			"action": action,
		})
}
