package jwt

import (
	"context"
	"crypto/rand"
	"encoding/hex"
	"errors"
	"fmt"
	"time"

	"github.com/golang-jwt/jwt/v5"
	"github.com/google/uuid"
	"github.com/redis/go-redis/v9"
)

// <PERSON><PERSON><PERSON> represents JWT claims
type Claims struct {
	UserID   string `json:"user_id"`
	Email    string `json:"email"`
	Role     string `json:"role"`
	Username string `json:"username"`
	jwt.RegisteredClaims
}

// TokenPair represents access and refresh tokens
type TokenPair struct {
	AccessToken  string `json:"access_token"`
	RefreshToken string `json:"refresh_token"`
	ExpiresIn    int64  `json:"expires_in"`
}

// JWTManager handles JWT operations with enhanced security
type JWTManager struct {
	accessSecret         string
	refreshSecret        string
	accessTokenDuration  time.Duration
	refreshTokenDuration time.Duration
	redisClient          *redis.Client // For token blacklisting
	issuer               string
}

// BlacklistEntry represents a blacklisted token
type BlacklistEntry struct {
	TokenID   string    `json:"token_id"`
	UserID    string    `json:"user_id"`
	Reason    string    `json:"reason"`
	ExpiresAt time.Time `json:"expires_at"`
}

// NewJWTManager creates a new JWT manager with enhanced security
func NewJWTManager(accessSecret, refreshSecret string, accessDuration, refreshDuration time.Duration, redisClient *redis.Client) *JWTManager {
	return &JWTManager{
		accessSecret:         accessSecret,
		refreshSecret:        refreshSecret,
		accessTokenDuration:  accessDuration,
		refreshTokenDuration: refreshDuration,
		redisClient:          redisClient,
		issuer:               "goreal-backend",
	}
}

// NewJWTManagerWithoutRedis creates a JWT manager without Redis (for testing)
func NewJWTManagerWithoutRedis(accessSecret, refreshSecret string, accessDuration, refreshDuration time.Duration) *JWTManager {
	return &JWTManager{
		accessSecret:         accessSecret,
		refreshSecret:        refreshSecret,
		accessTokenDuration:  accessDuration,
		refreshTokenDuration: refreshDuration,
		redisClient:          nil,
		issuer:               "goreal-backend",
	}
}

// GenerateTokenPair generates access and refresh tokens
func (j *JWTManager) GenerateTokenPair(userID uuid.UUID, email, username, role string) (*TokenPair, error) {
	// Generate access token
	accessToken, err := j.generateAccessToken(userID, email, username, role)
	if err != nil {
		return nil, err
	}

	// Generate refresh token
	refreshToken, err := j.generateRefreshToken(userID, email, username, role)
	if err != nil {
		return nil, err
	}

	return &TokenPair{
		AccessToken:  accessToken,
		RefreshToken: refreshToken,
		ExpiresIn:    int64(j.accessTokenDuration.Seconds()),
	}, nil
}

// generateAccessToken generates an access token
func (j *JWTManager) generateAccessToken(userID uuid.UUID, email, username, role string) (string, error) {
	claims := Claims{
		UserID:   userID.String(),
		Email:    email,
		Role:     role,
		Username: username,
		RegisteredClaims: jwt.RegisteredClaims{
			ExpiresAt: jwt.NewNumericDate(time.Now().Add(j.accessTokenDuration)),
			IssuedAt:  jwt.NewNumericDate(time.Now()),
			NotBefore: jwt.NewNumericDate(time.Now()),
			Issuer:    j.issuer,
			Subject:   userID.String(),
			ID:        generateSecureTokenID(),
		},
	}

	token := jwt.NewWithClaims(jwt.SigningMethodHS256, claims)
	return token.SignedString([]byte(j.accessSecret))
}

// generateRefreshToken generates a refresh token
func (j *JWTManager) generateRefreshToken(userID uuid.UUID, email, username, role string) (string, error) {
	claims := Claims{
		UserID:   userID.String(),
		Email:    email,
		Role:     role,
		Username: username,
		RegisteredClaims: jwt.RegisteredClaims{
			ExpiresAt: jwt.NewNumericDate(time.Now().Add(j.refreshTokenDuration)),
			IssuedAt:  jwt.NewNumericDate(time.Now()),
			NotBefore: jwt.NewNumericDate(time.Now()),
			Issuer:    j.issuer,
			Subject:   userID.String(),
			ID:        generateSecureTokenID(),
		},
	}

	token := jwt.NewWithClaims(jwt.SigningMethodHS256, claims)
	return token.SignedString([]byte(j.refreshSecret))
}

// ValidateAccessToken validates an access token and returns claims
func (j *JWTManager) ValidateAccessToken(tokenString string) (*Claims, error) {
	return j.validateToken(tokenString, j.accessSecret)
}

// ValidateRefreshToken validates a refresh token and returns claims
func (j *JWTManager) ValidateRefreshToken(tokenString string) (*Claims, error) {
	return j.validateToken(tokenString, j.refreshSecret)
}

// validateToken validates a token with the given secret
func (j *JWTManager) validateToken(tokenString, secret string) (*Claims, error) {
	token, err := jwt.ParseWithClaims(tokenString, &Claims{}, func(token *jwt.Token) (interface{}, error) {
		// Validate signing method
		if _, ok := token.Method.(*jwt.SigningMethodHMAC); !ok {
			return nil, errors.New("invalid signing method")
		}
		return []byte(secret), nil
	})

	if err != nil {
		return nil, err
	}

	claims, ok := token.Claims.(*Claims)
	if !ok || !token.Valid {
		return nil, errors.New("invalid token")
	}

	// Check if token is expired
	if claims.ExpiresAt != nil && claims.ExpiresAt.Time.Before(time.Now()) {
		return nil, errors.New("token expired")
	}

	return claims, nil
}

// RefreshTokenPair generates new tokens using a valid refresh token
func (j *JWTManager) RefreshTokenPair(refreshToken string) (*TokenPair, error) {
	// Validate refresh token
	claims, err := j.ValidateRefreshToken(refreshToken)
	if err != nil {
		return nil, err
	}

	// Parse user ID
	userID, err := uuid.Parse(claims.UserID)
	if err != nil {
		return nil, errors.New("invalid user ID in token")
	}

	// Generate new token pair
	return j.GenerateTokenPair(userID, claims.Email, claims.Username, claims.Role)
}

// ExtractUserID extracts user ID from token without validation
func ExtractUserID(tokenString string) (string, error) {
	token, _, err := new(jwt.Parser).ParseUnverified(tokenString, &Claims{})
	if err != nil {
		return "", err
	}

	claims, ok := token.Claims.(*Claims)
	if !ok {
		return "", errors.New("invalid claims")
	}

	return claims.UserID, nil
}

// GetTokenExpiration returns the expiration time of a token
func (j *JWTManager) GetTokenExpiration(tokenString string) (time.Time, error) {
	claims, err := j.ValidateAccessToken(tokenString)
	if err != nil {
		return time.Time{}, err
	}

	if claims.ExpiresAt == nil {
		return time.Time{}, errors.New("no expiration time in token")
	}

	return claims.ExpiresAt.Time, nil
}

// IsTokenExpired checks if a token is expired
func (j *JWTManager) IsTokenExpired(tokenString string) bool {
	expTime, err := j.GetTokenExpiration(tokenString)
	if err != nil {
		return true
	}
	return expTime.Before(time.Now())
}

// GetTokenClaims returns all claims from a token
func (j *JWTManager) GetTokenClaims(tokenString string) (*Claims, error) {
	return j.ValidateAccessToken(tokenString)
}

// ============================================================================
// ENHANCED SECURITY METHODS
// ============================================================================

// ValidateAccessTokenSecure validates an access token with blacklist checking
func (j *JWTManager) ValidateAccessTokenSecure(ctx context.Context, tokenString string) (*Claims, error) {
	return j.validateTokenWithBlacklist(ctx, tokenString, j.accessSecret)
}

// ValidateRefreshTokenSecure validates a refresh token with blacklist checking
func (j *JWTManager) ValidateRefreshTokenSecure(ctx context.Context, tokenString string) (*Claims, error) {
	return j.validateTokenWithBlacklist(ctx, tokenString, j.refreshSecret)
}

// validateTokenWithBlacklist validates a token and checks blacklist
func (j *JWTManager) validateTokenWithBlacklist(ctx context.Context, tokenString, secret string) (*Claims, error) {
	// First validate the token structure and signature
	claims, err := j.validateToken(tokenString, secret)
	if err != nil {
		return nil, err
	}

	// Check if token is blacklisted (if Redis is available)
	if j.redisClient != nil {
		isBlacklisted, err := j.IsTokenBlacklisted(ctx, claims.ID)
		if err != nil {
			// Log error but don't fail validation if Redis is down
			fmt.Printf("Warning: Failed to check token blacklist: %v\n", err)
		} else if isBlacklisted {
			return nil, errors.New("token has been revoked")
		}
	}

	return claims, nil
}

// BlacklistToken adds a token to the blacklist
func (j *JWTManager) BlacklistToken(ctx context.Context, tokenID, userID, reason string, expiresAt time.Time) error {
	if j.redisClient == nil {
		return errors.New("Redis client not available for token blacklisting")
	}

	entry := BlacklistEntry{
		TokenID:   tokenID,
		UserID:    userID,
		Reason:    reason,
		ExpiresAt: expiresAt,
	}

	key := fmt.Sprintf("blacklist:token:%s", tokenID)
	ttl := time.Until(expiresAt)
	if ttl <= 0 {
		return nil // Token already expired, no need to blacklist
	}

	return j.redisClient.Set(ctx, key, entry, ttl).Err()
}

// IsTokenBlacklisted checks if a token is blacklisted
func (j *JWTManager) IsTokenBlacklisted(ctx context.Context, tokenID string) (bool, error) {
	if j.redisClient == nil {
		return false, nil // No Redis, assume not blacklisted
	}

	key := fmt.Sprintf("blacklist:token:%s", tokenID)
	exists, err := j.redisClient.Exists(ctx, key).Result()
	if err != nil {
		return false, err
	}

	return exists > 0, nil
}

// BlacklistAllUserTokens blacklists all tokens for a specific user
func (j *JWTManager) BlacklistAllUserTokens(ctx context.Context, userID, reason string) error {
	if j.redisClient == nil {
		return errors.New("Redis client not available for token blacklisting")
	}

	// Add user to global blacklist (this will invalidate all future tokens)
	key := fmt.Sprintf("blacklist:user:%s", userID)
	return j.redisClient.Set(ctx, key, reason, 24*time.Hour).Err() // 24 hour blacklist
}

// IsUserBlacklisted checks if all tokens for a user are blacklisted
func (j *JWTManager) IsUserBlacklisted(ctx context.Context, userID string) (bool, error) {
	if j.redisClient == nil {
		return false, nil
	}

	key := fmt.Sprintf("blacklist:user:%s", userID)
	exists, err := j.redisClient.Exists(ctx, key).Result()
	if err != nil {
		return false, err
	}

	return exists > 0, nil
}

// generateSecureTokenID generates a cryptographically secure token ID
func generateSecureTokenID() string {
	bytes := make([]byte, 32)
	if _, err := rand.Read(bytes); err != nil {
		// Fallback to UUID if crypto/rand fails
		return uuid.New().String()
	}
	return hex.EncodeToString(bytes)
}
