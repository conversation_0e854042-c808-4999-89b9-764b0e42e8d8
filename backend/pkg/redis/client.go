// ============================================================================
// REDIS CLIENT FOR SECURITY FEATURES
// ============================================================================

package redis

import (
	"context"
	"fmt"
	"time"

	"goreal-backend/internal/config"

	"github.com/redis/go-redis/v9"
	"go.opentelemetry.io/otel"
	"go.opentelemetry.io/otel/attribute"
	"go.opentelemetry.io/otel/trace"
)

var redisTracer = otel.Tracer("goreal-backend/redis")

// ============================================================================
// REDIS CLIENT WRAPPER
// ============================================================================

// Client wraps redis.Client with additional functionality
type Client struct {
	*redis.Client
	config config.RedisConfig
}

// NewClient creates a new Redis client with configuration
func NewClient(cfg config.RedisConfig) (*Client, error) {
	ctx, span := redisTracer.Start(context.Background(), "redis.NewClient")
	defer span.End()

	// Create Redis options
	opts := &redis.Options{
		Addr:         fmt.Sprintf("%s:%d", cfg.Host, cfg.Port),
		Password:     cfg.Password,
		DB:           cfg.DB,
		PoolSize:     cfg.PoolSize,
		MinIdleConns: cfg.PoolSize / 4,
		MaxRetries:   3,
		DialTimeout:  5 * time.Second,
		ReadTimeout:  3 * time.Second,
		WriteTimeout: 3 * time.Second,
		PoolTimeout:  4 * time.Second,
		IdleTimeout:  5 * time.Minute,
	}

	// Create Redis client
	rdb := redis.NewClient(opts)

	// Test connection
	ctx, cancel := context.WithTimeout(context.Background(), 5*time.Second)
	defer cancel()

	if err := rdb.Ping(ctx).Err(); err != nil {
		span.RecordError(err)
		span.SetAttributes(
			attribute.String("redis.error", "connection_failed"),
			attribute.String("redis.host", cfg.Host),
			attribute.Int("redis.port", cfg.Port),
		)
		return nil, fmt.Errorf("failed to connect to Redis: %w", err)
	}

	span.SetAttributes(
		attribute.String("redis.status", "connected"),
		attribute.String("redis.host", cfg.Host),
		attribute.Int("redis.port", cfg.Port),
		attribute.Int("redis.db", cfg.DB),
	)

	return &Client{
		Client: rdb,
		config: cfg,
	}, nil
}

// ============================================================================
// RATE LIMITING OPERATIONS
// ============================================================================

// IncrementRateLimit increments the rate limit counter for a key
func (c *Client) IncrementRateLimit(ctx context.Context, key string, window time.Duration) (int64, error) {
	ctx, span := redisTracer.Start(ctx, "redis.IncrementRateLimit")
	defer span.End()

	pipe := c.Pipeline()
	incrCmd := pipe.Incr(ctx, key)
	expireCmd := pipe.Expire(ctx, key, window)

	_, err := pipe.Exec(ctx)
	if err != nil {
		span.RecordError(err)
		return 0, err
	}

	count := incrCmd.Val()
	
	span.SetAttributes(
		attribute.String("rate_limit.key", key),
		attribute.Int64("rate_limit.count", count),
		attribute.String("rate_limit.window", window.String()),
	)

	return count, nil
}

// GetRateLimit gets the current rate limit count for a key
func (c *Client) GetRateLimit(ctx context.Context, key string) (int64, error) {
	ctx, span := redisTracer.Start(ctx, "redis.GetRateLimit")
	defer span.End()

	count, err := c.Get(ctx, key).Int64()
	if err == redis.Nil {
		return 0, nil
	}
	if err != nil {
		span.RecordError(err)
		return 0, err
	}

	span.SetAttributes(
		attribute.String("rate_limit.key", key),
		attribute.Int64("rate_limit.count", count),
	)

	return count, nil
}

// ============================================================================
// TOKEN BLACKLISTING OPERATIONS
// ============================================================================

// BlacklistToken adds a token to the blacklist
func (c *Client) BlacklistToken(ctx context.Context, tokenID string, data interface{}, ttl time.Duration) error {
	ctx, span := redisTracer.Start(ctx, "redis.BlacklistToken")
	defer span.End()

	key := fmt.Sprintf("blacklist:token:%s", tokenID)
	
	err := c.Set(ctx, key, data, ttl).Err()
	if err != nil {
		span.RecordError(err)
		return err
	}

	span.SetAttributes(
		attribute.String("blacklist.token_id", tokenID),
		attribute.String("blacklist.ttl", ttl.String()),
	)

	return nil
}

// IsTokenBlacklisted checks if a token is blacklisted
func (c *Client) IsTokenBlacklisted(ctx context.Context, tokenID string) (bool, error) {
	ctx, span := redisTracer.Start(ctx, "redis.IsTokenBlacklisted")
	defer span.End()

	key := fmt.Sprintf("blacklist:token:%s", tokenID)
	
	exists, err := c.Exists(ctx, key).Result()
	if err != nil {
		span.RecordError(err)
		return false, err
	}

	isBlacklisted := exists > 0
	
	span.SetAttributes(
		attribute.String("blacklist.token_id", tokenID),
		attribute.Bool("blacklist.is_blacklisted", isBlacklisted),
	)

	return isBlacklisted, nil
}

// BlacklistUser adds a user to the global blacklist
func (c *Client) BlacklistUser(ctx context.Context, userID string, reason string, ttl time.Duration) error {
	ctx, span := redisTracer.Start(ctx, "redis.BlacklistUser")
	defer span.End()

	key := fmt.Sprintf("blacklist:user:%s", userID)
	
	data := map[string]interface{}{
		"reason":     reason,
		"created_at": time.Now().UTC(),
	}
	
	err := c.Set(ctx, key, data, ttl).Err()
	if err != nil {
		span.RecordError(err)
		return err
	}

	span.SetAttributes(
		attribute.String("blacklist.user_id", userID),
		attribute.String("blacklist.reason", reason),
		attribute.String("blacklist.ttl", ttl.String()),
	)

	return nil
}

// IsUserBlacklisted checks if a user is globally blacklisted
func (c *Client) IsUserBlacklisted(ctx context.Context, userID string) (bool, error) {
	ctx, span := redisTracer.Start(ctx, "redis.IsUserBlacklisted")
	defer span.End()

	key := fmt.Sprintf("blacklist:user:%s", userID)
	
	exists, err := c.Exists(ctx, key).Result()
	if err != nil {
		span.RecordError(err)
		return false, err
	}

	isBlacklisted := exists > 0
	
	span.SetAttributes(
		attribute.String("blacklist.user_id", userID),
		attribute.Bool("blacklist.is_blacklisted", isBlacklisted),
	)

	return isBlacklisted, nil
}

// ============================================================================
// SESSION MANAGEMENT
// ============================================================================

// StoreSession stores a user session
func (c *Client) StoreSession(ctx context.Context, sessionID string, userID string, data interface{}, ttl time.Duration) error {
	ctx, span := redisTracer.Start(ctx, "redis.StoreSession")
	defer span.End()

	key := fmt.Sprintf("session:%s", sessionID)
	
	sessionData := map[string]interface{}{
		"user_id":    userID,
		"data":       data,
		"created_at": time.Now().UTC(),
	}
	
	err := c.Set(ctx, key, sessionData, ttl).Err()
	if err != nil {
		span.RecordError(err)
		return err
	}

	span.SetAttributes(
		attribute.String("session.id", sessionID),
		attribute.String("session.user_id", userID),
		attribute.String("session.ttl", ttl.String()),
	)

	return nil
}

// GetSession retrieves a user session
func (c *Client) GetSession(ctx context.Context, sessionID string) (map[string]interface{}, error) {
	ctx, span := redisTracer.Start(ctx, "redis.GetSession")
	defer span.End()

	key := fmt.Sprintf("session:%s", sessionID)
	
	var sessionData map[string]interface{}
	err := c.Get(ctx, key).Scan(&sessionData)
	if err == redis.Nil {
		span.SetAttributes(attribute.String("session.status", "not_found"))
		return nil, nil
	}
	if err != nil {
		span.RecordError(err)
		return nil, err
	}

	span.SetAttributes(
		attribute.String("session.id", sessionID),
		attribute.String("session.status", "found"),
	)

	return sessionData, nil
}

// DeleteSession removes a user session
func (c *Client) DeleteSession(ctx context.Context, sessionID string) error {
	ctx, span := redisTracer.Start(ctx, "redis.DeleteSession")
	defer span.End()

	key := fmt.Sprintf("session:%s", sessionID)
	
	err := c.Del(ctx, key).Err()
	if err != nil {
		span.RecordError(err)
		return err
	}

	span.SetAttributes(attribute.String("session.id", sessionID))

	return nil
}

// ============================================================================
// CACHE OPERATIONS
// ============================================================================

// SetCache stores data in cache with TTL
func (c *Client) SetCache(ctx context.Context, key string, value interface{}, ttl time.Duration) error {
	ctx, span := redisTracer.Start(ctx, "redis.SetCache")
	defer span.End()

	err := c.Set(ctx, key, value, ttl).Err()
	if err != nil {
		span.RecordError(err)
		return err
	}

	span.SetAttributes(
		attribute.String("cache.key", key),
		attribute.String("cache.ttl", ttl.String()),
	)

	return nil
}

// GetCache retrieves data from cache
func (c *Client) GetCache(ctx context.Context, key string, dest interface{}) error {
	ctx, span := redisTracer.Start(ctx, "redis.GetCache")
	defer span.End()

	err := c.Get(ctx, key).Scan(dest)
	if err == redis.Nil {
		span.SetAttributes(
			attribute.String("cache.key", key),
			attribute.String("cache.status", "miss"),
		)
		return nil
	}
	if err != nil {
		span.RecordError(err)
		return err
	}

	span.SetAttributes(
		attribute.String("cache.key", key),
		attribute.String("cache.status", "hit"),
	)

	return nil
}

// ============================================================================
// HEALTH CHECK
// ============================================================================

// HealthCheck performs a health check on the Redis connection
func (c *Client) HealthCheck(ctx context.Context) error {
	ctx, span := redisTracer.Start(ctx, "redis.HealthCheck")
	defer span.End()

	start := time.Now()
	
	err := c.Ping(ctx).Err()
	if err != nil {
		span.RecordError(err)
		span.SetAttributes(attribute.String("health.status", "unhealthy"))
		return err
	}

	duration := time.Since(start)
	
	span.SetAttributes(
		attribute.String("health.status", "healthy"),
		attribute.Int64("health.response_time_ms", duration.Milliseconds()),
	)

	return nil
}

// Close closes the Redis connection
func (c *Client) Close() error {
	return c.Client.Close()
}
