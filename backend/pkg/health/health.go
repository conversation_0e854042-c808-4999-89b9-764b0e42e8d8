// ============================================================================
// COMPREHENSIVE HEALTH CHECK SYSTEM
// ============================================================================

package health

import (
	"context"
	"database/sql"
	"encoding/json"
	"fmt"
	"net/http"
	"sync"
	"time"

	"github.com/redis/go-redis/v9"
	"go.opentelemetry.io/otel"
	"go.opentelemetry.io/otel/attribute"
)

var healthTracer = otel.Tracer("goreal-backend/health")

// ============================================================================
// HEALTH CHECK TYPES
// ============================================================================

// HealthStatus represents the overall health status
type HealthStatus string

const (
	StatusHealthy   HealthStatus = "healthy"
	StatusDegraded  HealthStatus = "degraded"
	StatusUnhealthy HealthStatus = "unhealthy"
)

// ComponentHealth represents the health of a single component
type ComponentHealth struct {
	Name      string                 `json:"name"`
	Status    HealthStatus           `json:"status"`
	Message   string                 `json:"message,omitempty"`
	Details   map[string]interface{} `json:"details,omitempty"`
	Duration  time.Duration          `json:"duration"`
	Timestamp time.Time              `json:"timestamp"`
}

// OverallHealth represents the overall system health
type OverallHealth struct {
	Status     HealthStatus                `json:"status"`
	Service    string                      `json:"service"`
	Version    string                      `json:"version"`
	Timestamp  time.Time                   `json:"timestamp"`
	Duration   time.Duration               `json:"duration"`
	Components map[string]ComponentHealth  `json:"components"`
	Summary    map[string]interface{}      `json:"summary"`
}

// ============================================================================
// HEALTH CHECKER INTERFACE
// ============================================================================

// Checker defines the interface for health checks
type Checker interface {
	Check(ctx context.Context) ComponentHealth
	Name() string
}

// ============================================================================
// DATABASE HEALTH CHECKER
// ============================================================================

// DatabaseChecker checks database connectivity and performance
type DatabaseChecker struct {
	db   *sql.DB
	name string
}

// NewDatabaseChecker creates a new database health checker
func NewDatabaseChecker(db *sql.DB, name string) *DatabaseChecker {
	return &DatabaseChecker{
		db:   db,
		name: name,
	}
}

func (dc *DatabaseChecker) Name() string {
	return dc.name
}

func (dc *DatabaseChecker) Check(ctx context.Context) ComponentHealth {
	ctx, span := healthTracer.Start(ctx, "DatabaseChecker.Check")
	defer span.End()

	start := time.Now()
	health := ComponentHealth{
		Name:      dc.name,
		Timestamp: start,
		Details:   make(map[string]interface{}),
	}

	// Check database connectivity
	pingCtx, cancel := context.WithTimeout(ctx, 5*time.Second)
	defer cancel()

	if err := dc.db.PingContext(pingCtx); err != nil {
		health.Status = StatusUnhealthy
		health.Message = fmt.Sprintf("Database ping failed: %v", err)
		health.Duration = time.Since(start)
		span.SetAttributes(
			attribute.String("health.status", string(health.Status)),
			attribute.String("health.error", err.Error()),
		)
		return health
	}

	// Get database stats
	stats := dc.db.Stats()
	health.Details["open_connections"] = stats.OpenConnections
	health.Details["in_use"] = stats.InUse
	health.Details["idle"] = stats.Idle
	health.Details["wait_count"] = stats.WaitCount
	health.Details["wait_duration"] = stats.WaitDuration.String()
	health.Details["max_idle_closed"] = stats.MaxIdleClosed
	health.Details["max_lifetime_closed"] = stats.MaxLifetimeClosed

	// Check query performance
	queryCtx, queryCancel := context.WithTimeout(ctx, 3*time.Second)
	defer queryCancel()

	var result int
	queryStart := time.Now()
	err := dc.db.QueryRowContext(queryCtx, "SELECT 1").Scan(&result)
	queryDuration := time.Since(queryStart)

	if err != nil {
		health.Status = StatusDegraded
		health.Message = fmt.Sprintf("Database query failed: %v", err)
	} else {
		health.Details["query_duration"] = queryDuration.String()
		
		// Determine status based on performance
		if queryDuration > 1*time.Second {
			health.Status = StatusDegraded
			health.Message = "Database queries are slow"
		} else if stats.OpenConnections > 20 {
			health.Status = StatusDegraded
			health.Message = "High number of database connections"
		} else {
			health.Status = StatusHealthy
			health.Message = "Database is healthy"
		}
	}

	health.Duration = time.Since(start)
	span.SetAttributes(
		attribute.String("health.status", string(health.Status)),
		attribute.Float64("health.duration_seconds", health.Duration.Seconds()),
	)

	return health
}

// ============================================================================
// REDIS HEALTH CHECKER
// ============================================================================

// RedisChecker checks Redis connectivity and performance
type RedisChecker struct {
	client *redis.Client
	name   string
}

// NewRedisChecker creates a new Redis health checker
func NewRedisChecker(client *redis.Client, name string) *RedisChecker {
	return &RedisChecker{
		client: client,
		name:   name,
	}
}

func (rc *RedisChecker) Name() string {
	return rc.name
}

func (rc *RedisChecker) Check(ctx context.Context) ComponentHealth {
	ctx, span := healthTracer.Start(ctx, "RedisChecker.Check")
	defer span.End()

	start := time.Now()
	health := ComponentHealth{
		Name:      rc.name,
		Timestamp: start,
		Details:   make(map[string]interface{}),
	}

	// Check Redis connectivity
	pingCtx, cancel := context.WithTimeout(ctx, 3*time.Second)
	defer cancel()

	pingStart := time.Now()
	pong, err := rc.client.Ping(pingCtx).Result()
	pingDuration := time.Since(pingStart)

	if err != nil {
		health.Status = StatusUnhealthy
		health.Message = fmt.Sprintf("Redis ping failed: %v", err)
		health.Duration = time.Since(start)
		span.SetAttributes(
			attribute.String("health.status", string(health.Status)),
			attribute.String("health.error", err.Error()),
		)
		return health
	}

	health.Details["ping_response"] = pong
	health.Details["ping_duration"] = pingDuration.String()

	// Get Redis info
	infoCtx, infoCancel := context.WithTimeout(ctx, 2*time.Second)
	defer infoCancel()

	info, err := rc.client.Info(infoCtx, "memory", "clients", "stats").Result()
	if err != nil {
		health.Status = StatusDegraded
		health.Message = fmt.Sprintf("Redis info failed: %v", err)
	} else {
		health.Details["info_available"] = true
		
		// Parse memory usage (simplified)
		if len(info) > 0 {
			health.Details["info_length"] = len(info)
		}
	}

	// Test set/get operation
	testKey := fmt.Sprintf("health_check_%d", time.Now().UnixNano())
	testValue := "health_check_value"
	
	setCtx, setCancel := context.WithTimeout(ctx, 2*time.Second)
	defer setCancel()

	setStart := time.Now()
	err = rc.client.Set(setCtx, testKey, testValue, 10*time.Second).Err()
	setDuration := time.Since(setStart)

	if err != nil {
		health.Status = StatusDegraded
		health.Message = fmt.Sprintf("Redis set operation failed: %v", err)
	} else {
		health.Details["set_duration"] = setDuration.String()

		// Test get operation
		getCtx, getCancel := context.WithTimeout(ctx, 2*time.Second)
		defer getCancel()

		getStart := time.Now()
		val, err := rc.client.Get(getCtx, testKey).Result()
		getDuration := time.Since(getStart)

		if err != nil || val != testValue {
			health.Status = StatusDegraded
			health.Message = "Redis get operation failed"
		} else {
			health.Details["get_duration"] = getDuration.String()
			
			// Clean up test key
			rc.client.Del(context.Background(), testKey)
			
			// Determine status based on performance
			if pingDuration > 100*time.Millisecond || setDuration > 100*time.Millisecond {
				health.Status = StatusDegraded
				health.Message = "Redis operations are slow"
			} else {
				health.Status = StatusHealthy
				health.Message = "Redis is healthy"
			}
		}
	}

	health.Duration = time.Since(start)
	span.SetAttributes(
		attribute.String("health.status", string(health.Status)),
		attribute.Float64("health.duration_seconds", health.Duration.Seconds()),
	)

	return health
}

// ============================================================================
// EXTERNAL SERVICE HEALTH CHECKER
// ============================================================================

// ExternalServiceChecker checks external service availability
type ExternalServiceChecker struct {
	url    string
	name   string
	client *http.Client
}

// NewExternalServiceChecker creates a new external service health checker
func NewExternalServiceChecker(url, name string) *ExternalServiceChecker {
	return &ExternalServiceChecker{
		url:  url,
		name: name,
		client: &http.Client{
			Timeout: 5 * time.Second,
		},
	}
}

func (esc *ExternalServiceChecker) Name() string {
	return esc.name
}

func (esc *ExternalServiceChecker) Check(ctx context.Context) ComponentHealth {
	ctx, span := healthTracer.Start(ctx, "ExternalServiceChecker.Check")
	defer span.End()

	start := time.Now()
	health := ComponentHealth{
		Name:      esc.name,
		Timestamp: start,
		Details:   make(map[string]interface{}),
	}

	req, err := http.NewRequestWithContext(ctx, "GET", esc.url, nil)
	if err != nil {
		health.Status = StatusUnhealthy
		health.Message = fmt.Sprintf("Failed to create request: %v", err)
		health.Duration = time.Since(start)
		return health
	}

	resp, err := esc.client.Do(req)
	if err != nil {
		health.Status = StatusUnhealthy
		health.Message = fmt.Sprintf("Request failed: %v", err)
		health.Duration = time.Since(start)
		span.SetAttributes(
			attribute.String("health.status", string(health.Status)),
			attribute.String("health.error", err.Error()),
		)
		return health
	}
	defer resp.Body.Close()

	health.Details["status_code"] = resp.StatusCode
	health.Details["url"] = esc.url

	if resp.StatusCode >= 200 && resp.StatusCode < 300 {
		health.Status = StatusHealthy
		health.Message = "External service is healthy"
	} else if resp.StatusCode >= 500 {
		health.Status = StatusUnhealthy
		health.Message = fmt.Sprintf("External service returned %d", resp.StatusCode)
	} else {
		health.Status = StatusDegraded
		health.Message = fmt.Sprintf("External service returned %d", resp.StatusCode)
	}

	health.Duration = time.Since(start)
	span.SetAttributes(
		attribute.String("health.status", string(health.Status)),
		attribute.Int("http.status_code", resp.StatusCode),
		attribute.Float64("health.duration_seconds", health.Duration.Seconds()),
	)

	return health
}

// ============================================================================
// HEALTH MANAGER
// ============================================================================

// Manager manages all health checks
type Manager struct {
	checkers    []Checker
	serviceName string
	version     string
	mu          sync.RWMutex
	lastCheck   *OverallHealth
}

// NewManager creates a new health check manager
func NewManager(serviceName, version string) *Manager {
	return &Manager{
		checkers:    make([]Checker, 0),
		serviceName: serviceName,
		version:     version,
	}
}

// AddChecker adds a health checker
func (m *Manager) AddChecker(checker Checker) {
	m.mu.Lock()
	defer m.mu.Unlock()
	m.checkers = append(m.checkers, checker)
}

// Check performs all health checks
func (m *Manager) Check(ctx context.Context) *OverallHealth {
	ctx, span := healthTracer.Start(ctx, "HealthManager.Check")
	defer span.End()

	start := time.Now()
	overall := &OverallHealth{
		Service:    m.serviceName,
		Version:    m.version,
		Timestamp:  start,
		Components: make(map[string]ComponentHealth),
		Summary:    make(map[string]interface{}),
	}

	m.mu.RLock()
	checkers := make([]Checker, len(m.checkers))
	copy(checkers, m.checkers)
	m.mu.RUnlock()

	// Run health checks concurrently
	var wg sync.WaitGroup
	results := make(chan ComponentHealth, len(checkers))

	for _, checker := range checkers {
		wg.Add(1)
		go func(c Checker) {
			defer wg.Done()
			result := c.Check(ctx)
			results <- result
		}(checker)
	}

	wg.Wait()
	close(results)

	// Collect results
	healthyCount := 0
	degradedCount := 0
	unhealthyCount := 0

	for result := range results {
		overall.Components[result.Name] = result
		
		switch result.Status {
		case StatusHealthy:
			healthyCount++
		case StatusDegraded:
			degradedCount++
		case StatusUnhealthy:
			unhealthyCount++
		}
	}

	// Determine overall status
	if unhealthyCount > 0 {
		overall.Status = StatusUnhealthy
	} else if degradedCount > 0 {
		overall.Status = StatusDegraded
	} else {
		overall.Status = StatusHealthy
	}

	overall.Duration = time.Since(start)
	overall.Summary["total_components"] = len(checkers)
	overall.Summary["healthy_components"] = healthyCount
	overall.Summary["degraded_components"] = degradedCount
	overall.Summary["unhealthy_components"] = unhealthyCount

	// Cache the result
	m.mu.Lock()
	m.lastCheck = overall
	m.mu.Unlock()

	span.SetAttributes(
		attribute.String("health.overall_status", string(overall.Status)),
		attribute.Int("health.total_components", len(checkers)),
		attribute.Int("health.healthy_components", healthyCount),
		attribute.Int("health.degraded_components", degradedCount),
		attribute.Int("health.unhealthy_components", unhealthyCount),
		attribute.Float64("health.duration_seconds", overall.Duration.Seconds()),
	)

	return overall
}

// GetLastCheck returns the last health check result
func (m *Manager) GetLastCheck() *OverallHealth {
	m.mu.RLock()
	defer m.mu.RUnlock()
	return m.lastCheck
}

// ============================================================================
// HTTP HANDLER
// ============================================================================

// Handler creates an HTTP handler for health checks
func (m *Manager) Handler() http.HandlerFunc {
	return func(w http.ResponseWriter, r *http.Request) {
		ctx := r.Context()
		
		// Perform health check
		health := m.Check(ctx)
		
		// Set appropriate HTTP status code
		statusCode := http.StatusOK
		switch health.Status {
		case StatusDegraded:
			statusCode = http.StatusOK // Still return 200 for degraded
		case StatusUnhealthy:
			statusCode = http.StatusServiceUnavailable
		}
		
		w.Header().Set("Content-Type", "application/json")
		w.WriteHeader(statusCode)
		
		if err := json.NewEncoder(w).Encode(health); err != nil {
			http.Error(w, "Failed to encode health check response", http.StatusInternalServerError)
		}
	}
}
