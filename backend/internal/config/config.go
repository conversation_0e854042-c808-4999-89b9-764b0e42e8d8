package config

import (
	"os"
	"strconv"
	"strings"
	"time"

	"github.com/joho/godotenv"
)

// Config holds all configuration for the application
type Config struct {
	// Server configuration
	Port        string
	Environment string
	ServiceName string

	// Database configuration
	SupabaseURL       string
	SupabaseKey       string
	SupabaseS<PERSON>retKey string

	// JWT configuration
	JWT JWTConfig

	// Ethereum configuration
	EthereumNetwork    string
	EthereumRPCURL     string
	ContractAddress    string
	PrivateKey         string

	// CORS configuration
	CORS CORSConfig

	// Rate limiting
	RateLimit RateLimitConfig

	// Security
	Security SecurityConfig

	// Redis
	Redis RedisConfig

	// Observability
	JaegerEndpoint string
	LogLevel       string

	// File storage
	StorageBucket string
	MaxFileSize   int64

	// External APIs
	IPFSGateway string
	PinataAPIKey string
	PinataSecret string
}

// JWTConfig holds JWT configuration
type JWTConfig struct {
	AccessSecret         string
	RefreshSecret        string
	AccessTokenExpiry    time.Duration
	RefreshTokenExpiry   time.Duration
}

// CORSConfig holds CORS configuration
type CORSConfig struct {
	AllowedOrigins []string
}

// RateLimitConfig holds rate limiting configuration
type RateLimitConfig struct {
	RequestsPerMinute int
	BurstSize         int
	RequestsPerWindow int
	WindowSeconds     int
	RedisEnabled      bool
}

// SecurityConfig holds security-related configuration
type SecurityConfig struct {
	CSP           CSPConfig
	IPFilter      IPFilterConfig
	MaxRequestSize int64
	EnableAuditLog bool
}

// CSPConfig holds Content Security Policy configuration
type CSPConfig struct {
	Enabled    bool
	DefaultSrc string
	ScriptSrc  string
	StyleSrc   string
	ImgSrc     string
	ConnectSrc string
	FontSrc    string
	ObjectSrc  string
	MediaSrc   string
	FrameSrc   string
}

// IPFilterConfig holds IP filtering configuration
type IPFilterConfig struct {
	Enabled   bool
	Whitelist []string
	Blacklist []string
}

// RedisConfig holds Redis configuration
type RedisConfig struct {
	Host     string
	Port     int
	Password string
	DB       int
	PoolSize int
}

// Load loads configuration from environment variables
func Load() (*Config, error) {
	// Load .env file if it exists
	_ = godotenv.Load()

	cfg := &Config{
		Port:        getEnv("PORT", "8080"),
		Environment: getEnv("ENVIRONMENT", "development"),
		ServiceName: getEnv("SERVICE_NAME", "goreal-backend"),

		// Database
		SupabaseURL:       getEnv("SUPABASE_URL", ""),
		SupabaseKey:       getEnv("SUPABASE_ANON_KEY", ""),
		SupabaseSecretKey: getEnv("SUPABASE_SERVICE_ROLE_KEY", ""),

		// JWT
		JWT: JWTConfig{
			AccessSecret:       getEnv("JWT_ACCESS_SECRET", "your-access-secret-key"),
			RefreshSecret:      getEnv("JWT_REFRESH_SECRET", "your-refresh-secret-key"),
			AccessTokenExpiry:  time.Duration(getEnvAsInt("JWT_ACCESS_EXPIRY_MINUTES", 15)) * time.Minute,
			RefreshTokenExpiry: time.Duration(getEnvAsInt("JWT_REFRESH_EXPIRY_DAYS", 7)) * 24 * time.Hour,
		},

		// Ethereum
		EthereumNetwork: getEnv("ETHEREUM_NETWORK", "sepolia"),
		EthereumRPCURL:  getEnv("ETHEREUM_RPC_URL", ""),
		ContractAddress: getEnv("CONTRACT_ADDRESS", ""),
		PrivateKey:      getEnv("PRIVATE_KEY", ""),

		// CORS
		CORS: CORSConfig{
			AllowedOrigins: strings.Split(getEnv("CORS_ALLOWED_ORIGINS", "http://localhost:3000"), ","),
		},

		// Rate limiting
		RateLimit: RateLimitConfig{
			RequestsPerMinute: getEnvAsInt("RATE_LIMIT_RPM", 100),
			BurstSize:         getEnvAsInt("RATE_LIMIT_BURST", 10),
			RequestsPerWindow: getEnvAsInt("RATE_LIMIT_REQUESTS_PER_WINDOW", 100),
			WindowSeconds:     getEnvAsInt("RATE_LIMIT_WINDOW_SECONDS", 60),
			RedisEnabled:      getEnvAsBool("RATE_LIMIT_REDIS_ENABLED", false),
		},

		// Security
		Security: SecurityConfig{
			CSP: CSPConfig{
				Enabled:    getEnvAsBool("CSP_ENABLED", true),
				DefaultSrc: getEnv("CSP_DEFAULT_SRC", "'self'"),
				ScriptSrc:  getEnv("CSP_SCRIPT_SRC", "'self' 'unsafe-inline'"),
				StyleSrc:   getEnv("CSP_STYLE_SRC", "'self' 'unsafe-inline'"),
				ImgSrc:     getEnv("CSP_IMG_SRC", "'self' data: https:"),
				ConnectSrc: getEnv("CSP_CONNECT_SRC", "'self'"),
				FontSrc:    getEnv("CSP_FONT_SRC", "'self'"),
				ObjectSrc:  getEnv("CSP_OBJECT_SRC", "'none'"),
				MediaSrc:   getEnv("CSP_MEDIA_SRC", "'self'"),
				FrameSrc:   getEnv("CSP_FRAME_SRC", "'none'"),
			},
			IPFilter: IPFilterConfig{
				Enabled:   getEnvAsBool("IP_FILTER_ENABLED", false),
				Whitelist: strings.Split(getEnv("IP_FILTER_WHITELIST", ""), ","),
				Blacklist: strings.Split(getEnv("IP_FILTER_BLACKLIST", ""), ","),
			},
			MaxRequestSize: getEnvAsInt64("MAX_REQUEST_SIZE", 10*1024*1024), // 10MB
			EnableAuditLog: getEnvAsBool("ENABLE_AUDIT_LOG", true),
		},

		// Redis
		Redis: RedisConfig{
			Host:     getEnv("REDIS_HOST", "localhost"),
			Port:     getEnvAsInt("REDIS_PORT", 6379),
			Password: getEnv("REDIS_PASSWORD", ""),
			DB:       getEnvAsInt("REDIS_DB", 0),
			PoolSize: getEnvAsInt("REDIS_POOL_SIZE", 10),
		},

		// Observability
		JaegerEndpoint: getEnv("JAEGER_ENDPOINT", "http://localhost:14268/api/traces"),
		LogLevel:       getEnv("LOG_LEVEL", "info"),

		// File storage
		StorageBucket: getEnv("STORAGE_BUCKET", "goreal-storage"),
		MaxFileSize:   getEnvAsInt64("MAX_FILE_SIZE", 10*1024*1024), // 10MB default

		// External APIs
		IPFSGateway:  getEnv("IPFS_GATEWAY", "https://gateway.pinata.cloud/ipfs/"),
		PinataAPIKey: getEnv("PINATA_API_KEY", ""),
		PinataSecret: getEnv("PINATA_SECRET_API_KEY", ""),
	}

	return cfg, nil
}

// getEnv gets an environment variable with a fallback value
func getEnv(key, fallback string) string {
	if value := os.Getenv(key); value != "" {
		return value
	}
	return fallback
}

// getEnvAsInt gets an environment variable as integer with a fallback value
func getEnvAsInt(key string, fallback int) int {
	if value := os.Getenv(key); value != "" {
		if intVal, err := strconv.Atoi(value); err == nil {
			return intVal
		}
	}
	return fallback
}

// getEnvAsInt64 gets an environment variable as int64 with a fallback value
func getEnvAsInt64(key string, fallback int64) int64 {
	if value := os.Getenv(key); value != "" {
		if intVal, err := strconv.ParseInt(value, 10, 64); err == nil {
			return intVal
		}
	}
	return fallback
}

// getEnvAsBool gets an environment variable as boolean with a fallback value
func getEnvAsBool(key string, fallback bool) bool {
	if value := os.Getenv(key); value != "" {
		if boolVal, err := strconv.ParseBool(value); err == nil {
			return boolVal
		}
	}
	return fallback
}
