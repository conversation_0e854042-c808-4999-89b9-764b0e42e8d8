package container

import (
	"fmt"

	"goreal-backend/internal/config"
	"goreal-backend/internal/domain"
	"goreal-backend/internal/infrastructure/supabase"
	"goreal-backend/internal/observability"
	"goreal-backend/internal/services"
)

// Container holds all application dependencies following Clean Architecture principles
type Container struct {
	// Configuration
	Config *config.Config

	// Observability
	Observability *observability.Observability

	// Infrastructure Layer
	SupabaseClient *supabase.Client

	// Repository Layer (Data Access)
	UserRepository         domain.UserRepository
	LeadRepository         domain.LeadRepository
	CompanyRepository      domain.CompanyRepository
	ProjectRepository      domain.ProjectRepository
	TaskRepository         domain.TaskRepository
	SaleRepository         domain.SaleRepository
	ClientRepository       domain.ClientRepository
	NotificationRepository domain.NotificationRepository
	InventoryRepository    domain.InventoryRepository
	FollowUpRepository     domain.FollowUpRepository
	CashbookRepository     domain.CashbookRepository

	// Service Layer (Business Logic)
	AuthService         domain.AuthService
	UserService         domain.UserService
	LeadService         domain.LeadService
	ClientService       domain.ClientService
	TaskService         domain.TaskService
	SalesService        domain.SalesService
	AnalyticsService    domain.AnalyticsService
	NotificationService domain.NotificationService
	FinancialService    domain.FinancialService
	NFTService          domain.NFTService
	ChallengeService    domain.ChallengeService
	FilmService         domain.FilmService
}

// NewContainer creates and initializes a new container with all dependencies
// following Clean Architecture dependency injection principles
func NewContainer() (*Container, error) {
	// Load configuration (outermost layer)
	cfg, err := config.Load()
	if err != nil {
		return nil, fmt.Errorf("failed to load config: %w", err)
	}

	// Initialize infrastructure layer
	supabaseClient, err := supabase.NewClient(cfg)
	if err != nil {
		return nil, fmt.Errorf("failed to create Supabase client: %w", err)
	}

	// Initialize observability
	obsConfig := observability.DefaultConfig("goreal-backend", "1.0.0", cfg.Environment)
	obs, err := observability.New(obsConfig)
	if err != nil {
		return nil, fmt.Errorf("failed to create observability: %w", err)
	}

	// Initialize repository layer (data access interfaces)
	repositories, err := initializeRepositories(supabaseClient)
	if err != nil {
		return nil, fmt.Errorf("failed to initialize repositories: %w", err)
	}

	// Initialize service layer (business logic interfaces)
	services, err := initializeServices(cfg, repositories)
	if err != nil {
		return nil, fmt.Errorf("failed to initialize services: %w", err)
	}



	return &Container{
		Config:                 cfg,
		Observability:          obs,
		SupabaseClient:         supabaseClient,
		UserRepository:         repositories.UserRepository,
		LeadRepository:         repositories.LeadRepository,
		CompanyRepository:      repositories.CompanyRepository,
		ProjectRepository:      repositories.ProjectRepository,
		TaskRepository:         repositories.TaskRepository,
		SaleRepository:         repositories.SaleRepository,
		ClientRepository:       repositories.ClientRepository,
		NotificationRepository: repositories.NotificationRepository,
		InventoryRepository:    repositories.InventoryRepository,
		FollowUpRepository:     repositories.FollowUpRepository,
		CashbookRepository:     repositories.CashbookRepository,
		AuthService:            services.AuthService,
		UserService:            services.UserService,
		LeadService:            services.LeadService,
		ClientService:          services.ClientService,
		TaskService:            services.TaskService,
		SalesService:           services.SalesService,
		AnalyticsService:       services.AnalyticsService,
		NotificationService:    services.NotificationService,
		FinancialService:       services.FinancialService,
		NFTService:             services.NFTService,
		ChallengeService:       services.ChallengeService,
		FilmService:            services.FilmService,
	}, nil
}

// repositoryContainer holds all repository implementations
type repositoryContainer struct {
	UserRepository         domain.UserRepository
	LeadRepository         domain.LeadRepository
	CompanyRepository      domain.CompanyRepository
	ProjectRepository      domain.ProjectRepository
	TaskRepository         domain.TaskRepository
	SaleRepository         domain.SaleRepository
	ClientRepository       domain.ClientRepository
	NotificationRepository domain.NotificationRepository
	InventoryRepository    domain.InventoryRepository
	FollowUpRepository     domain.FollowUpRepository
	CashbookRepository     domain.CashbookRepository
}

// serviceContainer holds all service implementations
type serviceContainer struct {
	AuthService         domain.AuthService
	UserService         domain.UserService
	LeadService         domain.LeadService
	ClientService       domain.ClientService
	TaskService         domain.TaskService
	SalesService        domain.SalesService
	AnalyticsService    domain.AnalyticsService
	NotificationService domain.NotificationService
	FinancialService    domain.FinancialService
	NFTService          domain.NFTService
	ChallengeService    domain.ChallengeService
	FilmService         domain.FilmService
}

// initializeRepositories creates all repository implementations
func initializeRepositories(supabaseClient *supabase.Client) (*repositoryContainer, error) {
	return &repositoryContainer{
		UserRepository:         supabase.NewUserRepository(supabaseClient),
		LeadRepository:         supabase.NewLeadRepository(supabaseClient),
		CompanyRepository:      supabase.NewCompanyRepository(supabaseClient),
		ProjectRepository:      supabase.NewProjectRepository(supabaseClient),
		TaskRepository:         supabase.NewTaskRepository(supabaseClient),
		SaleRepository:         supabase.NewSaleRepository(supabaseClient),
		ClientRepository:       supabase.NewClientRepository(supabaseClient),
		NotificationRepository: supabase.NewNotificationRepository(supabaseClient),
		// TODO: Add these repositories when implemented
		InventoryRepository:    nil, // supabase.NewInventoryRepository(supabaseClient),
		FollowUpRepository:     nil, // supabase.NewFollowUpRepository(supabaseClient),
		CashbookRepository:     nil, // supabase.NewCashbookRepository(supabaseClient),
	}, nil
}

// initializeServices creates all service implementations with proper dependency injection
func initializeServices(cfg *config.Config, repos *repositoryContainer) (*serviceContainer, error) {
	// Initialize notification service first (no dependencies)
	notificationService := services.NewNotificationService(cfg)

	// Initialize core services
	authService := services.NewAuthService(cfg, repos.UserRepository)
	userService := services.NewUserService(cfg, repos.UserRepository)

	// Initialize business services with dependencies
	clientService := services.NewClientService(cfg, repos.ClientRepository, repos.UserRepository, repos.CompanyRepository, repos.LeadRepository, notificationService)
	taskService := services.NewTaskService(cfg, repos.TaskRepository, repos.UserRepository, notificationService)
	salesService := services.NewSalesService(cfg, repos.SaleRepository, repos.ClientRepository, repos.InventoryRepository, repos.UserRepository, notificationService)
	leadService := services.NewLeadService(cfg, repos.LeadRepository, repos.ClientRepository, repos.UserRepository, repos.TaskRepository, repos.FollowUpRepository, notificationService)

	// Initialize analytics service (depends on multiple repositories)
	analyticsService := services.NewAnalyticsService(repos.LeadRepository, repos.ClientRepository, repos.SaleRepository, repos.InventoryRepository, repos.TaskRepository, repos.UserRepository, repos.CashbookRepository)

	return &serviceContainer{
		AuthService:         authService,
		UserService:         userService,
		LeadService:         leadService,
		ClientService:       clientService,
		TaskService:         taskService,
		SalesService:        salesService,
		AnalyticsService:    analyticsService,
		NotificationService: notificationService,
		// TODO: Add these services when implemented
		FinancialService: nil, // services.NewFinancialService(cfg, repos.CashbookRepository, notificationService),
		NFTService:       nil, // services.NewNFTService(cfg, repos.NFTRepository, repos.UserRepository),
		ChallengeService: nil, // services.NewChallengeService(cfg, repos.ChallengeRepository, repos.UserRepository, notificationService),
		FilmService:      nil, // services.NewFilmService(cfg, repos.FilmRepository, repos.UserRepository, notificationService),
	}, nil
}

// Health checks the health of all dependencies
func (c *Container) Health() error {
	// Check Supabase connection
	if err := c.SupabaseClient.Health(nil); err != nil {
		return fmt.Errorf("supabase health check failed: %w", err)
	}

	// Add other health checks as needed

	return nil
}

// Close gracefully shuts down all dependencies
func (c *Container) Close() error {
	// Add cleanup logic for any resources that need to be closed
	// For example, database connections, message queues, etc.
	
	return nil
}



// GetConfig returns the application configuration
func (c *Container) GetConfig() *config.Config {
	return c.Config
}

// GetSupabaseClient returns the Supabase client
func (c *Container) GetSupabaseClient() *supabase.Client {
	return c.SupabaseClient
}

// GetUserService returns the user service
func (c *Container) GetUserService() domain.UserService {
	return c.UserService
}

// GetLeadService returns the lead service
func (c *Container) GetLeadService() domain.LeadService {
	return c.LeadService
}

// GetAuthService returns the auth service
func (c *Container) GetAuthService() domain.AuthService {
	return c.AuthService
}
