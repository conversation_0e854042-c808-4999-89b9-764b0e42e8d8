package handlers

import (
	"encoding/json"
	"net/http"

	"goreal-backend/internal/domain"
	"goreal-backend/internal/middleware"

	"github.com/google/uuid"
	"go.opentelemetry.io/otel"
	"go.opentelemetry.io/otel/attribute"
)

var userStdlibTracer = otel.Tracer("goreal-backend/handlers/user")

// UserHandlerStdlib handles user-related HTTP requests using standard library
type UserHandlerStdlib struct {
	userService domain.UserService
}

// NewUserHandlerStdlib creates a new user handler using standard library patterns
func NewUserHandlerStdlib(userService domain.UserService) *UserHandlerStdlib {
	return &UserHandlerStdlib{
		userService: userService,
	}
}

// GetProfile retrieves the current user's profile
func (h *UserHandlerStdlib) GetProfile(w http.ResponseWriter, r *http.Request) {
	ctx, span := userStdlibTracer.Start(r.Context(), "userHandler.GetProfile")
	defer span.End()

	// Get current user from context (set by auth middleware)
	user, err := middleware.GetUserFromContext(r)
	if err != nil {
		span.RecordError(err)
		http.Error(w, `{"error": "Authentication required"}`, http.StatusUnauthorized)
		return
	}

	// Get full user profile
	profile, err := h.userService.GetByID(ctx, user.ID)
	if err != nil {
		span.RecordError(err)
		http.Error(w, `{"error": "Failed to retrieve profile"}`, http.StatusInternalServerError)
		return
	}

	span.SetAttributes(
		attribute.String("user.id", user.ID.String()),
		attribute.String("user.email", profile.Email),
	)

	w.Header().Set("Content-Type", "application/json")
	w.WriteHeader(http.StatusOK)
	json.NewEncoder(w).Encode(map[string]interface{}{
		"data": profile,
	})
}

// UpdateProfile updates the current user's profile
func (h *UserHandlerStdlib) UpdateProfile(w http.ResponseWriter, r *http.Request) {
	ctx, span := userStdlibTracer.Start(r.Context(), "userHandler.UpdateProfile")
	defer span.End()

	// Get current user from context
	user, err := middleware.GetUserFromContext(r)
	if err != nil {
		span.RecordError(err)
		http.Error(w, `{"error": "Authentication required"}`, http.StatusUnauthorized)
		return
	}

	var req domain.UpdateProfileRequest
	if err := json.NewDecoder(r.Body).Decode(&req); err != nil {
		span.RecordError(err)
		http.Error(w, `{"error": "Invalid request format"}`, http.StatusBadRequest)
		return
	}

	// Update profile
	updatedUser, err := h.userService.UpdateProfile(ctx, user.ID, &req)
	if err != nil {
		span.RecordError(err)
		http.Error(w, `{"error": "Failed to update profile"}`, http.StatusBadRequest)
		return
	}

	span.SetAttributes(
		attribute.String("user.id", user.ID.String()),
		attribute.String("user.email", updatedUser.Email),
	)

	w.Header().Set("Content-Type", "application/json")
	w.WriteHeader(http.StatusOK)
	json.NewEncoder(w).Encode(map[string]interface{}{
		"message": "Profile updated successfully",
		"data":    updatedUser,
	})
}

// UploadAvatar handles avatar upload for the current user
func (h *UserHandlerStdlib) UploadAvatar(w http.ResponseWriter, r *http.Request) {
	ctx, span := userStdlibTracer.Start(r.Context(), "userHandler.UploadAvatar")
	defer span.End()

	// Get current user from context
	user, err := middleware.GetUserFromContext(r)
	if err != nil {
		span.RecordError(err)
		http.Error(w, `{"error": "Authentication required"}`, http.StatusUnauthorized)
		return
	}

	// Parse multipart form
	err = r.ParseMultipartForm(10 << 20) // 10 MB max
	if err != nil {
		span.RecordError(err)
		http.Error(w, `{"error": "Failed to parse form"}`, http.StatusBadRequest)
		return
	}

	file, header, err := r.FormFile("avatar")
	if err != nil {
		span.RecordError(err)
		http.Error(w, `{"error": "Avatar file is required"}`, http.StatusBadRequest)
		return
	}
	defer file.Close()

	// TODO: Implement file upload to storage service (Supabase Storage)
	// For now, return a placeholder response
	avatarURL := "https://placeholder.com/avatar/" + user.ID.String()

	// Update user's avatar URL
	updateReq := &domain.UpdateProfileRequest{
		AvatarURL: &avatarURL,
	}

	updatedUser, err := h.userService.UpdateProfile(ctx, user.ID, updateReq)
	if err != nil {
		span.RecordError(err)
		http.Error(w, `{"error": "Failed to update avatar"}`, http.StatusInternalServerError)
		return
	}

	span.SetAttributes(
		attribute.String("user.id", user.ID.String()),
		attribute.String("file.name", header.Filename),
		attribute.Int64("file.size", header.Size),
		attribute.String("avatar.url", avatarURL),
	)

	w.Header().Set("Content-Type", "application/json")
	w.WriteHeader(http.StatusOK)
	json.NewEncoder(w).Encode(map[string]interface{}{
		"message": "Avatar uploaded successfully",
		"data": map[string]interface{}{
			"avatar_url": avatarURL,
			"user":       updatedUser,
		},
	})
}

// GetUser retrieves a user by ID (public profile)
func (h *UserHandlerStdlib) GetUser(w http.ResponseWriter, r *http.Request) {
	ctx, span := userStdlibTracer.Start(r.Context(), "userHandler.GetUser")
	defer span.End()

	// Extract ID from path using Go 1.22+ path value
	idStr := r.PathValue("id")
	if idStr == "" {
		span.SetAttributes(attribute.String("error", "missing_user_id"))
		http.Error(w, `{"error": "User ID is required"}`, http.StatusBadRequest)
		return
	}

	id, err := uuid.Parse(idStr)
	if err != nil {
		span.RecordError(err)
		http.Error(w, `{"error": "Invalid user ID"}`, http.StatusBadRequest)
		return
	}

	user, err := h.userService.GetByID(ctx, id)
	if err != nil {
		span.RecordError(err)
		http.Error(w, `{"error": "User not found"}`, http.StatusNotFound)
		return
	}

	// Return public profile (exclude sensitive information)
	publicProfile := map[string]interface{}{
		"id":         user.ID,
		"username":   user.Username,
		"full_name":  user.FullName,
		"avatar_url": user.AvatarURL,
		"bio":        user.Bio,
		"created_at": user.CreatedAt,
		// Exclude email, password_hash, etc.
	}

	span.SetAttributes(attribute.String("user.id", id.String()))

	w.Header().Set("Content-Type", "application/json")
	w.WriteHeader(http.StatusOK)
	json.NewEncoder(w).Encode(map[string]interface{}{
		"data": publicProfile,
	})
}

// GetUserStats retrieves user statistics
func (h *UserHandlerStdlib) GetUserStats(w http.ResponseWriter, r *http.Request) {
	ctx, span := userStdlibTracer.Start(r.Context(), "userHandler.GetUserStats")
	defer span.End()

	// Extract ID from path
	idStr := r.PathValue("id")
	if idStr == "" {
		span.SetAttributes(attribute.String("error", "missing_user_id"))
		http.Error(w, `{"error": "User ID is required"}`, http.StatusBadRequest)
		return
	}

	id, err := uuid.Parse(idStr)
	if err != nil {
		span.RecordError(err)
		http.Error(w, `{"error": "Invalid user ID"}`, http.StatusBadRequest)
		return
	}

	// Verify user exists
	_, err = h.userService.GetByID(ctx, id)
	if err != nil {
		span.RecordError(err)
		http.Error(w, `{"error": "User not found"}`, http.StatusNotFound)
		return
	}

	// TODO: Implement user statistics collection
	// For now, return placeholder stats
	stats := map[string]interface{}{
		"user_id":           id,
		"total_tasks":       0,
		"completed_tasks":   0,
		"total_sales":       0,
		"total_leads":       0,
		"films_created":     0,
		"challenges_joined": 0,
		"nfts_owned":        0,
	}

	span.SetAttributes(attribute.String("user.id", id.String()))

	w.Header().Set("Content-Type", "application/json")
	w.WriteHeader(http.StatusOK)
	json.NewEncoder(w).Encode(map[string]interface{}{
		"data": stats,
	})
}

// FollowUser follows another user
func (h *UserHandlerStdlib) FollowUser(w http.ResponseWriter, r *http.Request) {
	ctx, span := userStdlibTracer.Start(r.Context(), "userHandler.FollowUser")
	defer span.End()

	// Get current user from context
	currentUser, err := middleware.GetUserFromContext(r)
	if err != nil {
		span.RecordError(err)
		http.Error(w, `{"error": "Authentication required"}`, http.StatusUnauthorized)
		return
	}

	// Extract target user ID from path
	targetIDStr := r.PathValue("id")
	if targetIDStr == "" {
		span.SetAttributes(attribute.String("error", "missing_target_user_id"))
		http.Error(w, `{"error": "Target user ID is required"}`, http.StatusBadRequest)
		return
	}

	targetID, err := uuid.Parse(targetIDStr)
	if err != nil {
		span.RecordError(err)
		http.Error(w, `{"error": "Invalid target user ID"}`, http.StatusBadRequest)
		return
	}

	// Check if trying to follow self
	if currentUser.ID == targetID {
		span.SetAttributes(attribute.String("error", "cannot_follow_self"))
		http.Error(w, `{"error": "Cannot follow yourself"}`, http.StatusBadRequest)
		return
	}

	// Verify target user exists
	_, err = h.userService.GetByID(ctx, targetID)
	if err != nil {
		span.RecordError(err)
		http.Error(w, `{"error": "Target user not found"}`, http.StatusNotFound)
		return
	}

	// TODO: Implement user following functionality
	// This would typically involve a UserFollows table/repository

	span.SetAttributes(
		attribute.String("follower.id", currentUser.ID.String()),
		attribute.String("target.id", targetID.String()),
	)

	w.Header().Set("Content-Type", "application/json")
	w.WriteHeader(http.StatusOK)
	json.NewEncoder(w).Encode(map[string]interface{}{
		"message": "User followed successfully",
	})
}

// UnfollowUser unfollows another user
func (h *UserHandlerStdlib) UnfollowUser(w http.ResponseWriter, r *http.Request) {
	_, span := userStdlibTracer.Start(r.Context(), "userHandler.UnfollowUser")
	defer span.End()

	// Get current user from context
	currentUser, err := middleware.GetUserFromContext(r)
	if err != nil {
		span.RecordError(err)
		http.Error(w, `{"error": "Authentication required"}`, http.StatusUnauthorized)
		return
	}

	// Extract target user ID from path
	targetIDStr := r.PathValue("id")
	if targetIDStr == "" {
		span.SetAttributes(attribute.String("error", "missing_target_user_id"))
		http.Error(w, `{"error": "Target user ID is required"}`, http.StatusBadRequest)
		return
	}

	targetID, err := uuid.Parse(targetIDStr)
	if err != nil {
		span.RecordError(err)
		http.Error(w, `{"error": "Invalid target user ID"}`, http.StatusBadRequest)
		return
	}

	// TODO: Implement user unfollowing functionality

	span.SetAttributes(
		attribute.String("follower.id", currentUser.ID.String()),
		attribute.String("target.id", targetID.String()),
	)

	w.Header().Set("Content-Type", "application/json")
	w.WriteHeader(http.StatusOK)
	json.NewEncoder(w).Encode(map[string]interface{}{
		"message": "User unfollowed successfully",
	})
}
