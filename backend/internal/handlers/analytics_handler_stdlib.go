package handlers

import (
	"encoding/json"
	"net/http"

	"goreal-backend/internal/domain"
	"goreal-backend/internal/middleware"

	"github.com/google/uuid"
	"go.opentelemetry.io/otel"
	"go.opentelemetry.io/otel/attribute"
)

var analyticsStdlibTracer = otel.Tracer("goreal-backend/handlers/analytics")

// AnalyticsHandlerStdlib handles analytics-related HTTP requests using standard library
type AnalyticsHandlerStdlib struct {
	analyticsService domain.AnalyticsService
}

// NewAnalyticsHandlerStdlib creates a new analytics handler using standard library patterns
func NewAnalyticsHandlerStdlib(analyticsService domain.AnalyticsService) *AnalyticsHandlerStdlib {
	return &AnalyticsHandlerStdlib{
		analyticsService: analyticsService,
	}
}

// GetDashboardStats retrieves dashboard statistics for the current user
func (h *AnalyticsHandlerStdlib) GetDashboardStats(w http.ResponseWriter, r *http.Request) {
	ctx, span := analyticsStdlibTracer.Start(r.Context(), "analyticsHandler.GetDashboardStats")
	defer span.End()

	// Get current user from context
	user, err := middleware.GetUserFromContext(r)
	if err != nil {
		span.RecordError(err)
		http.Error(w, `{"error": "Authentication required"}`, http.StatusUnauthorized)
		return
	}

	// Get dashboard stats
	stats, err := h.analyticsService.GetDashboardStats(ctx, user.ID)
	if err != nil {
		span.RecordError(err)
		http.Error(w, `{"error": "Failed to retrieve dashboard stats"}`, http.StatusInternalServerError)
		return
	}

	span.SetAttributes(
		attribute.String("user.id", user.ID.String()),
		attribute.String("user.role", string(user.Role)),
	)

	w.Header().Set("Content-Type", "application/json")
	w.WriteHeader(http.StatusOK)
	json.NewEncoder(w).Encode(map[string]interface{}{
		"data": stats,
	})
}

// GetSalesAnalytics retrieves sales analytics with filtering
func (h *AnalyticsHandlerStdlib) GetSalesAnalytics(w http.ResponseWriter, r *http.Request) {
	ctx, span := analyticsStdlibTracer.Start(r.Context(), "analyticsHandler.GetSalesAnalytics")
	defer span.End()

	// Parse query parameters for filtering
	query := r.URL.Query()
	
	// Create filters from query parameters
	filters := domain.SaleFilters{
		BaseFilters: domain.BaseFilters{
			Limit:  50, // Default limit
			Offset: 0,
		},
	}

	// Parse date range
	if dateFrom := query.Get("date_from"); dateFrom != "" {
		// TODO: Parse date from string
	}
	if dateTo := query.Get("date_to"); dateTo != "" {
		// TODO: Parse date from string
	}

	// Parse salesperson filter
	if salespersonID := query.Get("salesperson_id"); salespersonID != "" {
		if id, err := uuid.Parse(salespersonID); err == nil {
			filters.SalespersonID = &id
		}
	}

	// Get sales analytics
	analytics, err := h.analyticsService.GetSalesAnalytics(ctx, filters)
	if err != nil {
		span.RecordError(err)
		http.Error(w, `{"error": "Failed to retrieve sales analytics"}`, http.StatusInternalServerError)
		return
	}

	span.SetAttributes(
		attribute.String("filter.salesperson_id", query.Get("salesperson_id")),
		attribute.String("filter.date_from", query.Get("date_from")),
		attribute.String("filter.date_to", query.Get("date_to")),
	)

	w.Header().Set("Content-Type", "application/json")
	w.WriteHeader(http.StatusOK)
	json.NewEncoder(w).Encode(map[string]interface{}{
		"data": analytics,
	})
}

// GetLeadAnalytics retrieves lead analytics with filtering
func (h *AnalyticsHandlerStdlib) GetLeadAnalytics(w http.ResponseWriter, r *http.Request) {
	ctx, span := analyticsStdlibTracer.Start(r.Context(), "analyticsHandler.GetLeadAnalytics")
	defer span.End()

	// Parse query parameters for filtering
	query := r.URL.Query()
	
	// Create filters from query parameters
	filters := domain.LeadFilters{
		BaseFilters: domain.BaseFilters{
			Limit:  50, // Default limit
			Offset: 0,
		},
	}

	// Parse assigned to filter
	if assignedTo := query.Get("assigned_to"); assignedTo != "" {
		if id, err := uuid.Parse(assignedTo); err == nil {
			filters.AssignedTo = &id
		}
	}

	// Parse status filter
	if status := query.Get("status"); status != "" {
		leadStatus := domain.LeadStatus(status)
		filters.Status = &leadStatus
	}

	// Get lead analytics
	analytics, err := h.analyticsService.GetLeadAnalytics(ctx, filters)
	if err != nil {
		span.RecordError(err)
		http.Error(w, `{"error": "Failed to retrieve lead analytics"}`, http.StatusInternalServerError)
		return
	}

	span.SetAttributes(
		attribute.String("filter.assigned_to", query.Get("assigned_to")),
		attribute.String("filter.status", query.Get("status")),
	)

	w.Header().Set("Content-Type", "application/json")
	w.WriteHeader(http.StatusOK)
	json.NewEncoder(w).Encode(map[string]interface{}{
		"data": analytics,
	})
}

// GetPropertyAnalytics retrieves property analytics with filtering
func (h *AnalyticsHandlerStdlib) GetPropertyAnalytics(w http.ResponseWriter, r *http.Request) {
	ctx, span := analyticsStdlibTracer.Start(r.Context(), "analyticsHandler.GetPropertyAnalytics")
	defer span.End()

	// Parse query parameters for filtering
	query := r.URL.Query()
	
	// Create filters from query parameters
	filters := domain.InventoryFilters{
		BaseFilters: domain.BaseFilters{
			Limit:  50, // Default limit
			Offset: 0,
		},
	}

	// Parse project filter
	if projectID := query.Get("project_id"); projectID != "" {
		if id, err := uuid.Parse(projectID); err == nil {
			filters.ProjectID = &id
		}
	}

	// Parse status filter
	if status := query.Get("status"); status != "" {
		unitStatus := domain.UnitStatus(status)
		filters.Status = &unitStatus
	}

	// Get property analytics
	analytics, err := h.analyticsService.GetPropertyAnalytics(ctx, filters)
	if err != nil {
		span.RecordError(err)
		http.Error(w, `{"error": "Failed to retrieve property analytics"}`, http.StatusInternalServerError)
		return
	}

	span.SetAttributes(
		attribute.String("filter.project_id", query.Get("project_id")),
		attribute.String("filter.status", query.Get("status")),
	)

	w.Header().Set("Content-Type", "application/json")
	w.WriteHeader(http.StatusOK)
	json.NewEncoder(w).Encode(map[string]interface{}{
		"data": analytics,
	})
}

// GetFinancialAnalytics retrieves financial analytics with filtering
func (h *AnalyticsHandlerStdlib) GetFinancialAnalytics(w http.ResponseWriter, r *http.Request) {
	ctx, span := analyticsStdlibTracer.Start(r.Context(), "analyticsHandler.GetFinancialAnalytics")
	defer span.End()

	// Parse query parameters for filtering
	query := r.URL.Query()
	
	// Create filters from query parameters
	filters := domain.CashbookFilters{
		BaseFilters: domain.BaseFilters{
			Limit:  50, // Default limit
			Offset: 0,
		},
	}

	// Parse transaction type filter
	if transactionType := query.Get("transaction_type"); transactionType != "" {
		txType := domain.TransactionType(transactionType)
		filters.TransactionType = &txType
	}

	// Get financial analytics
	analytics, err := h.analyticsService.GetFinancialAnalytics(ctx, filters)
	if err != nil {
		span.RecordError(err)
		http.Error(w, `{"error": "Failed to retrieve financial analytics"}`, http.StatusInternalServerError)
		return
	}

	span.SetAttributes(
		attribute.String("filter.transaction_type", query.Get("transaction_type")),
	)

	w.Header().Set("Content-Type", "application/json")
	w.WriteHeader(http.StatusOK)
	json.NewEncoder(w).Encode(map[string]interface{}{
		"data": analytics,
	})
}

// GetUserPerformance retrieves user performance analytics
func (h *AnalyticsHandlerStdlib) GetUserPerformance(w http.ResponseWriter, r *http.Request) {
	ctx, span := analyticsStdlibTracer.Start(r.Context(), "analyticsHandler.GetUserPerformance")
	defer span.End()

	// Extract user ID from path
	userIDStr := r.PathValue("userID")
	if userIDStr == "" {
		span.SetAttributes(attribute.String("error", "missing_user_id"))
		http.Error(w, `{"error": "User ID is required"}`, http.StatusBadRequest)
		return
	}

	userID, err := uuid.Parse(userIDStr)
	if err != nil {
		span.RecordError(err)
		http.Error(w, `{"error": "Invalid user ID"}`, http.StatusBadRequest)
		return
	}

	// Parse period parameter
	query := r.URL.Query()
	period := query.Get("period")
	if period == "" {
		period = "month" // Default to monthly
	}

	// Get user performance
	performance, err := h.analyticsService.GetUserPerformance(ctx, userID, period)
	if err != nil {
		span.RecordError(err)
		http.Error(w, `{"error": "Failed to retrieve user performance"}`, http.StatusInternalServerError)
		return
	}

	span.SetAttributes(
		attribute.String("user.id", userID.String()),
		attribute.String("period", period),
	)

	w.Header().Set("Content-Type", "application/json")
	w.WriteHeader(http.StatusOK)
	json.NewEncoder(w).Encode(map[string]interface{}{
		"data": performance,
	})
}

// GenerateReport generates a custom report
func (h *AnalyticsHandlerStdlib) GenerateReport(w http.ResponseWriter, r *http.Request) {
	ctx, span := analyticsStdlibTracer.Start(r.Context(), "analyticsHandler.GenerateReport")
	defer span.End()

	var req domain.GenerateReportRequest
	if err := json.NewDecoder(r.Body).Decode(&req); err != nil {
		span.RecordError(err)
		http.Error(w, `{"error": "Invalid request format"}`, http.StatusBadRequest)
		return
	}

	// Generate report
	reportData, err := h.analyticsService.GenerateReport(ctx, &req)
	if err != nil {
		span.RecordError(err)
		http.Error(w, `{"error": "Failed to generate report"}`, http.StatusInternalServerError)
		return
	}

	span.SetAttributes(
		attribute.String("report.type", req.ReportType),
		attribute.String("report.format", req.Format),
		attribute.Int("report.size", len(reportData)),
	)

	// Set appropriate content type based on format
	switch req.Format {
	case "pdf":
		w.Header().Set("Content-Type", "application/pdf")
	case "excel":
		w.Header().Set("Content-Type", "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet")
	case "csv":
		w.Header().Set("Content-Type", "text/csv")
	default:
		w.Header().Set("Content-Type", "application/octet-stream")
	}

	w.Header().Set("Content-Disposition", "attachment; filename=report."+req.Format)
	w.WriteHeader(http.StatusOK)
	w.Write(reportData)
}
