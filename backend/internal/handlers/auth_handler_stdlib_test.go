package handlers

import (
	"bytes"
	"context"
	"encoding/json"
	"net/http"
	"net/http/httptest"
	"testing"

	"goreal-backend/internal/domain"

	"github.com/google/uuid"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/mock"
)

// MockAuthService is a mock implementation of domain.AuthService
type MockAuthService struct {
	mock.Mock
}

func (m *MockAuthService) Login(ctx context.Context, email, password string) (*domain.AuthResponse, error) {
	args := m.Called(ctx, email, password)
	return args.Get(0).(*domain.AuthResponse), args.Error(1)
}

func (m *MockAuthService) Register(ctx context.Context, req *domain.RegisterRequest) (*domain.AuthResponse, error) {
	args := m.Called(ctx, req)
	return args.Get(0).(*domain.AuthResponse), args.Error(1)
}

func (m *MockAuthService) RefreshToken(ctx context.Context, refreshToken string) (*domain.AuthResponse, error) {
	args := m.Called(ctx, refreshToken)
	return args.Get(0).(*domain.AuthResponse), args.Error(1)
}

func (m *MockAuthService) Logout(ctx context.Context, userID uuid.UUID) error {
	args := m.Called(ctx, userID)
	return args.Error(0)
}

func (m *MockAuthService) ResetPassword(ctx context.Context, email string) error {
	args := m.Called(ctx, email)
	return args.Error(0)
}

func (m *MockAuthService) ConfirmPasswordReset(ctx context.Context, token, newPassword string) error {
	args := m.Called(ctx, token, newPassword)
	return args.Error(0)
}

func (m *MockAuthService) ValidateToken(ctx context.Context, token string) (*domain.User, error) {
	args := m.Called(ctx, token)
	return args.Get(0).(*domain.User), args.Error(1)
}

func (m *MockAuthService) ChangePassword(ctx context.Context, userID uuid.UUID, oldPassword, newPassword string) error {
	args := m.Called(ctx, userID, oldPassword, newPassword)
	return args.Error(0)
}

func TestAuthHandlerStdlib_Login(t *testing.T) {
	tests := []struct {
		name           string
		requestBody    map[string]interface{}
		mockSetup      func(*MockAuthService)
		expectedStatus int
		expectedError  string
	}{
		{
			name: "successful login",
			requestBody: map[string]interface{}{
				"email":    "<EMAIL>",
				"password": "password123",
			},
			mockSetup: func(m *MockAuthService) {
				userID := uuid.New()
				authResponse := &domain.AuthResponse{
					User: &domain.User{
						ID:    userID,
						Email: "<EMAIL>",
						Role:  domain.RoleUser,
					},
					AccessToken:  "access_token",
					RefreshToken: "refresh_token",
				}
				m.On("Login", mock.Anything, "<EMAIL>", "password123").Return(authResponse, nil)
			},
			expectedStatus: http.StatusOK,
		},
		{
			name: "missing email",
			requestBody: map[string]interface{}{
				"password": "password123",
			},
			mockSetup:      func(m *MockAuthService) {},
			expectedStatus: http.StatusBadRequest,
			expectedError:  "Email and password are required",
		},
		{
			name: "missing password",
			requestBody: map[string]interface{}{
				"email": "<EMAIL>",
			},
			mockSetup:      func(m *MockAuthService) {},
			expectedStatus: http.StatusBadRequest,
			expectedError:  "Email and password are required",
		},
		{
			name: "invalid credentials",
			requestBody: map[string]interface{}{
				"email":    "<EMAIL>",
				"password": "wrongpassword",
			},
			mockSetup: func(m *MockAuthService) {
				m.On("Login", mock.Anything, "<EMAIL>", "wrongpassword").Return((*domain.AuthResponse)(nil), domain.ErrInvalidCredentials)
			},
			expectedStatus: http.StatusUnauthorized,
			expectedError:  "Authentication failed",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// Setup
			mockAuthService := new(MockAuthService)
			tt.mockSetup(mockAuthService)
			
			handler := NewAuthHandlerStdlib(mockAuthService)

			// Create request
			body, _ := json.Marshal(tt.requestBody)
			req := httptest.NewRequest("POST", "/api/auth/login", bytes.NewBuffer(body))
			req.Header.Set("Content-Type", "application/json")
			w := httptest.NewRecorder()

			// Execute
			handler.Login(w, req)

			// Assert
			assert.Equal(t, tt.expectedStatus, w.Code)

			if tt.expectedError != "" {
				var response map[string]interface{}
				err := json.Unmarshal(w.Body.Bytes(), &response)
				assert.NoError(t, err)
				assert.Contains(t, response["error"], tt.expectedError)
			} else {
				var response map[string]interface{}
				err := json.Unmarshal(w.Body.Bytes(), &response)
				assert.NoError(t, err)
				assert.Equal(t, "Login successful", response["message"])
				assert.NotNil(t, response["data"])
			}

			mockAuthService.AssertExpectations(t)
		})
	}
}

func TestAuthHandlerStdlib_Register(t *testing.T) {
	tests := []struct {
		name           string
		requestBody    *domain.RegisterRequest
		mockSetup      func(*MockAuthService)
		expectedStatus int
		expectedError  string
	}{
		{
			name: "successful registration",
			requestBody: &domain.RegisterRequest{
				Email:    "<EMAIL>",
				Password: "password123",
				FullName: "New User",
				Username: "newuser",
			},
			mockSetup: func(m *MockAuthService) {
				userID := uuid.New()
				authResponse := &domain.AuthResponse{
					User: &domain.User{
						ID:       userID,
						Email:    "<EMAIL>",
						FullName: "New User",
						Username: "newuser",
						Role:     domain.RoleUser,
					},
					AccessToken:  "access_token",
					RefreshToken: "refresh_token",
				}
				m.On("Register", mock.Anything, mock.AnythingOfType("*domain.RegisterRequest")).Return(authResponse, nil)
			},
			expectedStatus: http.StatusCreated,
		},
		{
			name: "email already exists",
			requestBody: &domain.RegisterRequest{
				Email:    "<EMAIL>",
				Password: "password123",
				FullName: "Existing User",
				Username: "existing",
			},
			mockSetup: func(m *MockAuthService) {
				m.On("Register", mock.Anything, mock.AnythingOfType("*domain.RegisterRequest")).Return((*domain.AuthResponse)(nil), domain.ErrEmailAlreadyExists)
			},
			expectedStatus: http.StatusBadRequest,
			expectedError:  "Registration failed",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// Setup
			mockAuthService := new(MockAuthService)
			tt.mockSetup(mockAuthService)
			
			handler := NewAuthHandlerStdlib(mockAuthService)

			// Create request
			body, _ := json.Marshal(tt.requestBody)
			req := httptest.NewRequest("POST", "/api/auth/register", bytes.NewBuffer(body))
			req.Header.Set("Content-Type", "application/json")
			w := httptest.NewRecorder()

			// Execute
			handler.Register(w, req)

			// Assert
			assert.Equal(t, tt.expectedStatus, w.Code)

			if tt.expectedError != "" {
				var response map[string]interface{}
				err := json.Unmarshal(w.Body.Bytes(), &response)
				assert.NoError(t, err)
				assert.Contains(t, response["error"], tt.expectedError)
			} else {
				var response map[string]interface{}
				err := json.Unmarshal(w.Body.Bytes(), &response)
				assert.NoError(t, err)
				assert.Equal(t, "Registration successful", response["message"])
				assert.NotNil(t, response["data"])
			}

			mockAuthService.AssertExpectations(t)
		})
	}
}

func TestAuthHandlerStdlib_RefreshToken(t *testing.T) {
	tests := []struct {
		name           string
		requestBody    map[string]interface{}
		mockSetup      func(*MockAuthService)
		expectedStatus int
		expectedError  string
	}{
		{
			name: "successful token refresh",
			requestBody: map[string]interface{}{
				"refresh_token": "valid_refresh_token",
			},
			mockSetup: func(m *MockAuthService) {
				userID := uuid.New()
				authResponse := &domain.AuthResponse{
					User: &domain.User{
						ID:    userID,
						Email: "<EMAIL>",
						Role:  domain.RoleUser,
					},
					AccessToken:  "new_access_token",
					RefreshToken: "new_refresh_token",
				}
				m.On("RefreshToken", mock.Anything, "valid_refresh_token").Return(authResponse, nil)
			},
			expectedStatus: http.StatusOK,
		},
		{
			name: "missing refresh token",
			requestBody: map[string]interface{}{},
			mockSetup:      func(m *MockAuthService) {},
			expectedStatus: http.StatusBadRequest,
			expectedError:  "Refresh token is required",
		},
		{
			name: "invalid refresh token",
			requestBody: map[string]interface{}{
				"refresh_token": "invalid_token",
			},
			mockSetup: func(m *MockAuthService) {
				m.On("RefreshToken", mock.Anything, "invalid_token").Return((*domain.AuthResponse)(nil), domain.ErrInvalidToken)
			},
			expectedStatus: http.StatusUnauthorized,
			expectedError:  "Token refresh failed",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// Setup
			mockAuthService := new(MockAuthService)
			tt.mockSetup(mockAuthService)
			
			handler := NewAuthHandlerStdlib(mockAuthService)

			// Create request
			body, _ := json.Marshal(tt.requestBody)
			req := httptest.NewRequest("POST", "/api/auth/refresh", bytes.NewBuffer(body))
			req.Header.Set("Content-Type", "application/json")
			w := httptest.NewRecorder()

			// Execute
			handler.RefreshToken(w, req)

			// Assert
			assert.Equal(t, tt.expectedStatus, w.Code)

			if tt.expectedError != "" {
				var response map[string]interface{}
				err := json.Unmarshal(w.Body.Bytes(), &response)
				assert.NoError(t, err)
				assert.Contains(t, response["error"], tt.expectedError)
			} else {
				var response map[string]interface{}
				err := json.Unmarshal(w.Body.Bytes(), &response)
				assert.NoError(t, err)
				assert.Equal(t, "Token refreshed successfully", response["message"])
				assert.NotNil(t, response["data"])
			}

			mockAuthService.AssertExpectations(t)
		})
	}
}

func TestAuthHandlerStdlib_Logout(t *testing.T) {
	userID := uuid.New()
	user := &domain.User{
		ID:    userID,
		Email: "<EMAIL>",
		Role:  domain.RoleUser,
	}

	tests := []struct {
		name           string
		setupContext   func(*http.Request) *http.Request
		mockSetup      func(*MockAuthService)
		expectedStatus int
		expectedError  string
	}{
		{
			name: "successful logout",
			setupContext: func(req *http.Request) *http.Request {
				ctx := context.WithValue(req.Context(), "user", user)
				return req.WithContext(ctx)
			},
			mockSetup: func(m *MockAuthService) {
				m.On("Logout", mock.Anything, userID).Return(nil)
			},
			expectedStatus: http.StatusOK,
		},
		{
			name: "missing user context",
			setupContext: func(req *http.Request) *http.Request {
				return req // No user in context
			},
			mockSetup:      func(m *MockAuthService) {},
			expectedStatus: http.StatusUnauthorized,
			expectedError:  "Authentication required",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// Setup
			mockAuthService := new(MockAuthService)
			tt.mockSetup(mockAuthService)
			
			handler := NewAuthHandlerStdlib(mockAuthService)

			// Create request
			req := httptest.NewRequest("POST", "/api/auth/logout", nil)
			req = tt.setupContext(req)
			w := httptest.NewRecorder()

			// Execute
			handler.Logout(w, req)

			// Assert
			assert.Equal(t, tt.expectedStatus, w.Code)

			if tt.expectedError != "" {
				var response map[string]interface{}
				err := json.Unmarshal(w.Body.Bytes(), &response)
				assert.NoError(t, err)
				assert.Contains(t, response["error"], tt.expectedError)
			} else {
				var response map[string]interface{}
				err := json.Unmarshal(w.Body.Bytes(), &response)
				assert.NoError(t, err)
				assert.Equal(t, "Logout successful", response["message"])
			}

			mockAuthService.AssertExpectations(t)
		})
	}
}
