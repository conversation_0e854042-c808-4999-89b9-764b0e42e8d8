package handlers

import (
	"encoding/json"
	"net/http"
	"strconv"

	"goreal-backend/internal/domain"
	"goreal-backend/internal/middleware"

	"github.com/google/uuid"
	"go.opentelemetry.io/otel"
	"go.opentelemetry.io/otel/attribute"
)

var notificationStdlibTracer = otel.Tracer("goreal-backend/handlers/notification")

// NotificationHandlerStdlib handles notification-related HTTP requests using standard library
type NotificationHandlerStdlib struct {
	notificationService domain.NotificationService
}

// NewNotificationHandlerStdlib creates a new notification handler using standard library patterns
func NewNotificationHandlerStdlib(notificationService domain.NotificationService) *NotificationHandlerStdlib {
	return &NotificationHandlerStdlib{
		notificationService: notificationService,
	}
}

// GetUserNotifications retrieves notifications for the current user
func (h *NotificationHandlerStdlib) GetUserNotifications(w http.ResponseWriter, r *http.Request) {
	ctx, span := notificationStdlibTracer.Start(r.Context(), "notificationHandler.GetUserNotifications")
	defer span.End()

	// Get current user from context
	user, err := middleware.GetUserFromContext(r)
	if err != nil {
		span.RecordError(err)
		http.Error(w, `{"error": "Authentication required"}`, http.StatusUnauthorized)
		return
	}

	// Parse query parameters for pagination
	query := r.URL.Query()
	page := 1
	if pageStr := query.Get("page"); pageStr != "" {
		if p, err := strconv.Atoi(pageStr); err == nil && p > 0 {
			page = p
		}
	}

	limit := 20
	if limitStr := query.Get("limit"); limitStr != "" {
		if l, err := strconv.Atoi(limitStr); err == nil && l > 0 && l <= 100 {
			limit = l
		}
	}

	// Get notifications
	notifications, err := h.notificationService.GetByUser(ctx, user.ID)
	if err != nil {
		span.RecordError(err)
		http.Error(w, `{"error": "Failed to retrieve notifications"}`, http.StatusInternalServerError)
		return
	}

	span.SetAttributes(
		attribute.String("user.id", user.ID.String()),
		attribute.Int("notifications.count", len(notifications)),
		attribute.Int("page", page),
		attribute.Int("limit", limit),
	)

	w.Header().Set("Content-Type", "application/json")
	w.WriteHeader(http.StatusOK)
	json.NewEncoder(w).Encode(map[string]interface{}{
		"data":  notifications,
		"page":  page,
		"limit": limit,
	})
}

// CreateNotification creates a new notification (admin only)
func (h *NotificationHandlerStdlib) CreateNotification(w http.ResponseWriter, r *http.Request) {
	ctx, span := notificationStdlibTracer.Start(r.Context(), "notificationHandler.CreateNotification")
	defer span.End()

	var req domain.CreateNotificationRequest
	if err := json.NewDecoder(r.Body).Decode(&req); err != nil {
		span.RecordError(err)
		http.Error(w, `{"error": "Invalid request format"}`, http.StatusBadRequest)
		return
	}

	// Create notification
	notification, err := h.notificationService.Create(ctx, &req)
	if err != nil {
		span.RecordError(err)
		http.Error(w, `{"error": "Failed to create notification"}`, http.StatusBadRequest)
		return
	}

	span.SetAttributes(
		attribute.String("notification.id", notification.ID.String()),
		attribute.String("notification.type", string(notification.Type)),
		attribute.String("user.id", req.UserID.String()),
	)

	w.Header().Set("Content-Type", "application/json")
	w.WriteHeader(http.StatusCreated)
	json.NewEncoder(w).Encode(map[string]interface{}{
		"message": "Notification created successfully",
		"data":    notification,
	})
}

// MarkAsRead marks a notification as read
func (h *NotificationHandlerStdlib) MarkAsRead(w http.ResponseWriter, r *http.Request) {
	ctx, span := notificationStdlibTracer.Start(r.Context(), "notificationHandler.MarkAsRead")
	defer span.End()

	// Extract notification ID from path
	idStr := r.PathValue("id")
	if idStr == "" {
		span.SetAttributes(attribute.String("error", "missing_notification_id"))
		http.Error(w, `{"error": "Notification ID is required"}`, http.StatusBadRequest)
		return
	}

	id, err := uuid.Parse(idStr)
	if err != nil {
		span.RecordError(err)
		http.Error(w, `{"error": "Invalid notification ID"}`, http.StatusBadRequest)
		return
	}

	// Mark as read
	if err := h.notificationService.MarkAsRead(ctx, id); err != nil {
		span.RecordError(err)
		http.Error(w, `{"error": "Failed to mark notification as read"}`, http.StatusInternalServerError)
		return
	}

	span.SetAttributes(attribute.String("notification.id", id.String()))

	w.Header().Set("Content-Type", "application/json")
	w.WriteHeader(http.StatusOK)
	json.NewEncoder(w).Encode(map[string]interface{}{
		"message": "Notification marked as read",
	})
}

// MarkAllAsRead marks all notifications as read for the current user
func (h *NotificationHandlerStdlib) MarkAllAsRead(w http.ResponseWriter, r *http.Request) {
	ctx, span := notificationStdlibTracer.Start(r.Context(), "notificationHandler.MarkAllAsRead")
	defer span.End()

	// Get current user from context
	user, err := middleware.GetUserFromContext(r)
	if err != nil {
		span.RecordError(err)
		http.Error(w, `{"error": "Authentication required"}`, http.StatusUnauthorized)
		return
	}

	if err := h.notificationService.MarkAllAsRead(ctx, user.ID); err != nil {
		span.RecordError(err)
		http.Error(w, `{"error": "Failed to mark all notifications as read"}`, http.StatusInternalServerError)
		return
	}

	span.SetAttributes(attribute.String("user.id", user.ID.String()))

	w.Header().Set("Content-Type", "application/json")
	w.WriteHeader(http.StatusOK)
	json.NewEncoder(w).Encode(map[string]interface{}{
		"message": "All notifications marked as read",
	})
}

// GetUnreadCount gets the count of unread notifications for the current user
func (h *NotificationHandlerStdlib) GetUnreadCount(w http.ResponseWriter, r *http.Request) {
	ctx, span := notificationStdlibTracer.Start(r.Context(), "notificationHandler.GetUnreadCount")
	defer span.End()

	// Get current user from context
	user, err := middleware.GetUserFromContext(r)
	if err != nil {
		span.RecordError(err)
		http.Error(w, `{"error": "Authentication required"}`, http.StatusUnauthorized)
		return
	}

	count, err := h.notificationService.GetUnreadCount(ctx, user.ID)
	if err != nil {
		span.RecordError(err)
		http.Error(w, `{"error": "Failed to get unread count"}`, http.StatusInternalServerError)
		return
	}

	span.SetAttributes(
		attribute.String("user.id", user.ID.String()),
		attribute.Int("unread.count", count),
	)

	w.Header().Set("Content-Type", "application/json")
	w.WriteHeader(http.StatusOK)
	json.NewEncoder(w).Encode(map[string]interface{}{
		"unread_count": count,
	})
}

// SendBulkNotification sends notifications to multiple users (admin only)
func (h *NotificationHandlerStdlib) SendBulkNotification(w http.ResponseWriter, r *http.Request) {
	ctx, span := notificationStdlibTracer.Start(r.Context(), "notificationHandler.SendBulkNotification")
	defer span.End()

	var req domain.BulkNotificationRequest
	if err := json.NewDecoder(r.Body).Decode(&req); err != nil {
		span.RecordError(err)
		http.Error(w, `{"error": "Invalid request format"}`, http.StatusBadRequest)
		return
	}

	// Send bulk notification
	if err := h.notificationService.SendBulkNotification(ctx, &req); err != nil {
		span.RecordError(err)
		http.Error(w, `{"error": "Failed to send bulk notification"}`, http.StatusInternalServerError)
		return
	}

	span.SetAttributes(
		attribute.Int("users.count", len(req.UserIDs)),
		attribute.String("notification.type", req.Type),
	)

	w.Header().Set("Content-Type", "application/json")
	w.WriteHeader(http.StatusOK)
	json.NewEncoder(w).Encode(map[string]interface{}{
		"message": "Bulk notification sent successfully",
	})
}

// SendEmailNotification sends an email notification (admin only)
func (h *NotificationHandlerStdlib) SendEmailNotification(w http.ResponseWriter, r *http.Request) {
	ctx, span := notificationStdlibTracer.Start(r.Context(), "notificationHandler.SendEmailNotification")
	defer span.End()

	var req domain.EmailNotificationRequest
	if err := json.NewDecoder(r.Body).Decode(&req); err != nil {
		span.RecordError(err)
		http.Error(w, `{"error": "Invalid request format"}`, http.StatusBadRequest)
		return
	}

	// Send email notification
	if err := h.notificationService.SendEmailNotification(ctx, &req); err != nil {
		span.RecordError(err)
		http.Error(w, `{"error": "Failed to send email notification"}`, http.StatusInternalServerError)
		return
	}

	span.SetAttributes(
		attribute.StringSlice("email.to", req.To),
		attribute.String("email.subject", req.Subject),
	)

	w.Header().Set("Content-Type", "application/json")
	w.WriteHeader(http.StatusOK)
	json.NewEncoder(w).Encode(map[string]interface{}{
		"message": "Email notification sent successfully",
	})
}

// SendPushNotification sends a push notification (admin only)
func (h *NotificationHandlerStdlib) SendPushNotification(w http.ResponseWriter, r *http.Request) {
	ctx, span := notificationStdlibTracer.Start(r.Context(), "notificationHandler.SendPushNotification")
	defer span.End()

	var req domain.PushNotificationRequest
	if err := json.NewDecoder(r.Body).Decode(&req); err != nil {
		span.RecordError(err)
		http.Error(w, `{"error": "Invalid request format"}`, http.StatusBadRequest)
		return
	}

	// Send push notification
	if err := h.notificationService.SendPushNotification(ctx, &req); err != nil {
		span.RecordError(err)
		http.Error(w, `{"error": "Failed to send push notification"}`, http.StatusInternalServerError)
		return
	}

	span.SetAttributes(
		attribute.String("push.title", req.Title),
		attribute.String("push.body", req.Body),
	)

	w.Header().Set("Content-Type", "application/json")
	w.WriteHeader(http.StatusOK)
	json.NewEncoder(w).Encode(map[string]interface{}{
		"message": "Push notification sent successfully",
	})
}
