package handlers

import (
	"encoding/json"
	"net/http"

	"goreal-backend/internal/domain"
	"goreal-backend/internal/middleware"

	"go.opentelemetry.io/otel"
	"go.opentelemetry.io/otel/attribute"
)

var authStdlibTracer = otel.Tracer("goreal-backend/handlers/auth")

// AuthHandlerStdlib handles authentication-related HTTP requests using standard library
type AuthHandlerStdlib struct {
	authService domain.AuthService
}

// NewAuthHandlerStdlib creates a new auth handler using standard library patterns
func NewAuthHandlerStdlib(authService domain.AuthService) *AuthHandlerStdlib {
	return &AuthHandlerStdlib{
		authService: authService,
	}
}

// Login handles user login
func (h *AuthHandlerStdlib) Login(w http.ResponseWriter, r *http.Request) {
	ctx, span := authStdlibTracer.Start(r.Context(), "authHandler.Login")
	defer span.End()

	var req struct {
		Email    string `json:"email"`
		Password string `json:"password"`
	}

	if err := json.NewDecoder(r.Body).Decode(&req); err != nil {
		span.RecordError(err)
		http.Error(w, `{"error": "Invalid request format"}`, http.StatusBadRequest)
		return
	}

	// Basic validation
	if req.Email == "" || req.Password == "" {
		span.SetAttributes(attribute.String("error", "missing_credentials"))
		http.Error(w, `{"error": "Email and password are required"}`, http.StatusBadRequest)
		return
	}

	// Authenticate user
	authResponse, err := h.authService.Login(ctx, req.Email, req.Password)
	if err != nil {
		span.RecordError(err)
		http.Error(w, `{"error": "Authentication failed"}`, http.StatusUnauthorized)
		return
	}

	span.SetAttributes(
		attribute.String("user.id", authResponse.User.ID.String()),
		attribute.String("user.email", authResponse.User.Email),
		attribute.String("user.role", string(authResponse.User.Role)),
	)

	w.Header().Set("Content-Type", "application/json")
	w.WriteHeader(http.StatusOK)
	json.NewEncoder(w).Encode(map[string]interface{}{
		"message": "Login successful",
		"data":    authResponse,
	})
}

// Register handles user registration
func (h *AuthHandlerStdlib) Register(w http.ResponseWriter, r *http.Request) {
	ctx, span := authStdlibTracer.Start(r.Context(), "authHandler.Register")
	defer span.End()

	var req domain.RegisterRequest

	if err := json.NewDecoder(r.Body).Decode(&req); err != nil {
		span.RecordError(err)
		http.Error(w, `{"error": "Invalid request format"}`, http.StatusBadRequest)
		return
	}

	// Register user
	authResponse, err := h.authService.Register(ctx, &req)
	if err != nil {
		span.RecordError(err)
		http.Error(w, `{"error": "Registration failed"}`, http.StatusBadRequest)
		return
	}

	span.SetAttributes(
		attribute.String("user.id", authResponse.User.ID.String()),
		attribute.String("user.email", authResponse.User.Email),
		attribute.String("user.role", string(authResponse.User.Role)),
	)

	w.Header().Set("Content-Type", "application/json")
	w.WriteHeader(http.StatusCreated)
	json.NewEncoder(w).Encode(map[string]interface{}{
		"message": "Registration successful",
		"data":    authResponse,
	})
}

// RefreshToken handles token refresh
func (h *AuthHandlerStdlib) RefreshToken(w http.ResponseWriter, r *http.Request) {
	ctx, span := authStdlibTracer.Start(r.Context(), "authHandler.RefreshToken")
	defer span.End()

	var req struct {
		RefreshToken string `json:"refresh_token"`
	}

	if err := json.NewDecoder(r.Body).Decode(&req); err != nil {
		span.RecordError(err)
		http.Error(w, `{"error": "Invalid request format"}`, http.StatusBadRequest)
		return
	}

	if req.RefreshToken == "" {
		span.SetAttributes(attribute.String("error", "missing_refresh_token"))
		http.Error(w, `{"error": "Refresh token is required"}`, http.StatusBadRequest)
		return
	}

	// Refresh token
	authResponse, err := h.authService.RefreshToken(ctx, req.RefreshToken)
	if err != nil {
		span.RecordError(err)
		http.Error(w, `{"error": "Token refresh failed"}`, http.StatusUnauthorized)
		return
	}

	span.SetAttributes(
		attribute.String("user.id", authResponse.User.ID.String()),
	)

	w.Header().Set("Content-Type", "application/json")
	w.WriteHeader(http.StatusOK)
	json.NewEncoder(w).Encode(map[string]interface{}{
		"message": "Token refreshed successfully",
		"data":    authResponse,
	})
}

// Logout handles user logout
func (h *AuthHandlerStdlib) Logout(w http.ResponseWriter, r *http.Request) {
	ctx, span := authStdlibTracer.Start(r.Context(), "authHandler.Logout")
	defer span.End()

	// Get user from context (set by auth middleware)
	user, err := middleware.GetUserFromContext(r)
	if err != nil {
		span.RecordError(err)
		http.Error(w, `{"error": "Authentication required"}`, http.StatusUnauthorized)
		return
	}

	// Logout user
	if err := h.authService.Logout(ctx, user.ID); err != nil {
		span.RecordError(err)
		http.Error(w, `{"error": "Logout failed"}`, http.StatusInternalServerError)
		return
	}

	span.SetAttributes(
		attribute.String("user.id", user.ID.String()),
	)

	w.Header().Set("Content-Type", "application/json")
	w.WriteHeader(http.StatusOK)
	json.NewEncoder(w).Encode(map[string]interface{}{
		"message": "Logout successful",
	})
}

// ResetPassword handles password reset request
func (h *AuthHandlerStdlib) ResetPassword(w http.ResponseWriter, r *http.Request) {
	ctx, span := authStdlibTracer.Start(r.Context(), "authHandler.ResetPassword")
	defer span.End()

	var req struct {
		Email string `json:"email"`
	}

	if err := json.NewDecoder(r.Body).Decode(&req); err != nil {
		span.RecordError(err)
		http.Error(w, `{"error": "Invalid request format"}`, http.StatusBadRequest)
		return
	}

	if req.Email == "" {
		span.SetAttributes(attribute.String("error", "missing_email"))
		http.Error(w, `{"error": "Email is required"}`, http.StatusBadRequest)
		return
	}

	// Request password reset
	if err := h.authService.ResetPassword(ctx, req.Email); err != nil {
		span.RecordError(err)
		// Don't reveal if email exists for security
	}

	span.SetAttributes(
		attribute.String("email", req.Email),
	)

	w.Header().Set("Content-Type", "application/json")
	w.WriteHeader(http.StatusOK)
	json.NewEncoder(w).Encode(map[string]interface{}{
		"message": "If the email exists, a password reset link has been sent",
	})
}

// ConfirmPasswordReset handles password reset confirmation
func (h *AuthHandlerStdlib) ConfirmPasswordReset(w http.ResponseWriter, r *http.Request) {
	ctx, span := authStdlibTracer.Start(r.Context(), "authHandler.ConfirmPasswordReset")
	defer span.End()

	var req struct {
		Token       string `json:"token"`
		NewPassword string `json:"new_password"`
	}

	if err := json.NewDecoder(r.Body).Decode(&req); err != nil {
		span.RecordError(err)
		http.Error(w, `{"error": "Invalid request format"}`, http.StatusBadRequest)
		return
	}

	if req.Token == "" || req.NewPassword == "" {
		span.SetAttributes(attribute.String("error", "missing_required_fields"))
		http.Error(w, `{"error": "Token and new password are required"}`, http.StatusBadRequest)
		return
	}

	// Confirm password reset
	if err := h.authService.ConfirmPasswordReset(ctx, req.Token, req.NewPassword); err != nil {
		span.RecordError(err)
		http.Error(w, `{"error": "Password reset failed"}`, http.StatusBadRequest)
		return
	}

	w.Header().Set("Content-Type", "application/json")
	w.WriteHeader(http.StatusOK)
	json.NewEncoder(w).Encode(map[string]interface{}{
		"message": "Password reset successful",
	})
}
