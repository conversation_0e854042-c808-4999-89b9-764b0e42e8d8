package handlers

import (
	"encoding/json"
	"net/http"
	"strconv"

	"goreal-backend/internal/domain"
	"goreal-backend/internal/middleware"

	"github.com/google/uuid"
	"go.opentelemetry.io/otel"
	"go.opentelemetry.io/otel/attribute"
)

var taskStdlibTracer = otel.Tracer("goreal-backend/handlers/task")

// TaskHandlerStdlib handles task-related HTTP requests using standard library
type TaskHandlerStdlib struct {
	taskService domain.TaskService
}

// NewTaskHandlerStdlib creates a new task handler using standard library patterns
func NewTaskHandlerStdlib(taskService domain.TaskService) *TaskHandlerStdlib {
	return &TaskHandlerStdlib{
		taskService: taskService,
	}
}

// ListTasks retrieves a list of tasks with optional filtering
func (h *TaskHandlerStdlib) ListTasks(w http.ResponseWriter, r *http.Request) {
	ctx, span := taskStdlibTracer.Start(r.Context(), "taskHandler.ListTasks")
	defer span.End()

	// Parse query parameters for filtering
	query := r.URL.Query()
	
	// Parse pagination parameters
	page := 1
	if pageStr := query.Get("page"); pageStr != "" {
		if p, err := strconv.Atoi(pageStr); err == nil && p > 0 {
			page = p
		}
	}

	limit := 20
	if limitStr := query.Get("limit"); limitStr != "" {
		if l, err := strconv.Atoi(limitStr); err == nil && l > 0 && l <= 100 {
			limit = l
		}
	}

	// Create filter from query parameters
	filter := domain.TaskFilters{
		BaseFilters: domain.BaseFilters{
			Limit:  limit,
			Offset: (page - 1) * limit,
		},
	}

	// Add status filter if provided
	if status := query.Get("status"); status != "" {
		// Convert string to TaskStatus
		taskStatus := domain.TaskStatus(status)
		filter.Status = &taskStatus
	}

	// Get tasks
	tasks, err := h.taskService.List(ctx, filter)
	if err != nil {
		span.RecordError(err)
		http.Error(w, `{"error": "Failed to retrieve tasks"}`, http.StatusInternalServerError)
		return
	}

	span.SetAttributes(
		attribute.Int("tasks.count", len(tasks)),
		attribute.Int("page", page),
		attribute.Int("limit", limit),
	)

	w.Header().Set("Content-Type", "application/json")
	w.WriteHeader(http.StatusOK)
	json.NewEncoder(w).Encode(map[string]interface{}{
		"data":  tasks,
		"page":  page,
		"limit": limit,
	})
}

// CreateTask creates a new task
func (h *TaskHandlerStdlib) CreateTask(w http.ResponseWriter, r *http.Request) {
	ctx, span := taskStdlibTracer.Start(r.Context(), "taskHandler.CreateTask")
	defer span.End()

	var req domain.CreateTaskRequest
	if err := json.NewDecoder(r.Body).Decode(&req); err != nil {
		span.RecordError(err)
		http.Error(w, `{"error": "Invalid request format"}`, http.StatusBadRequest)
		return
	}

	// Get current user from context (set by auth middleware)
	user, err := middleware.GetUserFromContext(r)
	if err != nil {
		span.RecordError(err)
		http.Error(w, `{"error": "Authentication required"}`, http.StatusUnauthorized)
		return
	}

	// Note: CreateTaskRequest doesn't have CreatedBy field, it will be set by the service layer

	task, err := h.taskService.Create(ctx, &req)
	if err != nil {
		span.RecordError(err)
		http.Error(w, `{"error": "Failed to create task"}`, http.StatusBadRequest)
		return
	}

	span.SetAttributes(
		attribute.String("task.id", task.ID.String()),
		attribute.String("task.title", task.Title),
		attribute.String("created_by", user.ID.String()),
	)

	w.Header().Set("Content-Type", "application/json")
	w.WriteHeader(http.StatusCreated)
	json.NewEncoder(w).Encode(map[string]interface{}{
		"message": "Task created successfully",
		"data":    task,
	})
}

// GetTask retrieves a task by ID using Go 1.22+ path parameters
func (h *TaskHandlerStdlib) GetTask(w http.ResponseWriter, r *http.Request) {
	ctx, span := taskStdlibTracer.Start(r.Context(), "taskHandler.GetTask")
	defer span.End()

	// Extract ID from path using Go 1.22+ path value
	idStr := r.PathValue("id")
	if idStr == "" {
		span.SetAttributes(attribute.String("error", "missing_task_id"))
		http.Error(w, `{"error": "Task ID is required"}`, http.StatusBadRequest)
		return
	}

	id, err := uuid.Parse(idStr)
	if err != nil {
		span.RecordError(err)
		http.Error(w, `{"error": "Invalid task ID"}`, http.StatusBadRequest)
		return
	}

	task, err := h.taskService.GetByID(ctx, id)
	if err != nil {
		span.RecordError(err)
		http.Error(w, `{"error": "Task not found"}`, http.StatusNotFound)
		return
	}

	span.SetAttributes(attribute.String("task.id", id.String()))

	w.Header().Set("Content-Type", "application/json")
	w.WriteHeader(http.StatusOK)
	json.NewEncoder(w).Encode(map[string]interface{}{
		"data": task,
	})
}

// UpdateTask updates an existing task
func (h *TaskHandlerStdlib) UpdateTask(w http.ResponseWriter, r *http.Request) {
	ctx, span := taskStdlibTracer.Start(r.Context(), "taskHandler.UpdateTask")
	defer span.End()

	// Extract ID from path
	idStr := r.PathValue("id")
	if idStr == "" {
		span.SetAttributes(attribute.String("error", "missing_task_id"))
		http.Error(w, `{"error": "Task ID is required"}`, http.StatusBadRequest)
		return
	}

	id, err := uuid.Parse(idStr)
	if err != nil {
		span.RecordError(err)
		http.Error(w, `{"error": "Invalid task ID"}`, http.StatusBadRequest)
		return
	}

	var req domain.UpdateTaskRequest
	if err := json.NewDecoder(r.Body).Decode(&req); err != nil {
		span.RecordError(err)
		http.Error(w, `{"error": "Invalid request format"}`, http.StatusBadRequest)
		return
	}

	task, err := h.taskService.Update(ctx, id, &req)
	if err != nil {
		span.RecordError(err)
		http.Error(w, `{"error": "Failed to update task"}`, http.StatusBadRequest)
		return
	}

	span.SetAttributes(
		attribute.String("task.id", id.String()),
		attribute.String("task.title", task.Title),
	)

	w.Header().Set("Content-Type", "application/json")
	w.WriteHeader(http.StatusOK)
	json.NewEncoder(w).Encode(map[string]interface{}{
		"message": "Task updated successfully",
		"data":    task,
	})
}

// DeleteTask deletes a task by ID
func (h *TaskHandlerStdlib) DeleteTask(w http.ResponseWriter, r *http.Request) {
	ctx, span := taskStdlibTracer.Start(r.Context(), "taskHandler.DeleteTask")
	defer span.End()

	// Extract ID from path
	idStr := r.PathValue("id")
	if idStr == "" {
		span.SetAttributes(attribute.String("error", "missing_task_id"))
		http.Error(w, `{"error": "Task ID is required"}`, http.StatusBadRequest)
		return
	}

	id, err := uuid.Parse(idStr)
	if err != nil {
		span.RecordError(err)
		http.Error(w, `{"error": "Invalid task ID"}`, http.StatusBadRequest)
		return
	}

	if err := h.taskService.Delete(ctx, id); err != nil {
		span.RecordError(err)
		http.Error(w, `{"error": "Failed to delete task"}`, http.StatusBadRequest)
		return
	}

	span.SetAttributes(attribute.String("task.id", id.String()))

	w.Header().Set("Content-Type", "application/json")
	w.WriteHeader(http.StatusOK)
	json.NewEncoder(w).Encode(map[string]interface{}{
		"message": "Task deleted successfully",
	})
}

// CompleteTask marks a task as completed
func (h *TaskHandlerStdlib) CompleteTask(w http.ResponseWriter, r *http.Request) {
	ctx, span := taskStdlibTracer.Start(r.Context(), "taskHandler.CompleteTask")
	defer span.End()

	// Extract ID from path
	idStr := r.PathValue("id")
	if idStr == "" {
		span.SetAttributes(attribute.String("error", "missing_task_id"))
		http.Error(w, `{"error": "Task ID is required"}`, http.StatusBadRequest)
		return
	}

	taskID, err := uuid.Parse(idStr)
	if err != nil {
		span.RecordError(err)
		http.Error(w, `{"error": "Invalid task ID"}`, http.StatusBadRequest)
		return
	}

	var req struct {
		Notes string `json:"notes"`
	}

	// Notes are optional
	json.NewDecoder(r.Body).Decode(&req)

	if err := h.taskService.CompleteTask(ctx, taskID, req.Notes); err != nil {
		span.RecordError(err)
		http.Error(w, `{"error": "Failed to complete task"}`, http.StatusBadRequest)
		return
	}

	span.SetAttributes(
		attribute.String("task.id", taskID.String()),
		attribute.Bool("task.completed", true),
	)

	w.Header().Set("Content-Type", "application/json")
	w.WriteHeader(http.StatusOK)
	json.NewEncoder(w).Encode(map[string]interface{}{
		"message": "Task completed successfully",
	})
}

// GetTasksByUser gets tasks assigned to a specific user
func (h *TaskHandlerStdlib) GetTasksByUser(w http.ResponseWriter, r *http.Request) {
	ctx, span := taskStdlibTracer.Start(r.Context(), "taskHandler.GetTasksByUser")
	defer span.End()

	// Extract user ID from path
	userIDStr := r.PathValue("userID")
	if userIDStr == "" {
		span.SetAttributes(attribute.String("error", "missing_user_id"))
		http.Error(w, `{"error": "User ID is required"}`, http.StatusBadRequest)
		return
	}

	userID, err := uuid.Parse(userIDStr)
	if err != nil {
		span.RecordError(err)
		http.Error(w, `{"error": "Invalid user ID"}`, http.StatusBadRequest)
		return
	}

	tasks, err := h.taskService.GetByAssignedUser(ctx, userID)
	if err != nil {
		span.RecordError(err)
		http.Error(w, `{"error": "Failed to retrieve user tasks"}`, http.StatusInternalServerError)
		return
	}

	span.SetAttributes(
		attribute.String("user.id", userID.String()),
		attribute.Int("tasks.count", len(tasks)),
	)

	w.Header().Set("Content-Type", "application/json")
	w.WriteHeader(http.StatusOK)
	json.NewEncoder(w).Encode(map[string]interface{}{
		"data": tasks,
	})
}

// GetOverdueTasks gets all overdue tasks
func (h *TaskHandlerStdlib) GetOverdueTasks(w http.ResponseWriter, r *http.Request) {
	ctx, span := taskStdlibTracer.Start(r.Context(), "taskHandler.GetOverdueTasks")
	defer span.End()

	tasks, err := h.taskService.GetOverdueTasks(ctx)
	if err != nil {
		span.RecordError(err)
		http.Error(w, `{"error": "Failed to retrieve overdue tasks"}`, http.StatusInternalServerError)
		return
	}

	span.SetAttributes(
		attribute.Int("overdue_tasks.count", len(tasks)),
	)

	w.Header().Set("Content-Type", "application/json")
	w.WriteHeader(http.StatusOK)
	json.NewEncoder(w).Encode(map[string]interface{}{
		"data": tasks,
	})
}

// BulkAssignTasks assigns multiple tasks to a user
func (h *TaskHandlerStdlib) BulkAssignTasks(w http.ResponseWriter, r *http.Request) {
	ctx, span := taskStdlibTracer.Start(r.Context(), "taskHandler.BulkAssignTasks")
	defer span.End()

	var req struct {
		TaskIDs []string `json:"task_ids"`
		UserID  string   `json:"user_id"`
	}

	if err := json.NewDecoder(r.Body).Decode(&req); err != nil {
		span.RecordError(err)
		http.Error(w, `{"error": "Invalid request format"}`, http.StatusBadRequest)
		return
	}

	// Parse user ID
	userID, err := uuid.Parse(req.UserID)
	if err != nil {
		span.RecordError(err)
		http.Error(w, `{"error": "Invalid user ID"}`, http.StatusBadRequest)
		return
	}

	// Parse task IDs
	taskIDs := make([]uuid.UUID, 0, len(req.TaskIDs))
	for _, idStr := range req.TaskIDs {
		id, err := uuid.Parse(idStr)
		if err != nil {
			span.RecordError(err)
			http.Error(w, `{"error": "Invalid task ID format"}`, http.StatusBadRequest)
			return
		}
		taskIDs = append(taskIDs, id)
	}

	// Perform bulk assignment
	successCount := 0
	errors := make([]string, 0)

	for _, taskID := range taskIDs {
		updateReq := &domain.UpdateTaskRequest{
			AssignedTo: &userID,
		}

		if _, err := h.taskService.Update(ctx, taskID, updateReq); err != nil {
			errors = append(errors, err.Error())
		} else {
			successCount++
		}
	}

	span.SetAttributes(
		attribute.String("user.id", userID.String()),
		attribute.Int("tasks.assigned", successCount),
		attribute.Int("tasks.failed", len(errors)),
		attribute.Int("tasks.total", len(taskIDs)),
	)

	response := map[string]interface{}{
		"message":  "Bulk assignment completed",
		"assigned": successCount,
		"total":    len(taskIDs),
	}

	if len(errors) > 0 {
		response["errors"] = errors
	}

	w.Header().Set("Content-Type", "application/json")
	w.WriteHeader(http.StatusOK)
	json.NewEncoder(w).Encode(response)
}
