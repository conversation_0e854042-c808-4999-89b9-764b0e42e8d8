// ============================================================================
// ENHANCED AUTHENTICATION MIDDLEWARE WITH SECURITY FEATURES
// ============================================================================

package middleware

import (
	"context"
	"encoding/json"
	"fmt"
	"net/http"
	"strings"
	"time"

	"goreal-backend/internal/domain"
	"goreal-backend/pkg/audit"
	"goreal-backend/pkg/jwt"

	"go.opentelemetry.io/otel"
	"go.opentelemetry.io/otel/attribute"
)

var enhancedAuthTracer = otel.Tracer("goreal-backend/middleware/enhanced-auth")

// ============================================================================
// ENHANCED AUTH MIDDLEWARE
// ============================================================================

// EnhancedAuthRequired provides comprehensive authentication with security features
func EnhancedAuthRequired(jwtManager *jwt.JWTManager, auditLogger audit.Logger) func(http.Handler) http.Handler {
	return func(next http.Handler) http.Handler {
		return http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
			ctx, span := enhancedAuthTracer.Start(r.Context(), "middleware.EnhancedAuthRequired")
			defer span.End()

			start := time.Now()

			// Extract token from Authorization header
			authHeader := r.Header.Get("Authorization")
			if authHeader == "" {
				span.SetAttributes(attribute.String("auth.error", "missing_authorization_header"))
				auditLogger.LogSecurityEvent(ctx, audit.EventSecurityViolation, 
					"Missing authorization header", 
					map[string]interface{}{
						"ip_address": getClientIP(r),
						"user_agent": r.UserAgent(),
						"path": r.URL.Path,
					})
				writeJSONError(w, "Authorization header required", http.StatusUnauthorized)
				return
			}

			// Check if it's a Bearer token
			parts := strings.SplitN(authHeader, " ", 2)
			if len(parts) != 2 || parts[0] != "Bearer" {
				span.SetAttributes(attribute.String("auth.error", "invalid_authorization_format"))
				auditLogger.LogSecurityEvent(ctx, audit.EventSecurityViolation,
					"Invalid authorization format",
					map[string]interface{}{
						"ip_address": getClientIP(r),
						"user_agent": r.UserAgent(),
						"auth_header": authHeader,
					})
				writeJSONError(w, "Invalid authorization format", http.StatusUnauthorized)
				return
			}

			token := parts[1]
			if token == "" {
				span.SetAttributes(attribute.String("auth.error", "empty_token"))
				auditLogger.LogSecurityEvent(ctx, audit.EventSecurityViolation,
					"Empty token provided",
					map[string]interface{}{
						"ip_address": getClientIP(r),
						"user_agent": r.UserAgent(),
					})
				writeJSONError(w, "Token required", http.StatusUnauthorized)
				return
			}

			// Validate token with enhanced security
			claims, err := jwtManager.ValidateAccessTokenSecure(ctx, token)
			if err != nil {
				span.SetAttributes(
					attribute.String("auth.error", "token_validation_failed"),
					attribute.String("auth.error_detail", err.Error()),
				)
				
				// Log failed authentication attempt
				auditLogger.LogEvent(ctx, audit.EventLoginFailed, audit.SeverityWarning,
					fmt.Sprintf("Token validation failed: %s", err.Error()),
					map[string]interface{}{
						"ip_address": getClientIP(r),
						"user_agent": r.UserAgent(),
						"error": err.Error(),
						"token_length": len(token),
					})
				
				writeJSONError(w, "Invalid or expired token", http.StatusUnauthorized)
				return
			}

			// Check if user is globally blacklisted
			isUserBlacklisted, err := jwtManager.IsUserBlacklisted(ctx, claims.UserID)
			if err != nil {
				span.RecordError(err)
				// Log error but continue (Redis might be down)
			} else if isUserBlacklisted {
				span.SetAttributes(attribute.String("auth.error", "user_blacklisted"))
				auditLogger.LogSecurityEvent(ctx, audit.EventSecurityViolation,
					"Blacklisted user attempted access",
					map[string]interface{}{
						"user_id": claims.UserID,
						"ip_address": getClientIP(r),
						"user_agent": r.UserAgent(),
					})
				writeJSONError(w, "Access denied", http.StatusForbidden)
				return
			}

			// Add user information to context
			ctx = context.WithValue(ctx, "user_id", claims.UserID)
			ctx = context.WithValue(ctx, "user_email", claims.Email)
			ctx = context.WithValue(ctx, "user_role", claims.Role)
			ctx = context.WithValue(ctx, "user_username", claims.Username)
			ctx = context.WithValue(ctx, "token_id", claims.ID)

			// Set span attributes for observability
			span.SetAttributes(
				attribute.String("auth.user_id", claims.UserID),
				attribute.String("auth.user_email", claims.Email),
				attribute.String("auth.user_role", claims.Role),
				attribute.String("auth.status", "success"),
				attribute.Int64("auth.duration_ms", time.Since(start).Milliseconds()),
			)

			// Log successful authentication (only for sensitive operations)
			if isSensitiveOperation(r) {
				auditLogger.LogEvent(ctx, audit.EventDataAccess, audit.SeverityInfo,
					fmt.Sprintf("Authenticated access to %s", r.URL.Path),
					map[string]interface{}{
						"user_id": claims.UserID,
						"user_email": claims.Email,
						"method": r.Method,
						"path": r.URL.Path,
						"ip_address": getClientIP(r),
					})
			}

			// Continue with the request
			next.ServeHTTP(w, r.WithContext(ctx))
		})
	}
}

// ============================================================================
// ROLE-BASED ACCESS CONTROL MIDDLEWARE
// ============================================================================

// RequireRole ensures the user has one of the specified roles
func RequireRole(auditLogger audit.Logger, roles ...string) func(http.Handler) http.Handler {
	return func(next http.Handler) http.Handler {
		return http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
			ctx, span := enhancedAuthTracer.Start(r.Context(), "middleware.RequireRole")
			defer span.End()

			userRole := ctx.Value("user_role")
			if userRole == nil {
				span.SetAttributes(attribute.String("rbac.error", "no_user_role"))
				writeJSONError(w, "Authentication required", http.StatusUnauthorized)
				return
			}

			userRoleStr := userRole.(string)
			hasRole := false
			for _, role := range roles {
				if userRoleStr == role {
					hasRole = true
					break
				}
			}

			if !hasRole {
				span.SetAttributes(
					attribute.String("rbac.error", "insufficient_permissions"),
					attribute.String("rbac.user_role", userRoleStr),
					attribute.StringSlice("rbac.required_roles", roles),
				)

				userID := ctx.Value("user_id")
				auditLogger.LogSecurityEvent(ctx, audit.EventSecurityViolation,
					fmt.Sprintf("Insufficient permissions: user role '%s' not in required roles %v", userRoleStr, roles),
					map[string]interface{}{
						"user_id": userID,
						"user_role": userRoleStr,
						"required_roles": roles,
						"path": r.URL.Path,
						"method": r.Method,
					})

				writeJSONError(w, "Insufficient permissions", http.StatusForbidden)
				return
			}

			span.SetAttributes(
				attribute.String("rbac.status", "authorized"),
				attribute.String("rbac.user_role", userRoleStr),
			)

			next.ServeHTTP(w, r)
		})
	}
}

// ============================================================================
// OPTIONAL AUTH MIDDLEWARE
// ============================================================================

// OptionalAuth provides optional authentication (doesn't fail if no token)
func OptionalAuth(jwtManager *jwt.JWTManager) func(http.Handler) http.Handler {
	return func(next http.Handler) http.Handler {
		return http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
			ctx, span := enhancedAuthTracer.Start(r.Context(), "middleware.OptionalAuth")
			defer span.End()

			// Extract token from Authorization header
			authHeader := r.Header.Get("Authorization")
			if authHeader == "" {
				// No token provided, continue without authentication
				span.SetAttributes(attribute.String("auth.status", "no_token"))
				next.ServeHTTP(w, r)
				return
			}

			// Check if it's a Bearer token
			parts := strings.SplitN(authHeader, " ", 2)
			if len(parts) != 2 || parts[0] != "Bearer" {
				// Invalid format, continue without authentication
				span.SetAttributes(attribute.String("auth.status", "invalid_format"))
				next.ServeHTTP(w, r)
				return
			}

			token := parts[1]
			if token == "" {
				// Empty token, continue without authentication
				span.SetAttributes(attribute.String("auth.status", "empty_token"))
				next.ServeHTTP(w, r)
				return
			}

			// Try to validate token
			claims, err := jwtManager.ValidateAccessTokenSecure(ctx, token)
			if err != nil {
				// Invalid token, continue without authentication
				span.SetAttributes(
					attribute.String("auth.status", "invalid_token"),
					attribute.String("auth.error", err.Error()),
				)
				next.ServeHTTP(w, r)
				return
			}

			// Valid token, add user information to context
			ctx = context.WithValue(ctx, "user_id", claims.UserID)
			ctx = context.WithValue(ctx, "user_email", claims.Email)
			ctx = context.WithValue(ctx, "user_role", claims.Role)
			ctx = context.WithValue(ctx, "user_username", claims.Username)

			span.SetAttributes(
				attribute.String("auth.status", "authenticated"),
				attribute.String("auth.user_id", claims.UserID),
			)

			next.ServeHTTP(w, r.WithContext(ctx))
		})
	}
}

// ============================================================================
// HELPER FUNCTIONS
// ============================================================================

// writeJSONError writes a JSON error response
func writeJSONError(w http.ResponseWriter, message string, statusCode int) {
	w.Header().Set("Content-Type", "application/json")
	w.WriteHeader(statusCode)
	
	response := map[string]interface{}{
		"error": message,
		"code": getErrorCode(statusCode),
		"timestamp": time.Now().UTC().Format(time.RFC3339),
	}
	
	json.NewEncoder(w).Encode(response)
}

// getErrorCode returns an error code based on status code
func getErrorCode(statusCode int) string {
	switch statusCode {
	case http.StatusUnauthorized:
		return "UNAUTHORIZED"
	case http.StatusForbidden:
		return "FORBIDDEN"
	case http.StatusTooManyRequests:
		return "RATE_LIMIT_EXCEEDED"
	default:
		return "ERROR"
	}
}

// isSensitiveOperation checks if the operation should be audited
func isSensitiveOperation(r *http.Request) bool {
	sensitivePaths := []string{
		"/api/admin",
		"/api/users",
		"/api/auth",
		"/api/analytics",
	}
	
	for _, path := range sensitivePaths {
		if strings.HasPrefix(r.URL.Path, path) {
			return true
		}
	}
	
	// Always audit non-GET requests
	return r.Method != "GET"
}

// getClientIP extracts the real client IP address
func getClientIP(r *http.Request) string {
	// Check X-Forwarded-For header (from load balancers/proxies)
	if xff := r.Header.Get("X-Forwarded-For"); xff != "" {
		ips := strings.Split(xff, ",")
		return strings.TrimSpace(ips[0])
	}
	
	// Check X-Real-IP header (from nginx)
	if xri := r.Header.Get("X-Real-IP"); xri != "" {
		return xri
	}
	
	// Check CF-Connecting-IP header (from Cloudflare)
	if cfip := r.Header.Get("CF-Connecting-IP"); cfip != "" {
		return cfip
	}
	
	// Fall back to RemoteAddr
	ip := r.RemoteAddr
	if colon := strings.LastIndex(ip, ":"); colon != -1 {
		ip = ip[:colon]
	}
	
	return ip
}
