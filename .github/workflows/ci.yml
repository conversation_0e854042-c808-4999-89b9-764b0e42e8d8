# ============================================================================
# COMPREHENSIVE CI/CD PIPELINE FOR GOREAL PLATFORM
# ============================================================================

name: CI/CD Pipeline

on:
  push:
    branches: [ main, develop ]
  pull_request:
    branches: [ main, develop ]
  release:
    types: [ published ]

env:
  GO_VERSION: '1.22'
  NODE_VERSION: '18'
  REGISTRY: ghcr.io
  IMAGE_NAME: ${{ github.repository }}

jobs:
  # ============================================================================
  # CODE QUALITY AND SECURITY CHECKS
  # ============================================================================
  
  code-quality:
    name: Code Quality & Security
    runs-on: ubuntu-latest
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4
      with:
        fetch-depth: 0

    - name: Set up Go
      uses: actions/setup-go@v4
      with:
        go-version: ${{ env.GO_VERSION }}

    - name: Set up Node.js
      uses: actions/setup-node@v4
      with:
        node-version: ${{ env.NODE_VERSION }}
        cache: 'npm'
        cache-dependency-path: client/package-lock.json

    # Backend code quality
    - name: Go mod download
      working-directory: ./backend
      run: go mod download

    - name: Go mod verify
      working-directory: ./backend
      run: go mod verify

    - name: Run golangci-lint
      uses: golangci/golangci-lint-action@v3
      with:
        version: latest
        working-directory: ./backend
        args: --timeout=5m --config=.golangci.yml

    - name: Go vet
      working-directory: ./backend
      run: go vet ./...

    - name: Go fmt check
      working-directory: ./backend
      run: |
        if [ "$(gofmt -s -l . | wc -l)" -gt 0 ]; then
          echo "Code is not formatted properly:"
          gofmt -s -l .
          exit 1
        fi

    # Frontend code quality
    - name: Install frontend dependencies
      working-directory: ./client
      run: npm ci

    - name: Run ESLint
      working-directory: ./client
      run: npm run lint

    - name: Run Prettier check
      working-directory: ./client
      run: npm run format:check

    - name: TypeScript type check
      working-directory: ./client
      run: npm run type-check

    # Security scanning
    - name: Run Gosec Security Scanner
      uses: securecodewarrior/github-action-gosec@master
      with:
        args: '-fmt sarif -out gosec-results.sarif ./backend/...'

    - name: Upload Gosec results to GitHub Security tab
      uses: github/codeql-action/upload-sarif@v2
      if: always()
      with:
        sarif_file: gosec-results.sarif

    - name: Run Trivy vulnerability scanner
      uses: aquasecurity/trivy-action@master
      with:
        scan-type: 'fs'
        scan-ref: '.'
        format: 'sarif'
        output: 'trivy-results.sarif'

    - name: Upload Trivy results to GitHub Security tab
      uses: github/codeql-action/upload-sarif@v2
      if: always()
      with:
        sarif_file: 'trivy-results.sarif'

  # ============================================================================
  # BACKEND TESTING
  # ============================================================================
  
  backend-tests:
    name: Backend Tests
    runs-on: ubuntu-latest
    needs: code-quality
    
    services:
      postgres:
        image: postgres:15
        env:
          POSTGRES_PASSWORD: postgres
          POSTGRES_DB: goreal_test
        options: >-
          --health-cmd pg_isready
          --health-interval 10s
          --health-timeout 5s
          --health-retries 5
        ports:
          - 5432:5432
          
      redis:
        image: redis:7-alpine
        options: >-
          --health-cmd "redis-cli ping"
          --health-interval 10s
          --health-timeout 5s
          --health-retries 5
        ports:
          - 6379:6379

    steps:
    - name: Checkout code
      uses: actions/checkout@v4

    - name: Set up Go
      uses: actions/setup-go@v4
      with:
        go-version: ${{ env.GO_VERSION }}

    - name: Go mod download
      working-directory: ./backend
      run: go mod download

    - name: Run unit tests
      working-directory: ./backend
      env:
        DB_HOST: localhost
        DB_PORT: 5432
        DB_NAME: goreal_test
        DB_USER: postgres
        DB_PASSWORD: postgres
        REDIS_HOST: localhost
        REDIS_PORT: 6379
      run: |
        go test -v -race -coverprofile=coverage.out -covermode=atomic ./...

    - name: Run integration tests
      working-directory: ./backend
      env:
        DB_HOST: localhost
        DB_PORT: 5432
        DB_NAME: goreal_test
        DB_USER: postgres
        DB_PASSWORD: postgres
        REDIS_HOST: localhost
        REDIS_PORT: 6379
      run: |
        go test -v -tags=integration ./test/...

    - name: Run benchmarks
      working-directory: ./backend
      run: |
        go test -bench=. -benchmem ./pkg/performance/

    - name: Upload coverage to Codecov
      uses: codecov/codecov-action@v3
      with:
        file: ./backend/coverage.out
        flags: backend
        name: backend-coverage

  # ============================================================================
  # FRONTEND TESTING
  # ============================================================================
  
  frontend-tests:
    name: Frontend Tests
    runs-on: ubuntu-latest
    needs: code-quality
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4

    - name: Set up Node.js
      uses: actions/setup-node@v4
      with:
        node-version: ${{ env.NODE_VERSION }}
        cache: 'npm'
        cache-dependency-path: client/package-lock.json

    - name: Install dependencies
      working-directory: ./client
      run: npm ci

    - name: Run unit tests
      working-directory: ./client
      run: npm run test:coverage

    - name: Run E2E tests
      working-directory: ./client
      run: npm run test:e2e

    - name: Upload coverage to Codecov
      uses: codecov/codecov-action@v3
      with:
        file: ./client/coverage/lcov.info
        flags: frontend
        name: frontend-coverage

  # ============================================================================
  # BUILD AND CONTAINERIZATION
  # ============================================================================
  
  build:
    name: Build and Push Images
    runs-on: ubuntu-latest
    needs: [backend-tests, frontend-tests]
    if: github.event_name == 'push' || github.event_name == 'release'
    
    permissions:
      contents: read
      packages: write

    strategy:
      matrix:
        component: [backend, frontend]

    steps:
    - name: Checkout code
      uses: actions/checkout@v4

    - name: Set up Docker Buildx
      uses: docker/setup-buildx-action@v3

    - name: Log in to Container Registry
      uses: docker/login-action@v3
      with:
        registry: ${{ env.REGISTRY }}
        username: ${{ github.actor }}
        password: ${{ secrets.GITHUB_TOKEN }}

    - name: Extract metadata
      id: meta
      uses: docker/metadata-action@v5
      with:
        images: ${{ env.REGISTRY }}/${{ env.IMAGE_NAME }}-${{ matrix.component }}
        tags: |
          type=ref,event=branch
          type=ref,event=pr
          type=semver,pattern={{version}}
          type=semver,pattern={{major}}.{{minor}}
          type=sha,prefix={{branch}}-

    - name: Build and push Docker image
      uses: docker/build-push-action@v5
      with:
        context: ./${{ matrix.component }}
        file: ./${{ matrix.component }}/Dockerfile.prod
        push: true
        tags: ${{ steps.meta.outputs.tags }}
        labels: ${{ steps.meta.outputs.labels }}
        cache-from: type=gha
        cache-to: type=gha,mode=max
        platforms: linux/amd64,linux/arm64

  # ============================================================================
  # PERFORMANCE TESTING
  # ============================================================================
  
  performance-tests:
    name: Performance Tests
    runs-on: ubuntu-latest
    needs: build
    if: github.event_name == 'push' && github.ref == 'refs/heads/main'
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4

    - name: Set up Docker Compose
      run: |
        docker-compose -f docker-compose.test.yml up -d
        sleep 30  # Wait for services to be ready

    - name: Run load tests
      run: |
        docker run --rm --network host \
          -v ${{ github.workspace }}/scripts:/scripts \
          loadimpact/k6:latest run /scripts/load-test.js

    - name: Run Lighthouse CI
      uses: treosh/lighthouse-ci-action@v10
      with:
        configPath: './client/lighthouserc.js'
        uploadArtifacts: true
        temporaryPublicStorage: true

    - name: Cleanup
      if: always()
      run: docker-compose -f docker-compose.test.yml down

  # ============================================================================
  # DEPLOYMENT
  # ============================================================================
  
  deploy-staging:
    name: Deploy to Staging
    runs-on: ubuntu-latest
    needs: [build, performance-tests]
    if: github.ref == 'refs/heads/develop'
    environment: staging
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4

    - name: Deploy to staging
      run: |
        echo "Deploying to staging environment..."
        # Add your staging deployment commands here
        # Example: kubectl apply -f k8s/staging/
        
    - name: Run health checks
      run: |
        echo "Running health checks..."
        # Add health check commands here
        
    - name: Run smoke tests
      run: |
        echo "Running smoke tests..."
        # Add smoke test commands here

  deploy-production:
    name: Deploy to Production
    runs-on: ubuntu-latest
    needs: build
    if: github.event_name == 'release'
    environment: production
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4

    - name: Deploy to production
      run: |
        echo "Deploying to production environment..."
        # Add your production deployment commands here
        # Example: kubectl apply -f k8s/production/
        
    - name: Run health checks
      run: |
        echo "Running production health checks..."
        # Add health check commands here
        
    - name: Run smoke tests
      run: |
        echo "Running production smoke tests..."
        # Add smoke test commands here

    - name: Notify deployment
      uses: 8398a7/action-slack@v3
      if: always()
      with:
        status: ${{ job.status }}
        channel: '#deployments'
        webhook_url: ${{ secrets.SLACK_WEBHOOK }}
        fields: repo,message,commit,author,action,eventName,ref,workflow
