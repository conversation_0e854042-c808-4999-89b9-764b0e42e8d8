# Contributing to GoReal Platform

Thank you for your interest in contributing to GoReal! This document provides guidelines and information for contributors.

## Table of Contents

- [Code of Conduct](#code-of-conduct)
- [Getting Started](#getting-started)
- [Development Setup](#development-setup)
- [Coding Standards](#coding-standards)
- [Testing Guidelines](#testing-guidelines)
- [Pull Request Process](#pull-request-process)
- [Issue Reporting](#issue-reporting)
- [Security Vulnerabilities](#security-vulnerabilities)

## Code of Conduct

We are committed to providing a welcoming and inclusive environment for all contributors. Please read and follow our Code of Conduct:

- **Be respectful**: Treat all community members with respect and kindness
- **Be inclusive**: Welcome newcomers and help them get started
- **Be collaborative**: Work together to improve the project
- **Be constructive**: Provide helpful feedback and suggestions
- **Be professional**: Maintain a professional tone in all interactions

## Getting Started

### Prerequisites

Before contributing, ensure you have the following installed:

#### Backend Development
- **Go 1.22+**: Latest stable version
- **PostgreSQL 15+**: Database server
- **Redis 7+**: Caching and rate limiting
- **Docker & Docker Compose**: For development environment

#### Frontend Development
- **Node.js 18+**: JavaScript runtime
- **npm/yarn/pnpm**: Package manager
- **Git**: Version control

#### Development Tools
- **golangci-lint**: Go linting tool
- **gofmt**: Go code formatter
- **goimports**: Go import organizer
- **Prettier**: Frontend code formatter
- **ESLint**: JavaScript/TypeScript linting

### Repository Structure

```
goreal/
├── backend/                    # Go backend application
│   ├── cmd/api/               # Application entrypoint
│   ├── internal/              # Private application code
│   ├── pkg/                   # Public packages
│   └── test/                  # Integration tests
├── client/                    # Next.js frontend application
│   ├── app/                   # App Router pages
│   ├── components/            # React components
│   ├── hooks/                 # Custom hooks
│   └── types/                 # TypeScript definitions
├── docs/                      # Documentation
├── scripts/                   # Development scripts
└── docker-compose.yml         # Development environment
```

## Development Setup

### 1. Clone the Repository

```bash
git clone https://github.com/your-org/goreal.git
cd goreal
```

### 2. Backend Setup

```bash
cd backend

# Install dependencies
go mod download

# Copy environment configuration
cp .env.example .env

# Start development services
docker-compose up -d postgres redis

# Run database migrations
go run cmd/migrate/main.go

# Start the backend server
go run cmd/api/main.go
```

### 3. Frontend Setup

```bash
cd client

# Install dependencies
npm install

# Copy environment configuration
cp .env.example .env.local

# Start the development server
npm run dev
```

### 4. Verify Setup

- Backend: http://localhost:8080/health
- Frontend: http://localhost:3000
- API Documentation: http://localhost:8080/docs

## Coding Standards

### Go Backend Standards

#### 1. Code Style
- Follow official Go style guidelines
- Use `gofmt` and `goimports` for formatting
- Run `golangci-lint` before committing
- Use meaningful variable and function names

#### 2. Package Organization
```go
// Package declaration with description
// Package handlers provides HTTP request handlers for the GoReal API.
package handlers

import (
    // Standard library imports first
    "context"
    "net/http"
    
    // Third-party imports
    "github.com/google/uuid"
    
    // Local imports
    "goreal-backend/internal/domain"
)
```

#### 3. Error Handling
```go
// Always handle errors explicitly
result, err := service.DoSomething(ctx, input)
if err != nil {
    return fmt.Errorf("failed to do something: %w", err)
}

// Use structured errors for domain logic
if user.IsInactive() {
    return domain.ErrUserInactive
}
```

#### 4. Function Documentation
```go
// CreateUser creates a new user with the provided information.
// It validates the input, checks for duplicates, and returns the created user.
//
// Parameters:
//   - ctx: Request context for cancellation and tracing
//   - req: User creation request with required fields
//
// Returns:
//   - *domain.User: The created user with generated ID
//   - error: Validation or database errors
func (s *UserService) CreateUser(ctx context.Context, req *CreateUserRequest) (*domain.User, error) {
    // Implementation
}
```

#### 5. Testing Standards
```go
func TestUserService_CreateUser(t *testing.T) {
    tests := []struct {
        name    string
        input   *CreateUserRequest
        want    *domain.User
        wantErr bool
    }{
        {
            name: "valid user creation",
            input: &CreateUserRequest{
                Email:    "<EMAIL>",
                Username: "testuser",
                FullName: "Test User",
            },
            want: &domain.User{
                Email:    "<EMAIL>",
                Username: "testuser",
                FullName: "Test User",
            },
            wantErr: false,
        },
        // More test cases...
    }

    for _, tt := range tests {
        t.Run(tt.name, func(t *testing.T) {
            // Test implementation
        })
    }
}
```

### Frontend Standards

#### 1. TypeScript Configuration
- Use strict TypeScript configuration
- Define comprehensive interfaces for all data structures
- Use generic types where appropriate
- Avoid `any` type usage

#### 2. Component Structure
```tsx
// Component with proper TypeScript and documentation
interface UserCardProps {
  user: User
  onEdit?: (user: User) => void
  className?: string
}

/**
 * UserCard displays user information in a card format
 * with optional edit functionality.
 */
export const UserCard: React.FC<UserCardProps> = ({
  user,
  onEdit,
  className
}) => {
  const handleEdit = useCallback(() => {
    onEdit?.(user)
  }, [user, onEdit])

  return (
    <div 
      className={cn('user-card', className)}
      role="article"
      aria-label={`User card for ${user.fullName}`}
    >
      {/* Component content */}
    </div>
  )
}
```

#### 3. Hook Patterns
```tsx
// Custom hook with proper typing and error handling
export function useUser(userId: string) {
  const [user, setUser] = useState<User | null>(null)
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<Error | null>(null)

  useEffect(() => {
    const fetchUser = async () => {
      try {
        setLoading(true)
        const userData = await api.users.getUser(userId)
        setUser(userData)
      } catch (err) {
        setError(err as Error)
      } finally {
        setLoading(false)
      }
    }

    fetchUser()
  }, [userId])

  return { user, loading, error }
}
```

#### 4. Accessibility Standards
- Use semantic HTML elements
- Include proper ARIA attributes
- Ensure keyboard navigation support
- Test with screen readers
- Maintain color contrast ratios

## Testing Guidelines

### Backend Testing

#### 1. Unit Tests
- Write table-driven tests for all public functions
- Use testify for assertions and mocking
- Achieve >90% code coverage
- Test both success and error cases

#### 2. Integration Tests
- Test complete API endpoints
- Use test databases for isolation
- Test middleware and authentication
- Verify error responses and status codes

#### 3. Benchmark Tests
```go
func BenchmarkUserService_CreateUser(b *testing.B) {
    service := setupTestService()
    req := &CreateUserRequest{
        Email:    "<EMAIL>",
        Username: "benchuser",
        FullName: "Bench User",
    }

    b.ResetTimer()
    for i := 0; i < b.N; i++ {
        _, err := service.CreateUser(context.Background(), req)
        if err != nil {
            b.Fatal(err)
        }
    }
}
```

### Frontend Testing

#### 1. Component Tests
```tsx
describe('UserCard', () => {
  const mockUser: User = {
    id: '1',
    email: '<EMAIL>',
    fullName: 'Test User',
    // ... other properties
  }

  it('renders user information correctly', () => {
    render(<UserCard user={mockUser} />)
    
    expect(screen.getByText('Test User')).toBeInTheDocument()
    expect(screen.getByText('<EMAIL>')).toBeInTheDocument()
  })

  it('calls onEdit when edit button is clicked', () => {
    const onEdit = jest.fn()
    render(<UserCard user={mockUser} onEdit={onEdit} />)
    
    fireEvent.click(screen.getByRole('button', { name: /edit/i }))
    expect(onEdit).toHaveBeenCalledWith(mockUser)
  })
})
```

#### 2. Hook Tests
```tsx
describe('useUser', () => {
  it('fetches user data successfully', async () => {
    const mockUser = { id: '1', name: 'Test User' }
    jest.spyOn(api.users, 'getUser').mockResolvedValue(mockUser)

    const { result, waitForNextUpdate } = renderHook(() => 
      useUser('1')
    )

    expect(result.current.loading).toBe(true)
    
    await waitForNextUpdate()
    
    expect(result.current.user).toEqual(mockUser)
    expect(result.current.loading).toBe(false)
    expect(result.current.error).toBe(null)
  })
})
```

## Pull Request Process

### 1. Before Creating a PR

- [ ] Create a feature branch from `main`
- [ ] Write comprehensive tests for your changes
- [ ] Run all tests and ensure they pass
- [ ] Run linting tools and fix any issues
- [ ] Update documentation if necessary
- [ ] Test your changes thoroughly

### 2. PR Requirements

#### Title Format
```
type(scope): brief description

Examples:
feat(auth): add JWT token blacklisting
fix(api): resolve user creation validation bug
docs(readme): update installation instructions
```

#### Description Template
```markdown
## Description
Brief description of the changes made.

## Type of Change
- [ ] Bug fix (non-breaking change which fixes an issue)
- [ ] New feature (non-breaking change which adds functionality)
- [ ] Breaking change (fix or feature that would cause existing functionality to not work as expected)
- [ ] Documentation update

## Testing
- [ ] Unit tests added/updated
- [ ] Integration tests added/updated
- [ ] Manual testing completed

## Checklist
- [ ] Code follows the style guidelines
- [ ] Self-review completed
- [ ] Documentation updated
- [ ] Tests added and passing
- [ ] No breaking changes (or breaking changes documented)
```

### 3. Review Process

1. **Automated Checks**: All CI checks must pass
2. **Code Review**: At least one maintainer review required
3. **Testing**: All tests must pass
4. **Documentation**: Updates must be included if applicable

### 4. Merging

- Use "Squash and merge" for feature branches
- Use "Merge commit" for release branches
- Delete feature branches after merging

## Issue Reporting

### Bug Reports

Use the bug report template:

```markdown
**Describe the bug**
A clear and concise description of what the bug is.

**To Reproduce**
Steps to reproduce the behavior:
1. Go to '...'
2. Click on '....'
3. Scroll down to '....'
4. See error

**Expected behavior**
A clear and concise description of what you expected to happen.

**Screenshots**
If applicable, add screenshots to help explain your problem.

**Environment:**
- OS: [e.g. iOS]
- Browser [e.g. chrome, safari]
- Version [e.g. 22]

**Additional context**
Add any other context about the problem here.
```

### Feature Requests

Use the feature request template:

```markdown
**Is your feature request related to a problem? Please describe.**
A clear and concise description of what the problem is.

**Describe the solution you'd like**
A clear and concise description of what you want to happen.

**Describe alternatives you've considered**
A clear and concise description of any alternative solutions or features you've considered.

**Additional context**
Add any other context or screenshots about the feature request here.
```

## Security Vulnerabilities

If you discover a security vulnerability, please:

1. **DO NOT** create a public issue
2. Email <EMAIL> with details
3. Include steps to reproduce if possible
4. Allow time for the issue to be addressed before public disclosure

## Development Commands

### Backend Commands
```bash
# Run tests
go test ./...

# Run tests with coverage
go test -cover ./...

# Run linting
golangci-lint run

# Format code
gofmt -w .
goimports -w .

# Build application
go build -o bin/api cmd/api/main.go
```

### Frontend Commands
```bash
# Run development server
npm run dev

# Run tests
npm test

# Run linting
npm run lint

# Format code
npm run format

# Build for production
npm run build
```

## Getting Help

- **Documentation**: Check the `/docs` directory
- **Issues**: Search existing issues before creating new ones
- **Discussions**: Use GitHub Discussions for questions
- **Discord**: Join our development Discord server

Thank you for contributing to GoReal! 🚀
