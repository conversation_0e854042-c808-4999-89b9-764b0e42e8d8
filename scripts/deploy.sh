#!/bin/bash

# ============================================================================
# GOREAL PLATFORM DEPLOYMENT SCRIPT
# ============================================================================

set -euo pipefail

# Configuration
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(dirname "$SCRIPT_DIR")"
ENVIRONMENT="${1:-staging}"
VERSION="${2:-latest}"
NAMESPACE="goreal-${ENVIRONMENT}"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Logging functions
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Check prerequisites
check_prerequisites() {
    log_info "Checking prerequisites..."
    
    # Check if kubectl is installed
    if ! command -v kubectl &> /dev/null; then
        log_error "kubectl is not installed or not in PATH"
        exit 1
    fi
    
    # Check if docker is installed
    if ! command -v docker &> /dev/null; then
        log_error "docker is not installed or not in PATH"
        exit 1
    fi
    
    # Check if helm is installed (optional)
    if ! command -v helm &> /dev/null; then
        log_warning "helm is not installed - some features may not be available"
    fi
    
    # Check kubectl connection
    if ! kubectl cluster-info &> /dev/null; then
        log_error "Cannot connect to Kubernetes cluster"
        exit 1
    fi
    
    log_success "Prerequisites check passed"
}

# Validate environment
validate_environment() {
    log_info "Validating environment: $ENVIRONMENT"
    
    case $ENVIRONMENT in
        development|staging|production)
            log_success "Environment '$ENVIRONMENT' is valid"
            ;;
        *)
            log_error "Invalid environment: $ENVIRONMENT. Must be one of: development, staging, production"
            exit 1
            ;;
    esac
}

# Create namespace if it doesn't exist
create_namespace() {
    log_info "Creating namespace: $NAMESPACE"
    
    if kubectl get namespace "$NAMESPACE" &> /dev/null; then
        log_info "Namespace '$NAMESPACE' already exists"
    else
        kubectl create namespace "$NAMESPACE"
        kubectl label namespace "$NAMESPACE" environment="$ENVIRONMENT"
        log_success "Created namespace: $NAMESPACE"
    fi
}

# Apply secrets
apply_secrets() {
    log_info "Applying secrets for $ENVIRONMENT..."
    
    local secrets_file="$PROJECT_ROOT/k8s/$ENVIRONMENT/secrets.yaml"
    
    if [[ -f "$secrets_file" ]]; then
        kubectl apply -f "$secrets_file" -n "$NAMESPACE"
        log_success "Applied secrets"
    else
        log_warning "No secrets file found at $secrets_file"
    fi
}

# Apply ConfigMaps
apply_configmaps() {
    log_info "Applying ConfigMaps for $ENVIRONMENT..."
    
    local configmap_file="$PROJECT_ROOT/k8s/$ENVIRONMENT/configmap.yaml"
    
    if [[ -f "$configmap_file" ]]; then
        kubectl apply -f "$configmap_file" -n "$NAMESPACE"
        log_success "Applied ConfigMaps"
    else
        log_warning "No ConfigMap file found at $configmap_file"
    fi
}

# Deploy database (if needed)
deploy_database() {
    log_info "Deploying database for $ENVIRONMENT..."
    
    local db_file="$PROJECT_ROOT/k8s/$ENVIRONMENT/database.yaml"
    
    if [[ -f "$db_file" ]]; then
        kubectl apply -f "$db_file" -n "$NAMESPACE"
        
        # Wait for database to be ready
        log_info "Waiting for database to be ready..."
        kubectl wait --for=condition=ready pod -l app=postgres -n "$NAMESPACE" --timeout=300s
        
        log_success "Database deployed and ready"
    else
        log_info "No database deployment file found - assuming external database"
    fi
}

# Deploy Redis (if needed)
deploy_redis() {
    log_info "Deploying Redis for $ENVIRONMENT..."
    
    local redis_file="$PROJECT_ROOT/k8s/$ENVIRONMENT/redis.yaml"
    
    if [[ -f "$redis_file" ]]; then
        kubectl apply -f "$redis_file" -n "$NAMESPACE"
        
        # Wait for Redis to be ready
        log_info "Waiting for Redis to be ready..."
        kubectl wait --for=condition=ready pod -l app=redis -n "$NAMESPACE" --timeout=300s
        
        log_success "Redis deployed and ready"
    else
        log_info "No Redis deployment file found - assuming external Redis"
    fi
}

# Deploy backend
deploy_backend() {
    log_info "Deploying backend for $ENVIRONMENT..."
    
    local backend_file="$PROJECT_ROOT/k8s/$ENVIRONMENT/backend-deployment.yaml"
    
    if [[ -f "$backend_file" ]]; then
        # Update image tag if version is specified
        if [[ "$VERSION" != "latest" ]]; then
            sed -i.bak "s|:latest|:$VERSION|g" "$backend_file"
        fi
        
        kubectl apply -f "$backend_file" -n "$NAMESPACE"
        
        # Wait for backend deployment to be ready
        log_info "Waiting for backend deployment to be ready..."
        kubectl rollout status deployment/goreal-backend -n "$NAMESPACE" --timeout=600s
        
        # Restore original file if we modified it
        if [[ "$VERSION" != "latest" && -f "$backend_file.bak" ]]; then
            mv "$backend_file.bak" "$backend_file"
        fi
        
        log_success "Backend deployed successfully"
    else
        log_error "Backend deployment file not found at $backend_file"
        exit 1
    fi
}

# Deploy frontend
deploy_frontend() {
    log_info "Deploying frontend for $ENVIRONMENT..."
    
    local frontend_file="$PROJECT_ROOT/k8s/$ENVIRONMENT/frontend-deployment.yaml"
    
    if [[ -f "$frontend_file" ]]; then
        # Update image tag if version is specified
        if [[ "$VERSION" != "latest" ]]; then
            sed -i.bak "s|:latest|:$VERSION|g" "$frontend_file"
        fi
        
        kubectl apply -f "$frontend_file" -n "$NAMESPACE"
        
        # Wait for frontend deployment to be ready
        log_info "Waiting for frontend deployment to be ready..."
        kubectl rollout status deployment/goreal-frontend -n "$NAMESPACE" --timeout=600s
        
        # Restore original file if we modified it
        if [[ "$VERSION" != "latest" && -f "$frontend_file.bak" ]]; then
            mv "$frontend_file.bak" "$frontend_file"
        fi
        
        log_success "Frontend deployed successfully"
    else
        log_error "Frontend deployment file not found at $frontend_file"
        exit 1
    fi
}

# Deploy ingress
deploy_ingress() {
    log_info "Deploying ingress for $ENVIRONMENT..."
    
    local ingress_file="$PROJECT_ROOT/k8s/$ENVIRONMENT/ingress.yaml"
    
    if [[ -f "$ingress_file" ]]; then
        kubectl apply -f "$ingress_file" -n "$NAMESPACE"
        log_success "Ingress deployed successfully"
    else
        log_warning "No ingress file found at $ingress_file"
    fi
}

# Deploy monitoring (if enabled)
deploy_monitoring() {
    log_info "Deploying monitoring for $ENVIRONMENT..."
    
    local monitoring_dir="$PROJECT_ROOT/k8s/$ENVIRONMENT/monitoring"
    
    if [[ -d "$monitoring_dir" ]]; then
        kubectl apply -f "$monitoring_dir/" -n "$NAMESPACE"
        log_success "Monitoring deployed successfully"
    else
        log_info "No monitoring configuration found for $ENVIRONMENT"
    fi
}

# Run health checks
run_health_checks() {
    log_info "Running health checks..."
    
    # Wait a bit for services to start
    sleep 30
    
    # Check backend health
    local backend_service="goreal-backend-service"
    if kubectl get service "$backend_service" -n "$NAMESPACE" &> /dev/null; then
        log_info "Checking backend health..."
        
        # Port forward to check health
        kubectl port-forward service/"$backend_service" 8080:80 -n "$NAMESPACE" &
        local port_forward_pid=$!
        
        sleep 5
        
        if curl -f http://localhost:8080/health &> /dev/null; then
            log_success "Backend health check passed"
        else
            log_error "Backend health check failed"
            kill $port_forward_pid 2>/dev/null || true
            exit 1
        fi
        
        kill $port_forward_pid 2>/dev/null || true
    fi
    
    # Check frontend health
    local frontend_service="goreal-frontend-service"
    if kubectl get service "$frontend_service" -n "$NAMESPACE" &> /dev/null; then
        log_info "Checking frontend health..."
        
        # Port forward to check health
        kubectl port-forward service/"$frontend_service" 3000:80 -n "$NAMESPACE" &
        local port_forward_pid=$!
        
        sleep 5
        
        if curl -f http://localhost:3000/api/health &> /dev/null; then
            log_success "Frontend health check passed"
        else
            log_error "Frontend health check failed"
            kill $port_forward_pid 2>/dev/null || true
            exit 1
        fi
        
        kill $port_forward_pid 2>/dev/null || true
    fi
    
    log_success "All health checks passed"
}

# Run smoke tests
run_smoke_tests() {
    log_info "Running smoke tests..."
    
    local smoke_test_script="$PROJECT_ROOT/scripts/smoke-tests.sh"
    
    if [[ -f "$smoke_test_script" ]]; then
        bash "$smoke_test_script" "$ENVIRONMENT" "$NAMESPACE"
        log_success "Smoke tests completed"
    else
        log_warning "No smoke test script found at $smoke_test_script"
    fi
}

# Show deployment status
show_deployment_status() {
    log_info "Deployment Status for $ENVIRONMENT:"
    echo
    
    # Show pods
    echo "Pods:"
    kubectl get pods -n "$NAMESPACE" -o wide
    echo
    
    # Show services
    echo "Services:"
    kubectl get services -n "$NAMESPACE"
    echo
    
    # Show ingress
    echo "Ingress:"
    kubectl get ingress -n "$NAMESPACE" 2>/dev/null || echo "No ingress found"
    echo
    
    # Show HPA
    echo "Horizontal Pod Autoscalers:"
    kubectl get hpa -n "$NAMESPACE" 2>/dev/null || echo "No HPA found"
    echo
}

# Rollback deployment
rollback_deployment() {
    log_warning "Rolling back deployment..."
    
    # Rollback backend
    if kubectl get deployment goreal-backend -n "$NAMESPACE" &> /dev/null; then
        kubectl rollout undo deployment/goreal-backend -n "$NAMESPACE"
        kubectl rollout status deployment/goreal-backend -n "$NAMESPACE"
    fi
    
    # Rollback frontend
    if kubectl get deployment goreal-frontend -n "$NAMESPACE" &> /dev/null; then
        kubectl rollout undo deployment/goreal-frontend -n "$NAMESPACE"
        kubectl rollout status deployment/goreal-frontend -n "$NAMESPACE"
    fi
    
    log_success "Rollback completed"
}

# Cleanup function
cleanup() {
    log_info "Cleaning up..."
    # Kill any background processes
    jobs -p | xargs -r kill 2>/dev/null || true
}

# Trap cleanup on exit
trap cleanup EXIT

# Main deployment function
main() {
    log_info "Starting deployment of GoReal Platform"
    log_info "Environment: $ENVIRONMENT"
    log_info "Version: $VERSION"
    log_info "Namespace: $NAMESPACE"
    echo
    
    # Run deployment steps
    check_prerequisites
    validate_environment
    create_namespace
    apply_secrets
    apply_configmaps
    deploy_database
    deploy_redis
    deploy_backend
    deploy_frontend
    deploy_ingress
    deploy_monitoring
    
    # Run post-deployment checks
    run_health_checks
    run_smoke_tests
    
    # Show final status
    show_deployment_status
    
    log_success "Deployment completed successfully!"
    log_info "Access your application at the ingress URL shown above"
}

# Handle script arguments
case "${1:-}" in
    rollback)
        rollback_deployment
        ;;
    status)
        show_deployment_status
        ;;
    *)
        main
        ;;
esac
