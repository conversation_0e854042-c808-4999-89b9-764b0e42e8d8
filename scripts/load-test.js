// ============================================================================
// K6 LOAD TESTING SCRIPT FOR GOREAL PLATFORM
// ============================================================================

import http from 'k6/http';
import { check, sleep } from 'k6';
import { Rate, Trend, Counter } from 'k6/metrics';

// Custom metrics
const errorRate = new Rate('errors');
const responseTime = new Trend('response_time');
const requestCount = new Counter('requests');

// Test configuration
export const options = {
  stages: [
    { duration: '2m', target: 100 }, // Ramp up to 100 users
    { duration: '5m', target: 100 }, // Stay at 100 users
    { duration: '2m', target: 200 }, // Ramp up to 200 users
    { duration: '5m', target: 200 }, // Stay at 200 users
    { duration: '2m', target: 0 },   // Ramp down to 0 users
  ],
  thresholds: {
    http_req_duration: ['p(95)<500'], // 95% of requests must complete below 500ms
    http_req_failed: ['rate<0.05'],   // Error rate must be below 5%
    errors: ['rate<0.05'],            // Custom error rate below 5%
  },
};

// Base URL from environment or default
const BASE_URL = __ENV.TARGET_URL || 'http://localhost:8080';

// Test data
const testUsers = [
  { email: '<EMAIL>', password: 'TestPassword123!' },
  { email: '<EMAIL>', password: 'TestPassword123!' },
  { email: '<EMAIL>', password: 'TestPassword123!' },
];

// Authentication tokens storage
let authTokens = {};

export function setup() {
  console.log('Setting up load test...');
  console.log(`Target URL: ${BASE_URL}`);
  
  // Health check
  const healthResponse = http.get(`${BASE_URL}/health`);
  check(healthResponse, {
    'health check status is 200': (r) => r.status === 200,
  });
  
  return { baseUrl: BASE_URL };
}

export default function (data) {
  const baseUrl = data.baseUrl;
  
  // Test scenarios with weighted distribution
  const scenario = Math.random();
  
  if (scenario < 0.3) {
    // 30% - Authentication flow
    testAuthentication(baseUrl);
  } else if (scenario < 0.6) {
    // 30% - User management
    testUserManagement(baseUrl);
  } else if (scenario < 0.8) {
    // 20% - Task management
    testTaskManagement(baseUrl);
  } else {
    // 20% - Analytics and reporting
    testAnalytics(baseUrl);
  }
  
  sleep(1); // Think time between requests
}

function testAuthentication(baseUrl) {
  const user = testUsers[Math.floor(Math.random() * testUsers.length)];
  
  // Login request
  const loginPayload = JSON.stringify({
    email: user.email,
    password: user.password,
  });
  
  const loginParams = {
    headers: {
      'Content-Type': 'application/json',
    },
  };
  
  const loginResponse = http.post(`${baseUrl}/api/auth/login`, loginPayload, loginParams);
  
  const loginSuccess = check(loginResponse, {
    'login status is 200': (r) => r.status === 200,
    'login response has token': (r) => {
      try {
        const body = JSON.parse(r.body);
        return body.accessToken !== undefined;
      } catch (e) {
        return false;
      }
    },
  });
  
  requestCount.add(1);
  responseTime.add(loginResponse.timings.duration);
  errorRate.add(!loginSuccess);
  
  if (loginSuccess) {
    const loginBody = JSON.parse(loginResponse.body);
    authTokens[user.email] = loginBody.accessToken;
    
    // Test token refresh
    sleep(0.5);
    
    const refreshPayload = JSON.stringify({
      refreshToken: loginBody.refreshToken,
    });
    
    const refreshResponse = http.post(`${baseUrl}/api/auth/refresh`, refreshPayload, loginParams);
    
    const refreshSuccess = check(refreshResponse, {
      'refresh status is 200': (r) => r.status === 200,
    });
    
    requestCount.add(1);
    responseTime.add(refreshResponse.timings.duration);
    errorRate.add(!refreshSuccess);
  }
}

function testUserManagement(baseUrl) {
  const user = testUsers[Math.floor(Math.random() * testUsers.length)];
  const token = authTokens[user.email];
  
  if (!token) {
    // If no token, perform login first
    testAuthentication(baseUrl);
    return;
  }
  
  const headers = {
    'Authorization': `Bearer ${token}`,
    'Content-Type': 'application/json',
  };
  
  // Get user profile
  const profileResponse = http.get(`${baseUrl}/api/users/profile`, { headers });
  
  const profileSuccess = check(profileResponse, {
    'profile status is 200': (r) => r.status === 200,
    'profile has user data': (r) => {
      try {
        const body = JSON.parse(r.body);
        return body.id !== undefined && body.email !== undefined;
      } catch (e) {
        return false;
      }
    },
  });
  
  requestCount.add(1);
  responseTime.add(profileResponse.timings.duration);
  errorRate.add(!profileSuccess);
  
  // Update profile
  sleep(0.3);
  
  const updatePayload = JSON.stringify({
    fullName: `Updated User ${Math.floor(Math.random() * 1000)}`,
    bio: 'Updated bio from load test',
  });
  
  const updateResponse = http.put(`${baseUrl}/api/users/profile`, updatePayload, { headers });
  
  const updateSuccess = check(updateResponse, {
    'profile update status is 200': (r) => r.status === 200,
  });
  
  requestCount.add(1);
  responseTime.add(updateResponse.timings.duration);
  errorRate.add(!updateSuccess);
}

function testTaskManagement(baseUrl) {
  const user = testUsers[Math.floor(Math.random() * testUsers.length)];
  const token = authTokens[user.email];
  
  if (!token) {
    testAuthentication(baseUrl);
    return;
  }
  
  const headers = {
    'Authorization': `Bearer ${token}`,
    'Content-Type': 'application/json',
  };
  
  // List tasks
  const tasksResponse = http.get(`${baseUrl}/api/tasks?page=1&limit=20`, { headers });
  
  const tasksSuccess = check(tasksResponse, {
    'tasks list status is 200': (r) => r.status === 200,
    'tasks response has data': (r) => {
      try {
        const body = JSON.parse(r.body);
        return body.data !== undefined;
      } catch (e) {
        return false;
      }
    },
  });
  
  requestCount.add(1);
  responseTime.add(tasksResponse.timings.duration);
  errorRate.add(!tasksSuccess);
  
  // Create a new task
  sleep(0.3);
  
  const taskPayload = JSON.stringify({
    title: `Load Test Task ${Math.floor(Math.random() * 10000)}`,
    description: 'Task created during load testing',
    priority: 'medium',
    tags: ['load-test', 'automated'],
  });
  
  const createTaskResponse = http.post(`${baseUrl}/api/tasks`, taskPayload, { headers });
  
  const createTaskSuccess = check(createTaskResponse, {
    'create task status is 201': (r) => r.status === 201,
    'created task has id': (r) => {
      try {
        const body = JSON.parse(r.body);
        return body.id !== undefined;
      } catch (e) {
        return false;
      }
    },
  });
  
  requestCount.add(1);
  responseTime.add(createTaskResponse.timings.duration);
  errorRate.add(!createTaskSuccess);
  
  // If task was created successfully, try to update it
  if (createTaskSuccess) {
    const createdTask = JSON.parse(createTaskResponse.body);
    
    sleep(0.2);
    
    const updateTaskPayload = JSON.stringify({
      status: 'in_progress',
      description: 'Updated during load test',
    });
    
    const updateTaskResponse = http.put(
      `${baseUrl}/api/tasks/${createdTask.id}`,
      updateTaskPayload,
      { headers }
    );
    
    const updateTaskSuccess = check(updateTaskResponse, {
      'update task status is 200': (r) => r.status === 200,
    });
    
    requestCount.add(1);
    responseTime.add(updateTaskResponse.timings.duration);
    errorRate.add(!updateTaskSuccess);
  }
}

function testAnalytics(baseUrl) {
  const user = testUsers[Math.floor(Math.random() * testUsers.length)];
  const token = authTokens[user.email];
  
  if (!token) {
    testAuthentication(baseUrl);
    return;
  }
  
  const headers = {
    'Authorization': `Bearer ${token}`,
  };
  
  // Get dashboard analytics
  const dashboardResponse = http.get(`${baseUrl}/api/analytics/dashboard`, { headers });
  
  const dashboardSuccess = check(dashboardResponse, {
    'dashboard status is 200': (r) => r.status === 200,
    'dashboard has metrics': (r) => {
      try {
        const body = JSON.parse(r.body);
        return body.totalTasks !== undefined;
      } catch (e) {
        return false;
      }
    },
  });
  
  requestCount.add(1);
  responseTime.add(dashboardResponse.timings.duration);
  errorRate.add(!dashboardSuccess);
  
  // Get performance metrics
  sleep(0.2);
  
  const metricsResponse = http.get(`${baseUrl}/metrics/performance`, { headers });
  
  const metricsSuccess = check(metricsResponse, {
    'metrics status is 200': (r) => r.status === 200,
  });
  
  requestCount.add(1);
  responseTime.add(metricsResponse.timings.duration);
  errorRate.add(!metricsSuccess);
}

export function teardown(data) {
  console.log('Load test completed');
  console.log(`Total requests: ${requestCount.count}`);
  console.log(`Average response time: ${responseTime.avg}ms`);
  console.log(`Error rate: ${(errorRate.rate * 100).toFixed(2)}%`);
}
