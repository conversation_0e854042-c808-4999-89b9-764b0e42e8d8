# ============================================================================
# DOCKER COMPOSE FOR TESTING ENVIRONMENT
# ============================================================================

version: '3.8'

services:
  # PostgreSQL for testing
  postgres-test:
    image: postgres:15-alpine
    environment:
      POSTGRES_DB: goreal_test
      POSTGRES_USER: goreal_test
      POSTGRES_PASSWORD: test_password
      POSTGRES_INITDB_ARGS: "--auth-host=scram-sha-256"
    ports:
      - "5433:5432"
    volumes:
      - postgres_test_data:/var/lib/postgresql/data
      - ./backend/migrations:/docker-entrypoint-initdb.d
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U goreal_test -d goreal_test"]
      interval: 10s
      timeout: 5s
      retries: 5
    networks:
      - test-network

  # Redis for testing
  redis-test:
    image: redis:7-alpine
    command: redis-server --requirepass test_password
    ports:
      - "6380:6379"
    volumes:
      - redis_test_data:/data
    healthcheck:
      test: ["CMD", "redis-cli", "--raw", "incr", "ping"]
      interval: 10s
      timeout: 3s
      retries: 5
    networks:
      - test-network

  # Backend service for testing
  backend-test:
    build:
      context: ./backend
      dockerfile: Dockerfile.prod
    environment:
      - PORT=8080
      - ENVIRONMENT=test
      - DB_HOST=postgres-test
      - DB_PORT=5432
      - DB_NAME=goreal_test
      - DB_USER=goreal_test
      - DB_PASSWORD=test_password
      - REDIS_HOST=redis-test
      - REDIS_PORT=6379
      - REDIS_PASSWORD=test_password
      - JWT_ACCESS_SECRET=test-access-secret-key-for-testing-only
      - JWT_REFRESH_SECRET=test-refresh-secret-key-for-testing-only
    ports:
      - "8081:8080"
    depends_on:
      postgres-test:
        condition: service_healthy
      redis-test:
        condition: service_healthy
    healthcheck:
      test: ["CMD", "wget", "--no-verbose", "--tries=1", "--spider", "http://localhost:8080/health"]
      interval: 30s
      timeout: 10s
      retries: 3
    networks:
      - test-network

  # Frontend service for testing
  frontend-test:
    build:
      context: ./client
      dockerfile: Dockerfile.prod
    environment:
      - NODE_ENV=test
      - NEXT_PUBLIC_API_URL=http://backend-test:8080
      - NEXT_PUBLIC_ENVIRONMENT=test
    ports:
      - "3001:3000"
    depends_on:
      backend-test:
        condition: service_healthy
    healthcheck:
      test: ["CMD", "wget", "--no-verbose", "--tries=1", "--spider", "http://localhost:3000/api/health"]
      interval: 30s
      timeout: 10s
      retries: 3
    networks:
      - test-network

  # nginx for load balancing in tests
  nginx-test:
    image: nginx:alpine
    ports:
      - "8082:80"
    volumes:
      - ./nginx/test.conf:/etc/nginx/nginx.conf:ro
    depends_on:
      - backend-test
      - frontend-test
    healthcheck:
      test: ["CMD", "nginx", "-t"]
      interval: 30s
      timeout: 10s
      retries: 3
    networks:
      - test-network

  # Prometheus for metrics testing
  prometheus-test:
    image: prom/prometheus:latest
    ports:
      - "9091:9090"
    volumes:
      - ./monitoring/prometheus-test.yml:/etc/prometheus/prometheus.yml:ro
    command:
      - '--config.file=/etc/prometheus/prometheus.yml'
      - '--storage.tsdb.path=/prometheus'
      - '--web.console.libraries=/etc/prometheus/console_libraries'
      - '--web.console.templates=/etc/prometheus/consoles'
      - '--storage.tsdb.retention.time=1h'
      - '--web.enable-lifecycle'
    networks:
      - test-network

  # Grafana for visualization testing
  grafana-test:
    image: grafana/grafana:latest
    ports:
      - "3002:3000"
    environment:
      - GF_SECURITY_ADMIN_PASSWORD=test_password
      - GF_USERS_ALLOW_SIGN_UP=false
    volumes:
      - grafana_test_data:/var/lib/grafana
    networks:
      - test-network

  # Jaeger for tracing testing
  jaeger-test:
    image: jaegertracing/all-in-one:latest
    ports:
      - "16687:16686"
      - "14269:14268"
    environment:
      - COLLECTOR_OTLP_ENABLED=true
    networks:
      - test-network

  # Load testing with k6
  k6-test:
    image: loadimpact/k6:latest
    volumes:
      - ./scripts:/scripts
    command: run /scripts/load-test.js
    environment:
      - TARGET_URL=http://nginx-test
    depends_on:
      - nginx-test
    networks:
      - test-network

networks:
  test-network:
    driver: bridge

volumes:
  postgres_test_data:
    driver: local
  redis_test_data:
    driver: local
  grafana_test_data:
    driver: local
