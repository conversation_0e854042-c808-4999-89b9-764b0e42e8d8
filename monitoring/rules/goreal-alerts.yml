# ============================================================================
# PROMETHEUS ALERTING RULES FOR GOREAL PLATFORM
# ============================================================================

groups:
  - name: goreal-backend-alerts
    rules:
      # High error rate alert
      - alert: HighErrorRate
        expr: |
          (
            rate(http_requests_total{job="goreal-backend",status=~"5.."}[5m]) /
            rate(http_requests_total{job="goreal-backend"}[5m])
          ) * 100 > 5
        for: 2m
        labels:
          severity: critical
          service: goreal-backend
        annotations:
          summary: "High error rate detected in GoReal backend"
          description: "Error rate is {{ $value }}% for the last 5 minutes"

      # High response time alert
      - alert: HighResponseTime
        expr: |
          histogram_quantile(0.95, 
            rate(http_request_duration_seconds_bucket{job="goreal-backend"}[5m])
          ) > 1
        for: 5m
        labels:
          severity: warning
          service: goreal-backend
        annotations:
          summary: "High response time detected in GoReal backend"
          description: "95th percentile response time is {{ $value }}s"

      # High memory usage alert
      - alert: HighMemoryUsage
        expr: |
          (
            container_memory_usage_bytes{pod=~"goreal-backend-.*"} /
            container_spec_memory_limit_bytes{pod=~"goreal-backend-.*"}
          ) * 100 > 80
        for: 5m
        labels:
          severity: warning
          service: goreal-backend
        annotations:
          summary: "High memory usage in GoReal backend"
          description: "Memory usage is {{ $value }}% of the limit"

      # High CPU usage alert
      - alert: HighCPUUsage
        expr: |
          (
            rate(container_cpu_usage_seconds_total{pod=~"goreal-backend-.*"}[5m]) /
            container_spec_cpu_quota{pod=~"goreal-backend-.*"} * 
            container_spec_cpu_period{pod=~"goreal-backend-.*"}
          ) * 100 > 80
        for: 5m
        labels:
          severity: warning
          service: goreal-backend
        annotations:
          summary: "High CPU usage in GoReal backend"
          description: "CPU usage is {{ $value }}% of the limit"

      # Pod restart alert
      - alert: PodRestartingTooOften
        expr: |
          rate(kube_pod_container_status_restarts_total{pod=~"goreal-backend-.*"}[15m]) > 0
        for: 0m
        labels:
          severity: warning
          service: goreal-backend
        annotations:
          summary: "GoReal backend pod is restarting too often"
          description: "Pod {{ $labels.pod }} has restarted {{ $value }} times in the last 15 minutes"

  - name: goreal-frontend-alerts
    rules:
      # Frontend error rate alert
      - alert: FrontendHighErrorRate
        expr: |
          (
            rate(http_requests_total{job="goreal-frontend",status=~"5.."}[5m]) /
            rate(http_requests_total{job="goreal-frontend"}[5m])
          ) * 100 > 5
        for: 2m
        labels:
          severity: warning
          service: goreal-frontend
        annotations:
          summary: "High error rate detected in GoReal frontend"
          description: "Frontend error rate is {{ $value }}%"

      # Frontend high response time
      - alert: FrontendHighResponseTime
        expr: |
          histogram_quantile(0.95, 
            rate(http_request_duration_seconds_bucket{job="goreal-frontend"}[5m])
          ) > 2
        for: 5m
        labels:
          severity: warning
          service: goreal-frontend
        annotations:
          summary: "High response time in GoReal frontend"
          description: "Frontend 95th percentile response time is {{ $value }}s"

  - name: goreal-database-alerts
    rules:
      # Database connection alert
      - alert: DatabaseHighConnections
        expr: |
          pg_stat_activity_count > 80
        for: 5m
        labels:
          severity: warning
          service: postgresql
        annotations:
          summary: "High number of database connections"
          description: "Database has {{ $value }} active connections"

      # Database slow queries
      - alert: DatabaseSlowQueries
        expr: |
          rate(pg_stat_statements_mean_time_ms[5m]) > 1000
        for: 5m
        labels:
          severity: warning
          service: postgresql
        annotations:
          summary: "Database slow queries detected"
          description: "Average query time is {{ $value }}ms"

      # Database disk usage
      - alert: DatabaseHighDiskUsage
        expr: |
          (
            pg_database_size_bytes /
            (pg_database_size_bytes + pg_tablespace_size_bytes)
          ) * 100 > 80
        for: 5m
        labels:
          severity: critical
          service: postgresql
        annotations:
          summary: "Database disk usage is high"
          description: "Database disk usage is {{ $value }}%"

  - name: goreal-redis-alerts
    rules:
      # Redis memory usage
      - alert: RedisHighMemoryUsage
        expr: |
          (
            redis_memory_used_bytes /
            redis_memory_max_bytes
          ) * 100 > 80
        for: 5m
        labels:
          severity: warning
          service: redis
        annotations:
          summary: "Redis memory usage is high"
          description: "Redis memory usage is {{ $value }}%"

      # Redis connection alert
      - alert: RedisHighConnections
        expr: |
          redis_connected_clients > 100
        for: 5m
        labels:
          severity: warning
          service: redis
        annotations:
          summary: "High number of Redis connections"
          description: "Redis has {{ $value }} connected clients"

      # Redis keyspace hit rate
      - alert: RedisLowHitRate
        expr: |
          (
            rate(redis_keyspace_hits_total[5m]) /
            (rate(redis_keyspace_hits_total[5m]) + rate(redis_keyspace_misses_total[5m]))
          ) * 100 < 70
        for: 10m
        labels:
          severity: warning
          service: redis
        annotations:
          summary: "Redis cache hit rate is low"
          description: "Redis hit rate is {{ $value }}%"

  - name: goreal-infrastructure-alerts
    rules:
      # Node disk usage
      - alert: NodeHighDiskUsage
        expr: |
          (
            (node_filesystem_size_bytes - node_filesystem_avail_bytes) /
            node_filesystem_size_bytes
          ) * 100 > 80
        for: 5m
        labels:
          severity: warning
          service: infrastructure
        annotations:
          summary: "Node disk usage is high"
          description: "Disk usage on {{ $labels.instance }} is {{ $value }}%"

      # Node memory usage
      - alert: NodeHighMemoryUsage
        expr: |
          (
            (node_memory_MemTotal_bytes - node_memory_MemAvailable_bytes) /
            node_memory_MemTotal_bytes
          ) * 100 > 80
        for: 5m
        labels:
          severity: warning
          service: infrastructure
        annotations:
          summary: "Node memory usage is high"
          description: "Memory usage on {{ $labels.instance }} is {{ $value }}%"

      # Node CPU usage
      - alert: NodeHighCPUUsage
        expr: |
          (
            100 - (avg by (instance) (rate(node_cpu_seconds_total{mode="idle"}[5m])) * 100)
          ) > 80
        for: 5m
        labels:
          severity: warning
          service: infrastructure
        annotations:
          summary: "Node CPU usage is high"
          description: "CPU usage on {{ $labels.instance }} is {{ $value }}%"

      # Node load average
      - alert: NodeHighLoadAverage
        expr: |
          node_load15 > 2
        for: 10m
        labels:
          severity: warning
          service: infrastructure
        annotations:
          summary: "Node load average is high"
          description: "15-minute load average on {{ $labels.instance }} is {{ $value }}"

  - name: goreal-business-alerts
    rules:
      # Low user registration rate
      - alert: LowUserRegistrationRate
        expr: |
          rate(user_registrations_total[1h]) < 1
        for: 2h
        labels:
          severity: info
          service: business
        annotations:
          summary: "Low user registration rate"
          description: "User registration rate is {{ $value }} per hour"

      # High task creation rate (potential spam)
      - alert: HighTaskCreationRate
        expr: |
          rate(tasks_created_total[5m]) > 10
        for: 5m
        labels:
          severity: warning
          service: business
        annotations:
          summary: "Unusually high task creation rate"
          description: "Task creation rate is {{ $value }} per minute"

      # Authentication failure rate
      - alert: HighAuthFailureRate
        expr: |
          (
            rate(auth_attempts_total{status="failed"}[5m]) /
            rate(auth_attempts_total[5m])
          ) * 100 > 20
        for: 5m
        labels:
          severity: warning
          service: security
        annotations:
          summary: "High authentication failure rate"
          description: "Authentication failure rate is {{ $value }}%"
