import {
  Dialog,
  DialogTitle,
  DialogContent,
  <PERSON>alog<PERSON><PERSON>,
  But<PERSON>,
  DialogContentText,
} from "@mui/material";
import React from "react";
import { deleteProject } from "../../../redux/action/project";
import { useDispatch, useSelector } from "react-redux";

const DeleteProject = ({ open, setOpen, projectId }) => {
  ////////////////////////////////////// VARIABLES ///////////////////////////////////////
  const dispatch = useDispatch();
  const { isFetching } = useSelector((state) => state.project);

  ////////////////////////////////////// FUNCTIONS ///////////////////////////////////////
  const handleClose = () => {
    setOpen(false);
  };
  const handleDelete = () => {
    dispatch(deleteProject(projectId));
    setOpen(false);
  };

  return (
    <Dialog open={open} onClose={() => setOpen(false)}>
      <DialogTitle id="alert-dialog-title">
        <div className="font-primary">Delete the Project?</div>
      </DialogTitle>
      <DialogContent>
        <DialogContentText id="alert-dialog-description">
          <div className="font-primary">Are you sure you want to delete this Project?</div>
        </DialogContentText>
      </DialogContent>
      <DialogActions className="mr-4 mb-2">
        <button
          className="bg-gray-400 hover:bg-gray-500 text-white px-4 py-2 rounded-lg font-primary"
          onClick={handleClose}>
          Cancel
        </button>
        <button
          className="bg-red-400 hover:bg-red-500 text-white px-4 py-2 rounded-lg font-primary"
          onClick={handleDelete}
          autoFocus>
          {isFetching ? "Deleting" : "Delete"}
        </button>
      </DialogActions>
    </Dialog>
  );
};

export default DeleteProject;
