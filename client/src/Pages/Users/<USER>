import { Modal, <PERSON><PERSON>, <PERSON><PERSON>Title, DialogContent, <PERSON>alog<PERSON><PERSON>, Button, DialogContentText } from '@mui/material'
import React from 'react'
import { deleteUser } from '../../redux/action/user'
import { useDispatch, useSelector } from 'react-redux'

const DeleteModal = ({ open, setOpen, userId }) => {

  ////////////////////////////////////// VARIABLES ///////////////////////////////////////
  const { isFetching, error } = useSelector(state => state.user)
  const dispatch = useDispatch()

  ////////////////////////////////////// FUNCTIONS ///////////////////////////////////////
  const handleClose = () => {
    setOpen(false)
  }
  const handleDelete = () => {
    dispatch(deleteUser(userId))
    setOpen(false)
  }

  return (
    <Dialog
      open={open}
      onClose={() => setOpen(false)}
    >
      <DialogTitle id="alert-dialog-title">
        Delete the User?
      </DialogTitle>
      <DialogContent>
        <DialogContentText id="alert-dialog-description">
          Are you sure you want to delete this user?
        </DialogContentText>
      </DialogContent>
      <DialogActions>
        <Button onClick={handleClose}>Close</Button>
        <Button onClick={handleDelete} autoFocus>
          {isFetching ? 'Deleting...' : 'Delete'}
        </Button>
      </DialogActions>
    </Dialog >
  )
}

export default DeleteModal