import { Modal, <PERSON><PERSON>, <PERSON><PERSON>Title, DialogContent, <PERSON>alog<PERSON><PERSON>, Button, DialogContentText } from '@mui/material'
import React from 'react'
import { deleteTask } from '../../redux/action/task'
import { useDispatch, useSelector } from 'react-redux'

const DeleteModal = ({ open, setOpen, taskId }) => {

  ////////////////////////////////////// VARIABLES ///////////////////////////////////////
  const { isFetching } = useSelector(state => state.task)
  const dispatch = useDispatch()

  ////////////////////////////////////// FUNCTIONS ///////////////////////////////////////
  const handleClose = () => {
    setOpen(false)
  }
  const handleDelete = () => {
    dispatch(deleteTask(taskId))
    setOpen(false)
  }

  return (
    <Dialog
      open={open}
      onClose={() => setOpen(false)}
    >
      <DialogTitle id="alert-dialog-title">
        Delete the Task?
      </DialogTitle>
      <DialogContent>
        <DialogContentText id="alert-dialog-description">
          Are you sure you want to delete this task?
        </DialogContentText>
      </DialogContent>
      <DialogActions>
        <Button onClick={handleClose}>Close</Button>
        <Button onClick={handleDelete} autoFocus>
          {isFetching ? 'Deleting...' : 'Delete'}
        </Button>
      </DialogActions>
    </Dialog >
  )
}

export default DeleteModal