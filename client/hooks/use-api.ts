'use client'

import { useCallback, useEffect, useRef, useState } from 'react'
import type { 
  ApiResponse, 
  ApiErrorResponse, 
  RequestConfig,
  QueryOptions,
  MutationOptions 
} from '@/types/api'

// ============================================================================
// API HOOK TYPES
// ============================================================================

interface UseApiState<T> {
  data: T | null
  loading: boolean
  error: ApiErrorResponse | null
  success: boolean
}

interface UseApiReturn<T> extends UseApiState<T> {
  execute: () => Promise<void>
  reset: () => void
  refetch: () => Promise<void>
}

interface UseMutationReturn<TData, TVariables> {
  mutate: (variables: TVariables) => Promise<TData>
  mutateAsync: (variables: TVariables) => Promise<TData>
  data: TData | null
  loading: boolean
  error: ApiErrorResponse | null
  success: boolean
  reset: () => void
}

// ============================================================================
// API CLIENT CONFIGURATION
// ============================================================================

const API_BASE_URL = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:8080/api'

class ApiClient {
  private baseURL: string
  private defaultHeaders: Record<string, string>

  constructor(baseURL: string) {
    this.baseURL = baseURL
    this.defaultHeaders = {
      'Content-Type': 'application/json',
    }
  }

  private async request<T>(
    endpoint: string,
    config: RequestConfig = {}
  ): Promise<ApiResponse<T>> {
    const {
      method = 'GET',
      headers = {},
      data,
      params,
      timeout = 10000,
    } = config

    // Build URL with query parameters
    const url = new URL(`${this.baseURL}${endpoint}`)
    if (params) {
      Object.entries(params).forEach(([key, value]) => {
        url.searchParams.append(key, String(value))
      })
    }

    // Get auth token from localStorage
    const token = typeof window !== 'undefined' 
      ? localStorage.getItem('accessToken') 
      : null

    // Build headers
    const requestHeaders = {
      ...this.defaultHeaders,
      ...headers,
      ...(token && { Authorization: `Bearer ${token}` }),
    }

    // Create abort controller for timeout
    const controller = new AbortController()
    const timeoutId = setTimeout(() => controller.abort(), timeout)

    try {
      const response = await fetch(url.toString(), {
        method,
        headers: requestHeaders,
        body: data ? JSON.stringify(data) : undefined,
        signal: controller.signal,
      })

      clearTimeout(timeoutId)

      // Handle non-JSON responses
      const contentType = response.headers.get('content-type')
      if (!contentType?.includes('application/json')) {
        if (!response.ok) {
          throw new Error(`HTTP ${response.status}: ${response.statusText}`)
        }
        return { success: true, data: null as T }
      }

      const result = await response.json()

      if (!response.ok) {
        throw {
          ...result,
          statusCode: response.status,
          timestamp: new Date().toISOString(),
          path: endpoint,
        } as ApiErrorResponse
      }

      return result
    } catch (error) {
      clearTimeout(timeoutId)
      
      if (error instanceof Error) {
        if (error.name === 'AbortError') {
          throw {
            error: 'Request Timeout',
            message: 'The request took too long to complete',
            statusCode: 408,
            timestamp: new Date().toISOString(),
            path: endpoint,
          } as ApiErrorResponse
        }
        
        throw {
          error: 'Network Error',
          message: error.message,
          statusCode: 0,
          timestamp: new Date().toISOString(),
          path: endpoint,
        } as ApiErrorResponse
      }
      
      throw error
    }
  }

  async get<T>(endpoint: string, config?: RequestConfig): Promise<ApiResponse<T>> {
    return this.request<T>(endpoint, { ...config, method: 'GET' })
  }

  async post<T>(endpoint: string, data?: unknown, config?: RequestConfig): Promise<ApiResponse<T>> {
    return this.request<T>(endpoint, { ...config, method: 'POST', data })
  }

  async put<T>(endpoint: string, data?: unknown, config?: RequestConfig): Promise<ApiResponse<T>> {
    return this.request<T>(endpoint, { ...config, method: 'PUT', data })
  }

  async patch<T>(endpoint: string, data?: unknown, config?: RequestConfig): Promise<ApiResponse<T>> {
    return this.request<T>(endpoint, { ...config, method: 'PATCH', data })
  }

  async delete<T>(endpoint: string, config?: RequestConfig): Promise<ApiResponse<T>> {
    return this.request<T>(endpoint, { ...config, method: 'DELETE' })
  }
}

// Global API client instance
const apiClient = new ApiClient(API_BASE_URL)

// ============================================================================
// USE API HOOK
// ============================================================================

export function useApi<T>(
  endpoint: string,
  config: RequestConfig = {},
  options: QueryOptions<T> = {}
): UseApiReturn<T> {
  const [state, setState] = useState<UseApiState<T>>({
    data: null,
    loading: false,
    error: null,
    success: false,
  })

  const abortControllerRef = useRef<AbortController | null>(null)
  const mountedRef = useRef(true)

  const execute = useCallback(async () => {
    if (!mountedRef.current) return

    // Cancel previous request
    if (abortControllerRef.current) {
      abortControllerRef.current.abort()
    }

    abortControllerRef.current = new AbortController()

    setState(prev => ({ ...prev, loading: true, error: null }))

    try {
      const response = await apiClient.request<T>(endpoint, {
        ...config,
        signal: abortControllerRef.current.signal,
      })

      if (!mountedRef.current) return

      setState({
        data: response.data || null,
        loading: false,
        error: null,
        success: true,
      })

      options.onSuccess?.(response.data as T)
    } catch (error) {
      if (!mountedRef.current) return

      const apiError = error as ApiErrorResponse
      setState({
        data: null,
        loading: false,
        error: apiError,
        success: false,
      })

      options.onError?.(apiError)
    }
  }, [endpoint, config, options])

  const reset = useCallback(() => {
    setState({
      data: null,
      loading: false,
      error: null,
      success: false,
    })
  }, [])

  const refetch = useCallback(async () => {
    await execute()
  }, [execute])

  useEffect(() => {
    if (options.enabled !== false) {
      execute()
    }

    return () => {
      if (abortControllerRef.current) {
        abortControllerRef.current.abort()
      }
    }
  }, [execute, options.enabled])

  useEffect(() => {
    return () => {
      mountedRef.current = false
    }
  }, [])

  return {
    ...state,
    execute,
    reset,
    refetch,
  }
}

// ============================================================================
// USE MUTATION HOOK
// ============================================================================

export function useMutation<TData = unknown, TVariables = unknown>(
  mutationFn: (variables: TVariables) => Promise<ApiResponse<TData>>,
  options: MutationOptions<TData, TVariables> = {}
): UseMutationReturn<TData, TVariables> {
  const [state, setState] = useState<UseApiState<TData>>({
    data: null,
    loading: false,
    error: null,
    success: false,
  })

  const mountedRef = useRef(true)

  const mutateAsync = useCallback(async (variables: TVariables): Promise<TData> => {
    if (!mountedRef.current) throw new Error('Component unmounted')

    setState(prev => ({ ...prev, loading: true, error: null }))

    try {
      options.onMutate?.(variables)

      const response = await mutationFn(variables)

      if (!mountedRef.current) throw new Error('Component unmounted')

      const data = response.data as TData

      setState({
        data,
        loading: false,
        error: null,
        success: true,
      })

      options.onSuccess?.(data, variables)
      return data
    } catch (error) {
      if (!mountedRef.current) throw error

      const apiError = error as ApiErrorResponse
      setState({
        data: null,
        loading: false,
        error: apiError,
        success: false,
      })

      options.onError?.(apiError, variables)
      throw error
    } finally {
      if (mountedRef.current) {
        options.onSettled?.(state.data, state.error, variables)
      }
    }
  }, [mutationFn, options, state.data, state.error])

  const mutate = useCallback(async (variables: TVariables): Promise<TData> => {
    try {
      return await mutateAsync(variables)
    } catch (error) {
      // Silently handle errors for mutate (non-async version)
      throw error
    }
  }, [mutateAsync])

  const reset = useCallback(() => {
    setState({
      data: null,
      loading: false,
      error: null,
      success: false,
    })
  }, [])

  useEffect(() => {
    return () => {
      mountedRef.current = false
    }
  }, [])

  return {
    mutate,
    mutateAsync,
    ...state,
    reset,
  }
}

// ============================================================================
// CONVENIENCE HOOKS
// ============================================================================

export const useGet = <T>(endpoint: string, options?: QueryOptions<T>) =>
  useApi<T>(endpoint, { method: 'GET' }, options)

export const usePost = <TData, TVariables>(endpoint: string) =>
  useMutation<TData, TVariables>((variables) =>
    apiClient.post<TData>(endpoint, variables)
  )

export const usePut = <TData, TVariables>(endpoint: string) =>
  useMutation<TData, TVariables>((variables) =>
    apiClient.put<TData>(endpoint, variables)
  )

export const useDelete = <TData>(endpoint: string) =>
  useMutation<TData, void>(() =>
    apiClient.delete<TData>(endpoint)
  )

// Export the API client for direct use
export { apiClient }
