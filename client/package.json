{"name": "goreal-client", "private": true, "version": "0.1.0", "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint", "type-check": "tsc --noEmit", "type-check:watch": "tsc --noEmit --watch", "dev:vite": "vite", "build:vite": "vite build", "preview:vite": "vite preview"}, "dependencies": {"@coreui/react": "^5.0.0-alpha.0", "@emotion/react": "^11.11.1", "@emotion/styled": "^11.11.0", "@fullcalendar/core": "^6.1.9", "@fullcalendar/daygrid": "^6.1.9", "@fullcalendar/list": "^6.1.9", "@fullcalendar/react": "^6.1.9", "@fullcalendar/timegrid": "^6.1.9", "@hookform/resolvers": "^3.3.4", "@mui/base": "^5.0.0-beta.11", "@mui/icons-material": "^5.14.0", "@mui/material": "^5.14.0", "@mui/x-data-grid": "^6.10.2", "@mui/x-date-pickers": "^6.11.0", "@pubnub/react-chat-components": "^0.30.0", "@radix-ui/react-accordion": "^1.1.2", "@radix-ui/react-alert-dialog": "^1.0.5", "@radix-ui/react-avatar": "^1.1.10", "@radix-ui/react-checkbox": "^1.0.4", "@radix-ui/react-dialog": "^1.0.5", "@radix-ui/react-dropdown-menu": "^2.1.15", "@radix-ui/react-form": "^0.0.3", "@radix-ui/react-icons": "^1.3.0", "@radix-ui/react-label": "^2.0.2", "@radix-ui/react-navigation-menu": "^1.1.4", "@radix-ui/react-popover": "^1.0.7", "@radix-ui/react-progress": "^1.0.3", "@radix-ui/react-scroll-area": "^1.0.5", "@radix-ui/react-select": "^2.0.0", "@radix-ui/react-separator": "^1.0.3", "@radix-ui/react-slider": "^1.1.2", "@radix-ui/react-slot": "^1.2.3", "@radix-ui/react-switch": "^1.0.3", "@radix-ui/react-tabs": "^1.0.4", "@radix-ui/react-toast": "^1.1.5", "@radix-ui/react-tooltip": "^1.0.7", "@react-pdf/renderer": "^3.1.12", "@reduxjs/toolkit": "^1.9.5", "@stripe/stripe-js": "^7.3.1", "@supabase/ssr": "^0.1.0", "@supabase/supabase-js": "^2.39.3", "@tanstack/react-query": "^5.17.19", "@tanstack/react-query-devtools": "^5.80.10", "@tanstack/react-table": "^8.11.8", "@tremor/react": "^3.18.7", "axios": "^1.4.0", "bootstrap-icons": "^1.10.5", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "cmdk": "^0.2.0", "date-fns": "^3.3.1", "dayjs": "^1.11.9", "email-validator": "^2.0.4", "framer-motion": "^10.18.0", "html2canvas": "^1.4.1", "js-cookie": "^3.0.5", "jsbarcode": "^3.11.5", "jspdf": "^2.5.1", "lucide-react": "^0.522.0", "moment": "^2.29.4", "next": "^15.3.4", "next-themes": "^0.2.1", "number-to-words": "^1.2.4", "pdfmake": "^0.2.7", "qrcode.react": "^3.1.0", "react": "^18.2.0", "react-barcode": "^1.4.6", "react-beautiful-dnd": "^13.1.1", "react-big-calendar": "^1.8.4", "react-bootstrap-icons": "^1.10.3", "react-day-picker": "^9.7.0", "react-dom": "^18.2.0", "react-file-base64": "^1.0.3", "react-hook-form": "^7.49.3", "react-hot-toast": "^2.4.1", "react-icons": "^4.10.1", "react-loader-spinner": "^5.3.4", "react-number-format": "^5.3.1", "react-pdf": "^7.3.3", "react-redux": "^8.1.1", "react-router-dom": "^6.14.1", "react-top-loading-bar": "^2.3.1", "react-transition-group": "^4.4.5", "recharts": "^2.15.4", "redux-persist": "^6.0.0", "rsuite": "^5.38.0", "stream-chat": "^8.10.1", "styled-components": "^6.0.7", "swiper": "^10.1.0", "tailwind-merge": "^3.3.1", "tailwindcss-animate": "^1.0.7", "timeago.js": "^4.0.2", "viem": "^2.7.13", "wagmi": "^2.5.7", "zod": "^3.22.4", "zustand": "^4.5.0"}, "devDependencies": {"@types/js-cookie": "^3.0.6", "@types/node": "^20.11.16", "@types/react": "^18.2.52", "@types/react-dom": "^18.2.18", "@vitejs/plugin-react": "^4.0.1", "autoprefixer": "^10.4.17", "eslint": "^8.56.0", "eslint-config-next": "14.2.3", "eslint-plugin-react": "^7.32.2", "eslint-plugin-react-hooks": "^4.6.0", "eslint-plugin-react-refresh": "^0.4.1", "postcss": "^8.4.33", "tailwindcss": "^3.4.1", "typescript": "^5.3.3", "vite": "^4.4.0"}}