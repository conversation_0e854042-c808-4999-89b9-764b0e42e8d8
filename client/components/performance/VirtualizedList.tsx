'use client'

// ============================================================================
// VIRTUALIZED LIST COMPONENT FOR PERFORMANCE
// ============================================================================

import React, { useCallback, useEffect, useMemo, useRef, useState } from 'react'
import { useVirtualScrolling, useIntersectionObserver } from '@/lib/performance'

interface VirtualizedListProps<T> {
  items: T[]
  itemHeight: number
  containerHeight: number
  renderItem: (item: T, index: number) => React.ReactNode
  className?: string
  onLoadMore?: () => void
  hasMore?: boolean
  loading?: boolean
  overscan?: number
  estimatedItemSize?: number
}

export function VirtualizedList<T>({
  items,
  itemHeight,
  containerHeight,
  renderItem,
  className = '',
  onLoadMore,
  hasMore = false,
  loading = false,
  overscan = 5,
}: VirtualizedListProps<T>) {
  const containerRef = useRef<HTMLDivElement>(null)
  const [scrollTop, setScrollTop] = useState(0)

  // Calculate visible items with overscan
  const visibleItems = useMemo(() => {
    const startIndex = Math.max(0, Math.floor(scrollTop / itemHeight) - overscan)
    const endIndex = Math.min(
      items.length,
      Math.ceil((scrollTop + containerHeight) / itemHeight) + overscan
    )

    return {
      startIndex,
      endIndex,
      items: items.slice(startIndex, endIndex),
      totalHeight: items.length * itemHeight,
      offsetY: startIndex * itemHeight,
    }
  }, [items, itemHeight, containerHeight, scrollTop, overscan])

  // Handle scroll events with throttling
  const handleScroll = useCallback((event: React.UIEvent<HTMLDivElement>) => {
    const newScrollTop = event.currentTarget.scrollTop
    setScrollTop(newScrollTop)

    // Trigger load more when near bottom
    if (onLoadMore && hasMore && !loading) {
      const scrollHeight = event.currentTarget.scrollHeight
      const clientHeight = event.currentTarget.clientHeight
      const threshold = scrollHeight - clientHeight - itemHeight * 5

      if (newScrollTop >= threshold) {
        onLoadMore()
      }
    }
  }, [onLoadMore, hasMore, loading, itemHeight])

  return (
    <div
      ref={containerRef}
      className={`overflow-auto ${className}`}
      style={{ height: containerHeight }}
      onScroll={handleScroll}
    >
      <div style={{ height: visibleItems.totalHeight, position: 'relative' }}>
        <div
          style={{
            transform: `translateY(${visibleItems.offsetY}px)`,
            position: 'absolute',
            top: 0,
            left: 0,
            right: 0,
          }}
        >
          {visibleItems.items.map((item, index) => (
            <div
              key={visibleItems.startIndex + index}
              style={{ height: itemHeight }}
              className="flex-shrink-0"
            >
              {renderItem(item, visibleItems.startIndex + index)}
            </div>
          ))}
        </div>
      </div>
      
      {loading && (
        <div className="flex justify-center items-center py-4">
          <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-blue-600"></div>
          <span className="ml-2 text-sm text-gray-600">Loading more...</span>
        </div>
      )}
    </div>
  )
}

// ============================================================================
// LAZY LOADING IMAGE COMPONENT
// ============================================================================

interface LazyImageProps {
  src: string
  alt: string
  width?: number
  height?: number
  className?: string
  placeholder?: string
  onLoad?: () => void
  onError?: () => void
}

export function LazyImage({
  src,
  alt,
  width,
  height,
  className = '',
  placeholder = 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMjAwIiBoZWlnaHQ9IjIwMCIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj48cmVjdCB3aWR0aD0iMTAwJSIgaGVpZ2h0PSIxMDAlIiBmaWxsPSIjZGRkIi8+PC9zdmc+',
  onLoad,
  onError,
}: LazyImageProps) {
  const [isLoaded, setIsLoaded] = useState(false)
  const [hasError, setHasError] = useState(false)
  const [imageSrc, setImageSrc] = useState(placeholder)
  const imgRef = useRef<HTMLImageElement>(null)

  const { isIntersecting, hasIntersected } = useIntersectionObserver(imgRef, {
    threshold: 0.1,
    rootMargin: '50px',
  })

  useEffect(() => {
    if (hasIntersected && !isLoaded && !hasError) {
      const img = new Image()
      img.onload = () => {
        setImageSrc(src)
        setIsLoaded(true)
        onLoad?.()
      }
      img.onerror = () => {
        setHasError(true)
        onError?.()
      }
      img.src = src
    }
  }, [hasIntersected, isLoaded, hasError, src, onLoad, onError])

  return (
    <img
      ref={imgRef}
      src={imageSrc}
      alt={alt}
      width={width}
      height={height}
      className={`transition-opacity duration-300 ${
        isLoaded ? 'opacity-100' : 'opacity-50'
      } ${className}`}
      loading="lazy"
    />
  )
}

// ============================================================================
// PERFORMANCE OPTIMIZED DATA TABLE
// ============================================================================

interface Column<T> {
  key: keyof T
  header: string
  width?: number
  render?: (value: any, item: T, index: number) => React.ReactNode
  sortable?: boolean
}

interface DataTableProps<T> {
  data: T[]
  columns: Column<T>[]
  className?: string
  rowHeight?: number
  maxHeight?: number
  onSort?: (key: keyof T, direction: 'asc' | 'desc') => void
  sortKey?: keyof T
  sortDirection?: 'asc' | 'desc'
  loading?: boolean
  emptyMessage?: string
}

export function DataTable<T extends Record<string, any>>({
  data,
  columns,
  className = '',
  rowHeight = 48,
  maxHeight = 400,
  onSort,
  sortKey,
  sortDirection,
  loading = false,
  emptyMessage = 'No data available',
}: DataTableProps<T>) {
  const handleSort = useCallback(
    (key: keyof T) => {
      if (!onSort) return
      
      const newDirection = 
        sortKey === key && sortDirection === 'asc' ? 'desc' : 'asc'
      onSort(key, newDirection)
    },
    [onSort, sortKey, sortDirection]
  )

  const renderRow = useCallback(
    (item: T, index: number) => (
      <div
        className="flex items-center border-b border-gray-200 hover:bg-gray-50"
        style={{ height: rowHeight }}
      >
        {columns.map((column) => (
          <div
            key={String(column.key)}
            className="px-4 py-2 flex-shrink-0 overflow-hidden"
            style={{ width: column.width || 'auto' }}
          >
            {column.render
              ? column.render(item[column.key], item, index)
              : String(item[column.key] || '')
            }
          </div>
        ))}
      </div>
    ),
    [columns, rowHeight]
  )

  if (loading) {
    return (
      <div className={`border rounded-lg ${className}`}>
        <div className="flex justify-center items-center py-8">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
          <span className="ml-3 text-gray-600">Loading...</span>
        </div>
      </div>
    )
  }

  if (data.length === 0) {
    return (
      <div className={`border rounded-lg ${className}`}>
        <div className="flex justify-center items-center py-8 text-gray-500">
          {emptyMessage}
        </div>
      </div>
    )
  }

  return (
    <div className={`border rounded-lg overflow-hidden ${className}`}>
      {/* Header */}
      <div className="bg-gray-50 border-b border-gray-200">
        <div className="flex">
          {columns.map((column) => (
            <div
              key={String(column.key)}
              className={`px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider flex-shrink-0 ${
                column.sortable ? 'cursor-pointer hover:bg-gray-100' : ''
              }`}
              style={{ width: column.width || 'auto' }}
              onClick={() => column.sortable && handleSort(column.key)}
            >
              <div className="flex items-center">
                {column.header}
                {column.sortable && sortKey === column.key && (
                  <span className="ml-1">
                    {sortDirection === 'asc' ? '↑' : '↓'}
                  </span>
                )}
              </div>
            </div>
          ))}
        </div>
      </div>

      {/* Body with virtualization for large datasets */}
      {data.length > 50 ? (
        <VirtualizedList
          items={data}
          itemHeight={rowHeight}
          containerHeight={Math.min(maxHeight, data.length * rowHeight)}
          renderItem={renderRow}
        />
      ) : (
        <div style={{ maxHeight }}>
          {data.map((item, index) => (
            <div key={index}>
              {renderRow(item, index)}
            </div>
          ))}
        </div>
      )}
    </div>
  )
}

// ============================================================================
// INFINITE SCROLL COMPONENT
// ============================================================================

interface InfiniteScrollProps {
  children: React.ReactNode
  onLoadMore: () => void
  hasMore: boolean
  loading: boolean
  threshold?: number
  className?: string
}

export function InfiniteScroll({
  children,
  onLoadMore,
  hasMore,
  loading,
  threshold = 100,
  className = '',
}: InfiniteScrollProps) {
  const sentinelRef = useRef<HTMLDivElement>(null)

  const { isIntersecting } = useIntersectionObserver(sentinelRef, {
    threshold: 0.1,
    rootMargin: `${threshold}px`,
  })

  useEffect(() => {
    if (isIntersecting && hasMore && !loading) {
      onLoadMore()
    }
  }, [isIntersecting, hasMore, loading, onLoadMore])

  return (
    <div className={className}>
      {children}
      
      {hasMore && (
        <div
          ref={sentinelRef}
          className="flex justify-center items-center py-4"
        >
          {loading ? (
            <>
              <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-blue-600"></div>
              <span className="ml-2 text-sm text-gray-600">Loading more...</span>
            </>
          ) : (
            <span className="text-sm text-gray-400">Scroll for more</span>
          )}
        </div>
      )}
    </div>
  )
}

// ============================================================================
// PERFORMANCE METRICS DISPLAY
// ============================================================================

interface PerformanceMetricsProps {
  className?: string
}

export function PerformanceMetrics({ className = '' }: PerformanceMetricsProps) {
  const [metrics, setMetrics] = useState<any>(null)

  useEffect(() => {
    const updateMetrics = () => {
      if (typeof window !== 'undefined' && 'performance' in window) {
        const navigation = performance.getEntriesByType('navigation')[0] as PerformanceNavigationTiming
        const paint = performance.getEntriesByType('paint')
        
        const newMetrics = {
          domContentLoaded: Math.round(navigation.domContentLoadedEventEnd - navigation.domContentLoadedEventStart),
          loadComplete: Math.round(navigation.loadEventEnd - navigation.loadEventStart),
          firstPaint: paint.find(entry => entry.name === 'first-paint')?.startTime || 0,
          firstContentfulPaint: paint.find(entry => entry.name === 'first-contentful-paint')?.startTime || 0,
          memoryUsage: (performance as any).memory?.usedJSHeapSize || 0,
        }
        
        setMetrics(newMetrics)
      }
    }

    updateMetrics()
    const interval = setInterval(updateMetrics, 5000)
    
    return () => clearInterval(interval)
  }, [])

  if (!metrics) return null

  return (
    <div className={`bg-gray-100 p-4 rounded-lg text-xs ${className}`}>
      <h3 className="font-semibold mb-2">Performance Metrics</h3>
      <div className="grid grid-cols-2 gap-2">
        <div>
          <span className="text-gray-600">DOM Ready:</span>
          <span className="ml-1 font-mono">{metrics.domContentLoaded}ms</span>
        </div>
        <div>
          <span className="text-gray-600">Load Complete:</span>
          <span className="ml-1 font-mono">{metrics.loadComplete}ms</span>
        </div>
        <div>
          <span className="text-gray-600">First Paint:</span>
          <span className="ml-1 font-mono">{Math.round(metrics.firstPaint)}ms</span>
        </div>
        <div>
          <span className="text-gray-600">First Contentful:</span>
          <span className="ml-1 font-mono">{Math.round(metrics.firstContentfulPaint)}ms</span>
        </div>
        <div className="col-span-2">
          <span className="text-gray-600">Memory Usage:</span>
          <span className="ml-1 font-mono">
            {(metrics.memoryUsage / 1024 / 1024).toFixed(1)}MB
          </span>
        </div>
      </div>
    </div>
  )
}
