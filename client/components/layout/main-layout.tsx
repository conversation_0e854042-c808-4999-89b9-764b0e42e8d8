'use client'

import { CommandPalette } from '@/components/ui/command-palette'
import { usePathname } from 'next/navigation'
import { useCallback, useEffect, useState } from 'react'
import { useSelector } from 'react-redux'
import { Navbar } from './navbar'
import { Sidebar } from './sidebar'
import type { User, UserRole } from '@/types'

interface RootState {
  user: {
    loggedUser: User | null
  }
}

interface MainLayoutProps {
  children: React.ReactNode
}

// Route configuration with proper typing
const ROUTE_TITLES: Record<string, string> = {
  'dashboard': 'Dashboard',
  'leads': 'Leads Management',
  'sales': 'Sales',
  'tasks': 'Tasks',
  'clients': 'Clients',
  'employees': 'Employees',
  'societies': 'Societies',
  'projects': 'Projects',
  'inventories': 'Inventories',
  'cashbook': 'Cash Book',
  'voucher': 'Vouchers',
  'transcript': 'Transcript',
  'authorization': 'Authorization',
  'notifications': 'Notifications'
} as const

// Pages that should not show the main layout
const EXCLUDED_PATHS = ['/auth', '/login', '/register', '/client', '/download'] as const

// Breakpoint for mobile/desktop detection
const MOBILE_BREAKPOINT = 1024

export function MainLayout({ children }: MainLayoutProps) {
  const [sidebarOpen, setSidebarOpen] = useState(true)
  const [commandPaletteOpen, setCommandPaletteOpen] = useState(false)
  const { loggedUser } = useSelector((state: RootState) => state.user)
  const pathname = usePathname()

  // Memoized handlers for better performance
  const handleSidebarToggle = useCallback((open: boolean) => {
    setSidebarOpen(open)
  }, [])

  const handleCommandPaletteToggle = useCallback((open: boolean) => {
    setCommandPaletteOpen(open)
  }, [])

  // Auto-collapse sidebar on mobile with proper cleanup
  useEffect(() => {
    const handleResize = () => {
      const isMobile = window.innerWidth < MOBILE_BREAKPOINT
      setSidebarOpen(!isMobile)
    }

    // Set initial state
    handleResize()

    window.addEventListener('resize', handleResize, { passive: true })
    return () => window.removeEventListener('resize', handleResize)
  }, [])

  // Close sidebar on mobile when navigating
  useEffect(() => {
    if (typeof window !== 'undefined' && window.innerWidth < MOBILE_BREAKPOINT) {
      setSidebarOpen(false)
    }
  }, [pathname])

  // Command palette keyboard shortcut with proper accessibility
  useEffect(() => {
    const handleKeyDown = (e: KeyboardEvent) => {
      // Cmd+K (Mac) or Ctrl+K (Windows/Linux)
      if ((e.metaKey || e.ctrlKey) && e.key === 'k') {
        e.preventDefault()
        setCommandPaletteOpen(true)
      }

      // Escape to close command palette
      if (e.key === 'Escape' && commandPaletteOpen) {
        setCommandPaletteOpen(false)
      }
    }

    document.addEventListener('keydown', handleKeyDown)
    return () => document.removeEventListener('keydown', handleKeyDown)
  }, [commandPaletteOpen])

  // Check if current path should exclude the main layout
  const shouldExcludeLayout = useCallback((path: string): boolean => {
    return EXCLUDED_PATHS.some(excludedPath => path.startsWith(excludedPath))
  }, [])

  // Get page title from pathname with proper typing
  const getPageTitle = useCallback((path: string): string => {
    const segments = path.split('/').filter(Boolean)
    if (segments.length === 0) return 'Dashboard'

    const firstSegment = segments[0]
    return ROUTE_TITLES[firstSegment] ||
           firstSegment.charAt(0).toUpperCase() + firstSegment.slice(1)
  }, [])

  // Early returns for excluded paths or unauthenticated users
  if (shouldExcludeLayout(pathname)) {
    return <>{children}</>
  }

  if (!loggedUser) {
    return <>{children}</>
  }

  const pageTitle = getPageTitle(pathname)

  return (
    <div
      className="flex h-screen bg-background gradient-mesh relative overflow-hidden"
      role="application"
      aria-label="GoReal Platform"
    >
      {/* Floating background elements - decorative only */}
      <div
        className="absolute top-20 left-20 w-32 h-32 gradient-aurora rounded-full opacity-10 animate-float blur-xl pointer-events-none"
        aria-hidden="true"
      />
      <div
        className="absolute top-40 right-32 w-24 h-24 gradient-sunset rounded-full opacity-10 animate-float floating-delayed blur-xl pointer-events-none"
        aria-hidden="true"
      />
      <div
        className="absolute bottom-32 left-40 w-28 h-28 gradient-cosmic rounded-full opacity-10 animate-float blur-xl pointer-events-none"
        aria-hidden="true"
      />
      <div
        className="absolute bottom-20 right-20 w-20 h-20 gradient-royal rounded-full opacity-10 animate-float floating-delayed blur-xl pointer-events-none"
        aria-hidden="true"
      />

      {/* Sidebar Navigation */}
      <Sidebar
        isOpen={sidebarOpen}
        setIsOpen={handleSidebarToggle}
        userRole={loggedUser.role as UserRole}
      />

      {/* Main content area */}
      <div className="flex-1 flex flex-col overflow-hidden relative">
        {/* Top Navigation */}
        <Navbar
          sidebarOpen={sidebarOpen}
          setSidebarOpen={handleSidebarToggle}
          title={pageTitle}
        />

        {/* Main content */}
        <main
          className="flex-1 overflow-y-auto bg-background/30 backdrop-blur-md relative"
          role="main"
          aria-label={`${pageTitle} content`}
        >
          <div className="container mx-auto p-8 max-w-7xl animate-fade-in">
            {children}
          </div>
        </main>
      </div>

      {/* Command Palette */}
      <CommandPalette
        isOpen={commandPaletteOpen}
        onClose={() => handleCommandPaletteToggle(false)}
      />
    </div>
  )
}
