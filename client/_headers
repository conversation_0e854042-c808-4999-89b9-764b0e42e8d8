# Cloudflare Pages Headers Configuration

/*
  X-Frame-Options: DENY
  X-Content-Type-Options: nosniff
  X-XSS-Protection: 1; mode=block
  Referrer-Policy: strict-origin-when-cross-origin
  Permissions-Policy: camera=(), microphone=(), geolocation=()

# Cache static assets
/static/*
  Cache-Control: public, max-age=31536000, immutable

/_next/static/*
  Cache-Control: public, max-age=31536000, immutable

# Cache images
*.jpg
  Cache-Control: public, max-age=86400

*.jpeg
  Cache-Control: public, max-age=86400

*.png
  Cache-Control: public, max-age=86400

*.gif
  Cache-Control: public, max-age=86400

*.webp
  Cache-Control: public, max-age=86400

*.svg
  Cache-Control: public, max-age=86400

# Cache fonts
*.woff
  Cache-Control: public, max-age=31536000, immutable

*.woff2
  Cache-Control: public, max-age=31536000, immutable

*.ttf
  Cache-Control: public, max-age=31536000, immutable

*.eot
  Cache-Control: public, max-age=31536000, immutable

# API routes - no cache
/api/*
  Cache-Control: no-cache, no-store, must-revalidate

# HTML pages - short cache
*.html
  Cache-Control: public, max-age=300
