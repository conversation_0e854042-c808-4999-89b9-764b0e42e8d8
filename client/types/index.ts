// ============================================================================
// GOREAL PLATFORM - COMPREHENSIVE TYPE DEFINITIONS
// ============================================================================

// Base Types
export type UUID = string
export type Timestamp = string
export type Email = string
export type URL = string

// ============================================================================
// USER TYPES
// ============================================================================

export enum UserRole {
  ADMIN = 'admin',
  MANAGER = 'manager',
  EMPLOYEE = 'employee',
  USER = 'user',
  CLIENT = 'client'
}

export interface User {
  readonly id: UUID
  email: Email
  username: string
  fullName: string
  role: UserRole
  avatarUrl?: URL
  bio?: string
  isActive: boolean
  emailVerified: boolean
  createdAt: Timestamp
  updatedAt: Timestamp
  lastLoginAt?: Timestamp
}

export interface CreateUserRequest {
  email: Email
  password: string
  fullName: string
  username: string
  role?: UserRole
}

export interface UpdateUserRequest {
  fullName?: string
  username?: string
  bio?: string
  avatarUrl?: URL
}

// ============================================================================
// AUTHENTICATION TYPES
// ============================================================================

export interface AuthResponse {
  user: User
  accessToken: string
  refreshToken: string
  expiresIn: number
}

export interface LoginRequest {
  email: Email
  password: string
  rememberMe?: boolean
}

export interface RegisterRequest {
  email: Email
  password: string
  fullName: string
  username: string
  acceptTerms: boolean
}

export interface ResetPasswordRequest {
  email: Email
}

export interface ConfirmPasswordResetRequest {
  token: string
  newPassword: string
}

// ============================================================================
// TASK TYPES
// ============================================================================

export enum TaskStatus {
  PENDING = 'pending',
  IN_PROGRESS = 'in_progress',
  COMPLETED = 'completed',
  CANCELLED = 'cancelled'
}

export enum TaskPriority {
  LOW = 'low',
  MEDIUM = 'medium',
  HIGH = 'high',
  URGENT = 'urgent'
}

export interface Task {
  readonly id: UUID
  title: string
  description?: string
  status: TaskStatus
  priority: TaskPriority
  assigneeId?: UUID
  assignee?: User
  createdBy: UUID
  creator?: User
  dueDate?: Timestamp
  completedAt?: Timestamp
  createdAt: Timestamp
  updatedAt: Timestamp
  tags?: string[]
  attachments?: string[]
}

export interface CreateTaskRequest {
  title: string
  description?: string
  priority?: TaskPriority
  assigneeId?: UUID
  dueDate?: Timestamp
  tags?: string[]
}

export interface UpdateTaskRequest {
  title?: string
  description?: string
  status?: TaskStatus
  priority?: TaskPriority
  assigneeId?: UUID
  dueDate?: Timestamp
  tags?: string[]
}

export interface TaskFilters {
  status?: TaskStatus
  priority?: TaskPriority
  assigneeId?: UUID
  createdBy?: UUID
  search?: string
  tags?: string[]
  dateFrom?: Timestamp
  dateTo?: Timestamp
  limit?: number
  offset?: number
}

// ============================================================================
// LEAD TYPES
// ============================================================================

export enum LeadStatus {
  NEW = 'new',
  CONTACTED = 'contacted',
  QUALIFIED = 'qualified',
  PROPOSAL = 'proposal',
  NEGOTIATION = 'negotiation',
  CLOSED_WON = 'closed_won',
  CLOSED_LOST = 'closed_lost'
}

export enum LeadSource {
  WEBSITE = 'website',
  REFERRAL = 'referral',
  SOCIAL_MEDIA = 'social_media',
  ADVERTISEMENT = 'advertisement',
  COLD_CALL = 'cold_call',
  EMAIL_CAMPAIGN = 'email_campaign',
  OTHER = 'other'
}

export interface Lead {
  readonly id: UUID
  name: string
  email: Email
  phone?: string
  company?: string
  status: LeadStatus
  source: LeadSource
  assignedTo?: UUID
  assignee?: User
  estimatedValue?: number
  notes?: string
  createdAt: Timestamp
  updatedAt: Timestamp
  lastContactedAt?: Timestamp
}

export interface CreateLeadRequest {
  name: string
  email: Email
  phone?: string
  company?: string
  source: LeadSource
  assignedTo?: UUID
  estimatedValue?: number
  notes?: string
}

// ============================================================================
// NOTIFICATION TYPES
// ============================================================================

export enum NotificationType {
  TASK_ASSIGNED = 'task_assigned',
  TASK_COMPLETED = 'task_completed',
  LEAD_UPDATED = 'lead_updated',
  SYSTEM_ALERT = 'system_alert',
  REMINDER = 'reminder'
}

export interface Notification {
  readonly id: UUID
  type: NotificationType
  title: string
  message: string
  userId: UUID
  isRead: boolean
  data?: Record<string, unknown>
  createdAt: Timestamp
  readAt?: Timestamp
}

// ============================================================================
// API RESPONSE TYPES
// ============================================================================

export interface ApiResponse<T = unknown> {
  success: boolean
  data?: T
  message?: string
  error?: string
  errors?: Record<string, string[]>
}

export interface PaginatedResponse<T = unknown> {
  data: T[]
  pagination: {
    page: number
    limit: number
    total: number
    totalPages: number
    hasNext: boolean
    hasPrev: boolean
  }
}

export interface ApiError {
  message: string
  code?: string
  details?: Record<string, unknown>
}

// ============================================================================
// FORM TYPES
// ============================================================================

export interface FormField<T = string> {
  value: T
  error?: string
  touched: boolean
  required?: boolean
}

export interface FormState<T extends Record<string, unknown>> {
  fields: {
    [K in keyof T]: FormField<T[K]>
  }
  isValid: boolean
  isSubmitting: boolean
  isDirty: boolean
}

// ============================================================================
// UI COMPONENT TYPES
// ============================================================================

export interface SelectOption<T = string> {
  label: string
  value: T
  disabled?: boolean
  icon?: React.ReactNode
}

export interface TableColumn<T = unknown> {
  key: keyof T | string
  label: string
  sortable?: boolean
  width?: string
  align?: 'left' | 'center' | 'right'
  render?: (value: unknown, row: T) => React.ReactNode
}

export interface TableProps<T = unknown> {
  data: T[]
  columns: TableColumn<T>[]
  loading?: boolean
  pagination?: {
    page: number
    limit: number
    total: number
    onPageChange: (page: number) => void
    onLimitChange: (limit: number) => void
  }
  sorting?: {
    key: string
    direction: 'asc' | 'desc'
    onSort: (key: string, direction: 'asc' | 'desc') => void
  }
  selection?: {
    selectedRows: T[]
    onSelectionChange: (rows: T[]) => void
  }
}

// ============================================================================
// ANALYTICS TYPES
// ============================================================================

export interface DashboardStats {
  totalTasks: number
  completedTasks: number
  pendingTasks: number
  totalLeads: number
  convertedLeads: number
  totalRevenue: number
  monthlyGrowth: number
}

export interface ChartDataPoint {
  label: string
  value: number
  color?: string
}

export interface TimeSeriesData {
  timestamp: Timestamp
  value: number
  label?: string
}

// ============================================================================
// UTILITY TYPES
// ============================================================================

export type DeepPartial<T> = {
  [P in keyof T]?: T[P] extends object ? DeepPartial<T[P]> : T[P]
}

export type RequiredFields<T, K extends keyof T> = T & Required<Pick<T, K>>

export type OptionalFields<T, K extends keyof T> = Omit<T, K> & Partial<Pick<T, K>>

export type WithTimestamps<T> = T & {
  createdAt: Timestamp
  updatedAt: Timestamp
}

export type WithId<T> = T & {
  readonly id: UUID
}

// ============================================================================
// THEME TYPES
// ============================================================================

export type ThemeMode = 'light' | 'dark' | 'system'

export interface ThemeConfig {
  mode: ThemeMode
  primaryColor: string
  accentColor: string
  borderRadius: number
  fontFamily: string
}

// ============================================================================
// ROUTE TYPES
// ============================================================================

export interface RouteConfig {
  path: string
  title: string
  icon?: React.ReactNode
  roles?: UserRole[]
  children?: RouteConfig[]
}

// ============================================================================
// EXPORT ALL TYPES
// ============================================================================

export type * from './api'
export type * from './components'
