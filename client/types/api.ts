// ============================================================================
// API CLIENT TYPES
// ============================================================================

import type { 
  User, 
  Task, 
  Lead, 
  Notification, 
  AuthResponse,
  LoginRequest,
  RegisterRequest,
  CreateTaskRequest,
  UpdateTaskRequest,
  CreateLeadRequest,
  TaskFilters,
  ApiResponse,
  PaginatedResponse
} from './index'

// ============================================================================
// HTTP CLIENT TYPES
// ============================================================================

export interface RequestConfig {
  method?: 'GET' | 'POST' | 'PUT' | 'PATCH' | 'DELETE'
  headers?: Record<string, string>
  params?: Record<string, string | number | boolean>
  data?: unknown
  timeout?: number
  retries?: number
  cache?: boolean
}

export interface ApiClient {
  get<T = unknown>(url: string, config?: RequestConfig): Promise<ApiResponse<T>>
  post<T = unknown>(url: string, data?: unknown, config?: RequestConfig): Promise<ApiResponse<T>>
  put<T = unknown>(url: string, data?: unknown, config?: RequestConfig): Promise<ApiResponse<T>>
  patch<T = unknown>(url: string, data?: unknown, config?: RequestConfig): Promise<ApiResponse<T>>
  delete<T = unknown>(url: string, config?: RequestConfig): Promise<ApiResponse<T>>
}

// ============================================================================
// AUTH API TYPES
// ============================================================================

export interface AuthApi {
  login(request: LoginRequest): Promise<ApiResponse<AuthResponse>>
  register(request: RegisterRequest): Promise<ApiResponse<AuthResponse>>
  logout(): Promise<ApiResponse<void>>
  refreshToken(): Promise<ApiResponse<AuthResponse>>
  resetPassword(email: string): Promise<ApiResponse<void>>
  confirmPasswordReset(token: string, newPassword: string): Promise<ApiResponse<void>>
  getCurrentUser(): Promise<ApiResponse<User>>
}

// ============================================================================
// USER API TYPES
// ============================================================================

export interface UserApi {
  getProfile(): Promise<ApiResponse<User>>
  updateProfile(data: Partial<User>): Promise<ApiResponse<User>>
  uploadAvatar(file: File): Promise<ApiResponse<{ avatarUrl: string }>>
  getUser(id: string): Promise<ApiResponse<User>>
  getUserStats(id: string): Promise<ApiResponse<{
    totalTasks: number
    completedTasks: number
    totalLeads: number
    joinedAt: string
  }>>
  followUser(id: string): Promise<ApiResponse<void>>
  unfollowUser(id: string): Promise<ApiResponse<void>>
}

// ============================================================================
// TASK API TYPES
// ============================================================================

export interface TaskApi {
  getTasks(filters?: TaskFilters): Promise<ApiResponse<PaginatedResponse<Task>>>
  getTask(id: string): Promise<ApiResponse<Task>>
  createTask(data: CreateTaskRequest): Promise<ApiResponse<Task>>
  updateTask(id: string, data: UpdateTaskRequest): Promise<ApiResponse<Task>>
  deleteTask(id: string): Promise<ApiResponse<void>>
  completeTask(id: string, notes?: string): Promise<ApiResponse<Task>>
  assignTask(id: string, assigneeId: string): Promise<ApiResponse<Task>>
  bulkAssignTasks(taskIds: string[], assigneeId: string): Promise<ApiResponse<void>>
  getMyTasks(): Promise<ApiResponse<Task[]>>
  getOverdueTasks(): Promise<ApiResponse<Task[]>>
}

// ============================================================================
// LEAD API TYPES
// ============================================================================

export interface LeadApi {
  getLeads(filters?: {
    status?: string
    assignedTo?: string
    search?: string
    limit?: number
    offset?: number
  }): Promise<ApiResponse<PaginatedResponse<Lead>>>
  getLead(id: string): Promise<ApiResponse<Lead>>
  createLead(data: CreateLeadRequest): Promise<ApiResponse<Lead>>
  updateLead(id: string, data: Partial<Lead>): Promise<ApiResponse<Lead>>
  deleteLead(id: string): Promise<ApiResponse<void>>
  assignLead(id: string, assigneeId: string): Promise<ApiResponse<Lead>>
  convertLead(id: string): Promise<ApiResponse<void>>
}

// ============================================================================
// NOTIFICATION API TYPES
// ============================================================================

export interface NotificationApi {
  getNotifications(params?: {
    page?: number
    limit?: number
    unreadOnly?: boolean
  }): Promise<ApiResponse<PaginatedResponse<Notification>>>
  markAsRead(id: string): Promise<ApiResponse<void>>
  markAllAsRead(): Promise<ApiResponse<void>>
  getUnreadCount(): Promise<ApiResponse<{ count: number }>>
  deleteNotification(id: string): Promise<ApiResponse<void>>
}

// ============================================================================
// ANALYTICS API TYPES
// ============================================================================

export interface AnalyticsApi {
  getDashboardStats(): Promise<ApiResponse<{
    totalTasks: number
    completedTasks: number
    pendingTasks: number
    totalLeads: number
    convertedLeads: number
    totalRevenue: number
    monthlyGrowth: number
  }>>
  getSalesAnalytics(filters?: {
    dateFrom?: string
    dateTo?: string
    salespersonId?: string
  }): Promise<ApiResponse<{
    totalSales: number
    revenue: number
    averageValue: number
    conversionRate: number
    chartData: Array<{ date: string; value: number }>
  }>>
  getLeadAnalytics(filters?: {
    dateFrom?: string
    dateTo?: string
    assignedTo?: string
  }): Promise<ApiResponse<{
    totalLeads: number
    convertedLeads: number
    conversionRate: number
    averageTimeToConvert: number
    statusDistribution: Array<{ status: string; count: number }>
  }>>
  getUserPerformance(userId: string, period?: string): Promise<ApiResponse<{
    tasksCompleted: number
    leadsConverted: number
    revenue: number
    efficiency: number
    chartData: Array<{ date: string; tasks: number; leads: number }>
  }>>
}

// ============================================================================
// FILE UPLOAD TYPES
// ============================================================================

export interface FileUploadApi {
  uploadFile(file: File, type: 'avatar' | 'document' | 'image'): Promise<ApiResponse<{
    url: string
    filename: string
    size: number
    type: string
  }>>
  deleteFile(url: string): Promise<ApiResponse<void>>
}

// ============================================================================
// WEBSOCKET TYPES
// ============================================================================

export interface WebSocketMessage<T = unknown> {
  type: string
  data: T
  timestamp: string
  id?: string
}

export interface WebSocketClient {
  connect(): void
  disconnect(): void
  subscribe(channel: string, callback: (message: WebSocketMessage) => void): void
  unsubscribe(channel: string): void
  send(message: WebSocketMessage): void
  isConnected(): boolean
}

// ============================================================================
// ERROR HANDLING TYPES
// ============================================================================

export interface ApiErrorResponse {
  error: string
  message: string
  statusCode: number
  timestamp: string
  path: string
  details?: Record<string, unknown>
}

export interface ValidationError {
  field: string
  message: string
  code: string
}

export interface ApiValidationErrorResponse extends ApiErrorResponse {
  errors: ValidationError[]
}

// ============================================================================
// CACHE TYPES
// ============================================================================

export interface CacheConfig {
  ttl?: number // Time to live in milliseconds
  maxSize?: number // Maximum number of cached items
  strategy?: 'lru' | 'fifo' // Cache eviction strategy
}

export interface CacheEntry<T = unknown> {
  data: T
  timestamp: number
  ttl: number
}

export interface ApiCache {
  get<T = unknown>(key: string): CacheEntry<T> | null
  set<T = unknown>(key: string, data: T, ttl?: number): void
  delete(key: string): void
  clear(): void
  has(key: string): boolean
  size(): number
}

// ============================================================================
// REQUEST INTERCEPTOR TYPES
// ============================================================================

export interface RequestInterceptor {
  onRequest?: (config: RequestConfig) => RequestConfig | Promise<RequestConfig>
  onRequestError?: (error: Error) => Error | Promise<Error>
}

export interface ResponseInterceptor {
  onResponse?: <T>(response: ApiResponse<T>) => ApiResponse<T> | Promise<ApiResponse<T>>
  onResponseError?: (error: ApiErrorResponse) => ApiErrorResponse | Promise<ApiErrorResponse>
}

// ============================================================================
// API CLIENT CONFIGURATION
// ============================================================================

export interface ApiClientConfig {
  baseURL: string
  timeout?: number
  retries?: number
  headers?: Record<string, string>
  cache?: CacheConfig
  interceptors?: {
    request?: RequestInterceptor[]
    response?: ResponseInterceptor[]
  }
  auth?: {
    tokenKey?: string
    refreshTokenKey?: string
    tokenPrefix?: string
  }
}

// ============================================================================
// MAIN API INTERFACE
// ============================================================================

export interface GoRealApi {
  auth: AuthApi
  users: UserApi
  tasks: TaskApi
  leads: LeadApi
  notifications: NotificationApi
  analytics: AnalyticsApi
  files: FileUploadApi
  client: ApiClient
  ws: WebSocketClient
}

// ============================================================================
// QUERY TYPES (for React Query/SWR)
// ============================================================================

export interface QueryOptions<T = unknown> {
  enabled?: boolean
  refetchOnWindowFocus?: boolean
  refetchOnMount?: boolean
  staleTime?: number
  cacheTime?: number
  retry?: number | boolean
  retryDelay?: number
  onSuccess?: (data: T) => void
  onError?: (error: ApiErrorResponse) => void
}

export interface MutationOptions<TData = unknown, TVariables = unknown> {
  onSuccess?: (data: TData, variables: TVariables) => void
  onError?: (error: ApiErrorResponse, variables: TVariables) => void
  onMutate?: (variables: TVariables) => void
  onSettled?: (data: TData | undefined, error: ApiErrorResponse | null, variables: TVariables) => void
}
