// ============================================================================
// UI COMPONENT TYPES
// ============================================================================

import type { ReactNode, HTMLAttributes, ButtonHTMLAttributes, InputHTMLAttributes } from 'react'
import type { VariantProps } from 'class-variance-authority'

// ============================================================================
// BASE COMPONENT TYPES
// ============================================================================

export interface BaseComponentProps {
  className?: string
  children?: ReactNode
  'data-testid'?: string
}

export interface ComponentWithVariants<T> extends BaseComponentProps, VariantProps<T> {}

// ============================================================================
// BUTTON COMPONENT TYPES
// ============================================================================

export interface ButtonProps extends ButtonHTMLAttributes<HTMLButtonElement>, BaseComponentProps {
  variant?: 'default' | 'destructive' | 'outline' | 'secondary' | 'ghost' | 'link' | 'gradient' | 'glass' | 'success' | 'warning' | 'accent' | 'royal'
  size?: 'default' | 'sm' | 'lg' | 'xl' | 'icon' | 'icon-sm' | 'icon-lg'
  asChild?: boolean
  loading?: boolean
  leftIcon?: ReactNode
  rightIcon?: ReactNode
}

// ============================================================================
// INPUT COMPONENT TYPES
// ============================================================================

export interface InputProps extends InputHTMLAttributes<HTMLInputElement>, BaseComponentProps {
  label?: string
  error?: string
  helperText?: string
  leftIcon?: ReactNode
  rightIcon?: ReactNode
  variant?: 'default' | 'filled' | 'outlined'
}

export interface TextareaProps extends BaseComponentProps {
  label?: string
  error?: string
  helperText?: string
  rows?: number
  resize?: 'none' | 'vertical' | 'horizontal' | 'both'
}

// ============================================================================
// FORM COMPONENT TYPES
// ============================================================================

export interface FormFieldProps extends BaseComponentProps {
  name: string
  label?: string
  error?: string
  required?: boolean
  helperText?: string
}

export interface SelectProps extends FormFieldProps {
  options: Array<{
    label: string
    value: string | number
    disabled?: boolean
    icon?: ReactNode
  }>
  placeholder?: string
  multiple?: boolean
  searchable?: boolean
  clearable?: boolean
  loading?: boolean
  onSearch?: (query: string) => void
}

export interface CheckboxProps extends BaseComponentProps {
  checked?: boolean
  indeterminate?: boolean
  label?: string
  error?: string
  disabled?: boolean
  onChange?: (checked: boolean) => void
}

export interface RadioGroupProps extends FormFieldProps {
  options: Array<{
    label: string
    value: string
    disabled?: boolean
    description?: string
  }>
  value?: string
  onChange?: (value: string) => void
  orientation?: 'horizontal' | 'vertical'
}

// ============================================================================
// LAYOUT COMPONENT TYPES
// ============================================================================

export interface ContainerProps extends BaseComponentProps {
  maxWidth?: 'sm' | 'md' | 'lg' | 'xl' | '2xl' | 'full'
  padding?: boolean
  centered?: boolean
}

export interface GridProps extends BaseComponentProps {
  cols?: 1 | 2 | 3 | 4 | 5 | 6 | 12
  gap?: 'none' | 'sm' | 'md' | 'lg' | 'xl'
  responsive?: {
    sm?: number
    md?: number
    lg?: number
    xl?: number
  }
}

export interface FlexProps extends BaseComponentProps {
  direction?: 'row' | 'column' | 'row-reverse' | 'column-reverse'
  align?: 'start' | 'center' | 'end' | 'stretch' | 'baseline'
  justify?: 'start' | 'center' | 'end' | 'between' | 'around' | 'evenly'
  wrap?: 'nowrap' | 'wrap' | 'wrap-reverse'
  gap?: 'none' | 'sm' | 'md' | 'lg' | 'xl'
}

// ============================================================================
// NAVIGATION COMPONENT TYPES
// ============================================================================

export interface NavItem {
  label: string
  href?: string
  icon?: ReactNode
  badge?: string | number
  children?: NavItem[]
  disabled?: boolean
  external?: boolean
}

export interface SidebarProps extends BaseComponentProps {
  isOpen: boolean
  setIsOpen: (open: boolean) => void
  userRole?: string
  items?: NavItem[]
  collapsible?: boolean
  width?: 'sm' | 'md' | 'lg'
}

export interface NavbarProps extends BaseComponentProps {
  title?: string
  sidebarOpen?: boolean
  setSidebarOpen?: (open: boolean) => void
  actions?: ReactNode
  breadcrumbs?: Array<{
    label: string
    href?: string
  }>
}

export interface BreadcrumbProps extends BaseComponentProps {
  items: Array<{
    label: string
    href?: string
    current?: boolean
  }>
  separator?: ReactNode
}

// ============================================================================
// DATA DISPLAY COMPONENT TYPES
// ============================================================================

export interface TableColumn<T = unknown> {
  key: keyof T | string
  label: string
  sortable?: boolean
  width?: string | number
  minWidth?: string | number
  align?: 'left' | 'center' | 'right'
  sticky?: boolean
  render?: (value: unknown, row: T, index: number) => ReactNode
  headerRender?: () => ReactNode
}

export interface TableProps<T = unknown> extends BaseComponentProps {
  data: T[]
  columns: TableColumn<T>[]
  loading?: boolean
  empty?: ReactNode
  keyExtractor?: (row: T, index: number) => string
  onRowClick?: (row: T, index: number) => void
  selectable?: boolean
  selectedRows?: T[]
  onSelectionChange?: (rows: T[]) => void
  pagination?: {
    page: number
    limit: number
    total: number
    onPageChange: (page: number) => void
    onLimitChange: (limit: number) => void
  }
  sorting?: {
    key: string
    direction: 'asc' | 'desc'
    onSort: (key: string, direction: 'asc' | 'desc') => void
  }
  filters?: ReactNode
  actions?: ReactNode
}

export interface CardProps extends BaseComponentProps {
  title?: string
  subtitle?: string
  image?: string
  actions?: ReactNode
  footer?: ReactNode
  variant?: 'default' | 'outlined' | 'elevated' | 'glass'
  padding?: 'none' | 'sm' | 'md' | 'lg'
  hoverable?: boolean
  clickable?: boolean
  onClick?: () => void
}

export interface StatCardProps extends BaseComponentProps {
  title: string
  value: string | number
  change?: {
    value: number
    type: 'increase' | 'decrease'
    period?: string
  }
  icon?: ReactNode
  color?: 'primary' | 'secondary' | 'success' | 'warning' | 'error'
  loading?: boolean
}

// ============================================================================
// FEEDBACK COMPONENT TYPES
// ============================================================================

export interface AlertProps extends BaseComponentProps {
  variant?: 'info' | 'success' | 'warning' | 'error'
  title?: string
  description?: string
  icon?: ReactNode
  closable?: boolean
  onClose?: () => void
  actions?: ReactNode
}

export interface ToastProps extends BaseComponentProps {
  id: string
  title?: string
  description?: string
  variant?: 'info' | 'success' | 'warning' | 'error'
  duration?: number
  action?: {
    label: string
    onClick: () => void
  }
  onClose?: () => void
}

export interface ModalProps extends BaseComponentProps {
  isOpen: boolean
  onClose: () => void
  title?: string
  description?: string
  size?: 'sm' | 'md' | 'lg' | 'xl' | 'full'
  closeOnOverlayClick?: boolean
  closeOnEscape?: boolean
  showCloseButton?: boolean
  footer?: ReactNode
}

export interface DrawerProps extends BaseComponentProps {
  isOpen: boolean
  onClose: () => void
  title?: string
  position?: 'left' | 'right' | 'top' | 'bottom'
  size?: 'sm' | 'md' | 'lg' | 'xl'
  overlay?: boolean
  closeOnOverlayClick?: boolean
  closeOnEscape?: boolean
}

// ============================================================================
// LOADING COMPONENT TYPES
// ============================================================================

export interface SpinnerProps extends BaseComponentProps {
  size?: 'sm' | 'md' | 'lg' | 'xl'
  color?: 'primary' | 'secondary' | 'current'
  label?: string
}

export interface SkeletonProps extends BaseComponentProps {
  width?: string | number
  height?: string | number
  variant?: 'text' | 'rectangular' | 'circular'
  animation?: 'pulse' | 'wave' | 'none'
}

export interface ProgressProps extends BaseComponentProps {
  value: number
  max?: number
  size?: 'sm' | 'md' | 'lg'
  variant?: 'default' | 'success' | 'warning' | 'error'
  showLabel?: boolean
  label?: string
  animated?: boolean
}

// ============================================================================
// CHART COMPONENT TYPES
// ============================================================================

export interface ChartDataPoint {
  label: string
  value: number
  color?: string
}

export interface LineChartProps extends BaseComponentProps {
  data: Array<{
    name: string
    data: Array<{ x: string | number; y: number }>
  }>
  xAxis?: {
    label?: string
    type?: 'category' | 'datetime' | 'numeric'
  }
  yAxis?: {
    label?: string
    min?: number
    max?: number
  }
  colors?: string[]
  height?: number
  loading?: boolean
  empty?: ReactNode
}

export interface BarChartProps extends BaseComponentProps {
  data: ChartDataPoint[]
  orientation?: 'horizontal' | 'vertical'
  colors?: string[]
  height?: number
  loading?: boolean
  empty?: ReactNode
}

export interface PieChartProps extends BaseComponentProps {
  data: ChartDataPoint[]
  showLabels?: boolean
  showLegend?: boolean
  colors?: string[]
  height?: number
  loading?: boolean
  empty?: ReactNode
}

// ============================================================================
// UTILITY COMPONENT TYPES
// ============================================================================

export interface AvatarProps extends BaseComponentProps {
  src?: string
  alt?: string
  name?: string
  size?: 'xs' | 'sm' | 'md' | 'lg' | 'xl' | '2xl'
  variant?: 'circular' | 'rounded' | 'square'
  fallback?: ReactNode
  online?: boolean
  onClick?: () => void
}

export interface BadgeProps extends BaseComponentProps {
  variant?: 'default' | 'secondary' | 'success' | 'warning' | 'error' | 'info'
  size?: 'sm' | 'md' | 'lg'
  dot?: boolean
  pulse?: boolean
}

export interface TooltipProps extends BaseComponentProps {
  content: ReactNode
  placement?: 'top' | 'bottom' | 'left' | 'right'
  trigger?: 'hover' | 'click' | 'focus'
  delay?: number
  arrow?: boolean
}

export interface DropdownProps extends BaseComponentProps {
  trigger: ReactNode
  items: Array<{
    label: string
    value?: string
    icon?: ReactNode
    disabled?: boolean
    separator?: boolean
    onClick?: () => void
  }>
  placement?: 'bottom-start' | 'bottom-end' | 'top-start' | 'top-end'
  closeOnClick?: boolean
}

// ============================================================================
// ACCESSIBILITY TYPES
// ============================================================================

export interface AriaProps {
  'aria-label'?: string
  'aria-labelledby'?: string
  'aria-describedby'?: string
  'aria-expanded'?: boolean
  'aria-selected'?: boolean
  'aria-checked'?: boolean
  'aria-disabled'?: boolean
  'aria-hidden'?: boolean
  'aria-live'?: 'off' | 'polite' | 'assertive'
  role?: string
}

export interface FocusableProps {
  autoFocus?: boolean
  tabIndex?: number
  onFocus?: (event: React.FocusEvent) => void
  onBlur?: (event: React.FocusEvent) => void
}

// ============================================================================
// THEME COMPONENT TYPES
// ============================================================================

export interface ThemeProviderProps extends BaseComponentProps {
  theme?: 'light' | 'dark' | 'system'
  defaultTheme?: 'light' | 'dark'
  storageKey?: string
}

export interface ColorModeProps {
  colorMode: 'light' | 'dark'
  toggleColorMode: () => void
  setColorMode: (mode: 'light' | 'dark') => void
}
