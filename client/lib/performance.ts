// ============================================================================
// FRONTEND PERFORMANCE OPTIMIZATION
// ============================================================================

import { useCallback, useEffect, useMemo, useRef, useState } from 'react'
import { debounce, throttle } from 'lodash-es'

// ============================================================================
// PERFORMANCE MONITORING
// ============================================================================

interface PerformanceMetrics {
  renderTime: number
  componentCount: number
  memoryUsage: number
  networkRequests: number
  cacheHitRate: number
}

interface PerformanceEntry {
  name: string
  startTime: number
  duration: number
  type: string
}

class PerformanceMonitor {
  private metrics: PerformanceMetrics = {
    renderTime: 0,
    componentCount: 0,
    memoryUsage: 0,
    networkRequests: 0,
    cacheHitRate: 0,
  }

  private entries: PerformanceEntry[] = []
  private observer: PerformanceObserver | null = null

  constructor() {
    this.initializeObserver()
    this.startMemoryMonitoring()
  }

  private initializeObserver() {
    if (typeof window !== 'undefined' && 'PerformanceObserver' in window) {
      this.observer = new PerformanceObserver((list) => {
        const entries = list.getEntries()
        entries.forEach((entry) => {
          this.entries.push({
            name: entry.name,
            startTime: entry.startTime,
            duration: entry.duration,
            type: entry.entryType,
          })
        })
      })

      this.observer.observe({ 
        entryTypes: ['measure', 'navigation', 'resource', 'paint'] 
      })
    }
  }

  private startMemoryMonitoring() {
    if (typeof window !== 'undefined' && 'performance' in window && 'memory' in performance) {
      setInterval(() => {
        const memory = (performance as any).memory
        this.metrics.memoryUsage = memory.usedJSHeapSize
      }, 5000)
    }
  }

  public measureRender(componentName: string, renderFn: () => void) {
    const startTime = performance.now()
    renderFn()
    const endTime = performance.now()
    
    this.metrics.renderTime = endTime - startTime
    this.metrics.componentCount++

    if (typeof window !== 'undefined' && 'performance' in window) {
      performance.mark(`${componentName}-render-start`)
      performance.mark(`${componentName}-render-end`)
      performance.measure(
        `${componentName}-render`,
        `${componentName}-render-start`,
        `${componentName}-render-end`
      )
    }
  }

  public trackNetworkRequest(url: string, method: string) {
    this.metrics.networkRequests++
    
    if (typeof window !== 'undefined' && 'performance' in window) {
      performance.mark(`network-${method}-${url}-start`)
    }
  }

  public getMetrics(): PerformanceMetrics {
    return { ...this.metrics }
  }

  public getEntries(): PerformanceEntry[] {
    return [...this.entries]
  }

  public clearEntries() {
    this.entries = []
    if (typeof window !== 'undefined' && 'performance' in window) {
      performance.clearMarks()
      performance.clearMeasures()
    }
  }
}

export const performanceMonitor = new PerformanceMonitor()

// ============================================================================
// REACT PERFORMANCE HOOKS
// ============================================================================

// Optimized debounced value hook
export function useDebouncedValue<T>(value: T, delay: number): T {
  const [debouncedValue, setDebouncedValue] = useState<T>(value)

  useEffect(() => {
    const handler = setTimeout(() => {
      setDebouncedValue(value)
    }, delay)

    return () => {
      clearTimeout(handler)
    }
  }, [value, delay])

  return debouncedValue
}

// Optimized throttled callback hook
export function useThrottledCallback<T extends (...args: any[]) => any>(
  callback: T,
  delay: number
): T {
  const throttledCallback = useMemo(
    () => throttle(callback, delay, { leading: true, trailing: true }),
    [callback, delay]
  )

  useEffect(() => {
    return () => {
      throttledCallback.cancel()
    }
  }, [throttledCallback])

  return throttledCallback as T
}

// Optimized debounced callback hook
export function useDebouncedCallback<T extends (...args: any[]) => any>(
  callback: T,
  delay: number
): T {
  const debouncedCallback = useMemo(
    () => debounce(callback, delay),
    [callback, delay]
  )

  useEffect(() => {
    return () => {
      debouncedCallback.cancel()
    }
  }, [debouncedCallback])

  return debouncedCallback as T
}

// Intersection Observer hook for lazy loading
export function useIntersectionObserver(
  elementRef: React.RefObject<Element>,
  options: IntersectionObserverInit = {}
) {
  const [isIntersecting, setIsIntersecting] = useState(false)
  const [hasIntersected, setHasIntersected] = useState(false)

  useEffect(() => {
    const element = elementRef.current
    if (!element) return

    const observer = new IntersectionObserver(
      ([entry]) => {
        setIsIntersecting(entry.isIntersecting)
        if (entry.isIntersecting && !hasIntersected) {
          setHasIntersected(true)
        }
      },
      {
        threshold: 0.1,
        rootMargin: '50px',
        ...options,
      }
    )

    observer.observe(element)

    return () => {
      observer.unobserve(element)
    }
  }, [elementRef, options, hasIntersected])

  return { isIntersecting, hasIntersected }
}

// Virtual scrolling hook
export function useVirtualScrolling<T>(
  items: T[],
  itemHeight: number,
  containerHeight: number
) {
  const [scrollTop, setScrollTop] = useState(0)

  const visibleItems = useMemo(() => {
    const startIndex = Math.floor(scrollTop / itemHeight)
    const endIndex = Math.min(
      startIndex + Math.ceil(containerHeight / itemHeight) + 1,
      items.length
    )

    return {
      startIndex,
      endIndex,
      items: items.slice(startIndex, endIndex),
      totalHeight: items.length * itemHeight,
      offsetY: startIndex * itemHeight,
    }
  }, [items, itemHeight, containerHeight, scrollTop])

  const handleScroll = useCallback((event: React.UIEvent<HTMLDivElement>) => {
    setScrollTop(event.currentTarget.scrollTop)
  }, [])

  return {
    visibleItems,
    handleScroll,
  }
}

// ============================================================================
// CACHING UTILITIES
// ============================================================================

interface CacheEntry<T> {
  data: T
  timestamp: number
  ttl: number
}

class MemoryCache<T> {
  private cache = new Map<string, CacheEntry<T>>()
  private maxSize: number
  private defaultTTL: number

  constructor(maxSize = 100, defaultTTL = 5 * 60 * 1000) {
    this.maxSize = maxSize
    this.defaultTTL = defaultTTL
  }

  set(key: string, data: T, ttl = this.defaultTTL): void {
    // Remove expired entries if cache is full
    if (this.cache.size >= this.maxSize) {
      this.cleanup()
      
      // If still full, remove oldest entry
      if (this.cache.size >= this.maxSize) {
        const firstKey = this.cache.keys().next().value
        this.cache.delete(firstKey)
      }
    }

    this.cache.set(key, {
      data,
      timestamp: Date.now(),
      ttl,
    })
  }

  get(key: string): T | null {
    const entry = this.cache.get(key)
    if (!entry) return null

    // Check if entry has expired
    if (Date.now() - entry.timestamp > entry.ttl) {
      this.cache.delete(key)
      return null
    }

    return entry.data
  }

  has(key: string): boolean {
    return this.get(key) !== null
  }

  delete(key: string): boolean {
    return this.cache.delete(key)
  }

  clear(): void {
    this.cache.clear()
  }

  private cleanup(): void {
    const now = Date.now()
    for (const [key, entry] of this.cache.entries()) {
      if (now - entry.timestamp > entry.ttl) {
        this.cache.delete(key)
      }
    }
  }

  getStats() {
    return {
      size: this.cache.size,
      maxSize: this.maxSize,
      hitRate: 0, // Would need to track hits/misses
    }
  }
}

export const memoryCache = new MemoryCache()

// React Query-like cache hook
export function useCachedData<T>(
  key: string,
  fetcher: () => Promise<T>,
  options: { ttl?: number; enabled?: boolean } = {}
) {
  const { ttl = 5 * 60 * 1000, enabled = true } = options
  const [data, setData] = useState<T | null>(() => memoryCache.get(key))
  const [isLoading, setIsLoading] = useState(false)
  const [error, setError] = useState<Error | null>(null)

  const fetchData = useCallback(async () => {
    if (!enabled) return

    // Check cache first
    const cachedData = memoryCache.get(key)
    if (cachedData) {
      setData(cachedData)
      return
    }

    setIsLoading(true)
    setError(null)

    try {
      const result = await fetcher()
      memoryCache.set(key, result, ttl)
      setData(result)
    } catch (err) {
      setError(err as Error)
    } finally {
      setIsLoading(false)
    }
  }, [key, fetcher, ttl, enabled])

  useEffect(() => {
    fetchData()
  }, [fetchData])

  const refetch = useCallback(() => {
    memoryCache.delete(key)
    fetchData()
  }, [key, fetchData])

  return { data, isLoading, error, refetch }
}

// ============================================================================
// IMAGE OPTIMIZATION
// ============================================================================

interface ImageOptimizationOptions {
  quality?: number
  format?: 'webp' | 'avif' | 'jpeg' | 'png'
  sizes?: string
  priority?: boolean
  placeholder?: 'blur' | 'empty'
  blurDataURL?: string
}

export function useOptimizedImage(
  src: string,
  options: ImageOptimizationOptions = {}
) {
  const {
    quality = 75,
    format = 'webp',
    sizes = '100vw',
    priority = false,
    placeholder = 'empty',
  } = options

  const [isLoaded, setIsLoaded] = useState(false)
  const [error, setError] = useState<string | null>(null)

  const optimizedSrc = useMemo(() => {
    if (!src) return ''
    
    // Add optimization parameters
    const url = new URL(src, window.location.origin)
    url.searchParams.set('q', quality.toString())
    url.searchParams.set('f', format)
    
    return url.toString()
  }, [src, quality, format])

  const handleLoad = useCallback(() => {
    setIsLoaded(true)
    setError(null)
  }, [])

  const handleError = useCallback(() => {
    setError('Failed to load image')
    setIsLoaded(false)
  }, [])

  return {
    src: optimizedSrc,
    isLoaded,
    error,
    onLoad: handleLoad,
    onError: handleError,
    sizes,
    priority,
    placeholder,
  }
}

// ============================================================================
// BUNDLE OPTIMIZATION
// ============================================================================

// Dynamic import with error handling
export async function dynamicImport<T>(
  importFn: () => Promise<{ default: T }>
): Promise<T> {
  try {
    const module = await importFn()
    return module.default
  } catch (error) {
    console.error('Dynamic import failed:', error)
    throw error
  }
}

// Preload critical resources
export function preloadResource(href: string, as: string, type?: string) {
  if (typeof document === 'undefined') return

  const link = document.createElement('link')
  link.rel = 'preload'
  link.href = href
  link.as = as
  if (type) link.type = type

  document.head.appendChild(link)
}

// Prefetch resources for future navigation
export function prefetchResource(href: string) {
  if (typeof document === 'undefined') return

  const link = document.createElement('link')
  link.rel = 'prefetch'
  link.href = href

  document.head.appendChild(link)
}

// ============================================================================
// PERFORMANCE UTILITIES
// ============================================================================

// Measure component render time
export function withPerformanceTracking<P extends object>(
  Component: React.ComponentType<P>,
  componentName: string
) {
  return function PerformanceTrackedComponent(props: P) {
    useEffect(() => {
      performanceMonitor.measureRender(componentName, () => {
        // Component render measurement
      })
    })

    return <Component {...props} />
  }
}

// Batch DOM updates
export function batchDOMUpdates(callback: () => void) {
  if (typeof window !== 'undefined' && 'requestIdleCallback' in window) {
    requestIdleCallback(callback)
  } else {
    setTimeout(callback, 0)
  }
}

// Optimize heavy computations
export function useComputedValue<T>(
  computeFn: () => T,
  deps: React.DependencyList,
  options: { timeout?: number } = {}
) {
  const { timeout = 16 } = options // 16ms = 1 frame at 60fps
  const [value, setValue] = useState<T | null>(null)
  const [isComputing, setIsComputing] = useState(false)

  useEffect(() => {
    setIsComputing(true)
    
    const startTime = performance.now()
    const compute = () => {
      try {
        const result = computeFn()
        setValue(result)
      } catch (error) {
        console.error('Computation failed:', error)
      } finally {
        setIsComputing(false)
      }
    }

    // If computation might be heavy, defer it
    if (performance.now() - startTime > timeout) {
      setTimeout(compute, 0)
    } else {
      compute()
    }
  }, deps)

  return { value, isComputing }
}

// Web Workers for heavy computations
export function useWebWorker<T, R>(
  workerScript: string,
  data: T,
  options: { enabled?: boolean } = {}
) {
  const { enabled = true } = options
  const [result, setResult] = useState<R | null>(null)
  const [isLoading, setIsLoading] = useState(false)
  const [error, setError] = useState<Error | null>(null)
  const workerRef = useRef<Worker | null>(null)

  useEffect(() => {
    if (!enabled || typeof Worker === 'undefined') return

    setIsLoading(true)
    setError(null)

    const worker = new Worker(workerScript)
    workerRef.current = worker

    worker.onmessage = (event) => {
      setResult(event.data)
      setIsLoading(false)
    }

    worker.onerror = (event) => {
      setError(new Error(event.message))
      setIsLoading(false)
    }

    worker.postMessage(data)

    return () => {
      worker.terminate()
      workerRef.current = null
    }
  }, [workerScript, data, enabled])

  return { result, isLoading, error }
}

// ============================================================================
// EXPORT PERFORMANCE UTILITIES
// ============================================================================

export {
  PerformanceMonitor,
  MemoryCache,
  type PerformanceMetrics,
  type PerformanceEntry,
}
