// ============================================================================
// ACCESSIBILITY UTILITIES
// ============================================================================

import { useCallback, useEffect, useRef } from 'react'

// ============================================================================
// ARIA UTILITIES
// ============================================================================

/**
 * Generates a unique ID for accessibility purposes
 */
export function generateId(prefix = 'id'): string {
  return `${prefix}-${Math.random().toString(36).substr(2, 9)}`
}

/**
 * Creates ARIA attributes for form fields
 */
export function createAriaAttributes(options: {
  id?: string
  label?: string
  description?: string
  error?: string
  required?: boolean
  invalid?: boolean
}) {
  const { id, label, description, error, required, invalid } = options
  
  const attributes: Record<string, string | boolean | undefined> = {}
  
  if (id) {
    attributes.id = id
  }
  
  if (label) {
    attributes['aria-label'] = label
  }
  
  if (description) {
    attributes['aria-describedby'] = `${id}-description`
  }
  
  if (error) {
    attributes['aria-describedby'] = `${id}-error`
    attributes['aria-invalid'] = true
  }
  
  if (required) {
    attributes['aria-required'] = true
  }
  
  if (invalid !== undefined) {
    attributes['aria-invalid'] = invalid
  }
  
  return attributes
}

/**
 * Creates ARIA attributes for expandable elements
 */
export function createExpandableAttributes(isExpanded: boolean, controlsId?: string) {
  return {
    'aria-expanded': isExpanded,
    ...(controlsId && { 'aria-controls': controlsId }),
  }
}

/**
 * Creates ARIA attributes for selected elements
 */
export function createSelectableAttributes(isSelected: boolean, setSize?: number, posInSet?: number) {
  return {
    'aria-selected': isSelected,
    ...(setSize && { 'aria-setsize': setSize }),
    ...(posInSet && { 'aria-posinset': posInSet }),
  }
}

// ============================================================================
// FOCUS MANAGEMENT
// ============================================================================

/**
 * Hook for managing focus trap within a container
 */
export function useFocusTrap(isActive: boolean) {
  const containerRef = useRef<HTMLElement>(null)
  
  useEffect(() => {
    if (!isActive || !containerRef.current) return
    
    const container = containerRef.current
    const focusableElements = container.querySelectorAll(
      'button, [href], input, select, textarea, [tabindex]:not([tabindex="-1"])'
    )
    
    const firstElement = focusableElements[0] as HTMLElement
    const lastElement = focusableElements[focusableElements.length - 1] as HTMLElement
    
    const handleTabKey = (e: KeyboardEvent) => {
      if (e.key !== 'Tab') return
      
      if (e.shiftKey) {
        if (document.activeElement === firstElement) {
          e.preventDefault()
          lastElement?.focus()
        }
      } else {
        if (document.activeElement === lastElement) {
          e.preventDefault()
          firstElement?.focus()
        }
      }
    }
    
    container.addEventListener('keydown', handleTabKey)
    firstElement?.focus()
    
    return () => {
      container.removeEventListener('keydown', handleTabKey)
    }
  }, [isActive])
  
  return containerRef
}

/**
 * Hook for managing focus restoration
 */
export function useFocusRestore() {
  const previousActiveElement = useRef<HTMLElement | null>(null)
  
  const saveFocus = useCallback(() => {
    previousActiveElement.current = document.activeElement as HTMLElement
  }, [])
  
  const restoreFocus = useCallback(() => {
    if (previousActiveElement.current) {
      previousActiveElement.current.focus()
      previousActiveElement.current = null
    }
  }, [])
  
  return { saveFocus, restoreFocus }
}

/**
 * Hook for managing focus on mount
 */
export function useFocusOnMount(shouldFocus = true) {
  const elementRef = useRef<HTMLElement>(null)
  
  useEffect(() => {
    if (shouldFocus && elementRef.current) {
      elementRef.current.focus()
    }
  }, [shouldFocus])
  
  return elementRef
}

// ============================================================================
// KEYBOARD NAVIGATION
// ============================================================================

/**
 * Hook for handling arrow key navigation in lists
 */
export function useArrowNavigation<T extends HTMLElement>(
  items: T[],
  options: {
    loop?: boolean
    orientation?: 'horizontal' | 'vertical'
    onSelect?: (item: T, index: number) => void
  } = {}
) {
  const { loop = true, orientation = 'vertical', onSelect } = options
  const currentIndex = useRef(0)
  
  const handleKeyDown = useCallback((e: KeyboardEvent) => {
    const isVertical = orientation === 'vertical'
    const nextKey = isVertical ? 'ArrowDown' : 'ArrowRight'
    const prevKey = isVertical ? 'ArrowUp' : 'ArrowLeft'
    
    if (e.key === nextKey) {
      e.preventDefault()
      currentIndex.current = loop 
        ? (currentIndex.current + 1) % items.length
        : Math.min(currentIndex.current + 1, items.length - 1)
      items[currentIndex.current]?.focus()
    } else if (e.key === prevKey) {
      e.preventDefault()
      currentIndex.current = loop
        ? (currentIndex.current - 1 + items.length) % items.length
        : Math.max(currentIndex.current - 1, 0)
      items[currentIndex.current]?.focus()
    } else if (e.key === 'Home') {
      e.preventDefault()
      currentIndex.current = 0
      items[0]?.focus()
    } else if (e.key === 'End') {
      e.preventDefault()
      currentIndex.current = items.length - 1
      items[items.length - 1]?.focus()
    } else if (e.key === 'Enter' || e.key === ' ') {
      e.preventDefault()
      onSelect?.(items[currentIndex.current], currentIndex.current)
    }
  }, [items, loop, orientation, onSelect])
  
  return { handleKeyDown, currentIndex: currentIndex.current }
}

// ============================================================================
// SCREEN READER UTILITIES
// ============================================================================

/**
 * Announces a message to screen readers
 */
export function announceToScreenReader(message: string, priority: 'polite' | 'assertive' = 'polite') {
  const announcement = document.createElement('div')
  announcement.setAttribute('aria-live', priority)
  announcement.setAttribute('aria-atomic', 'true')
  announcement.className = 'sr-only'
  announcement.textContent = message
  
  document.body.appendChild(announcement)
  
  setTimeout(() => {
    document.body.removeChild(announcement)
  }, 1000)
}

/**
 * Hook for managing live region announcements
 */
export function useLiveRegion() {
  const liveRegionRef = useRef<HTMLDivElement>(null)
  
  const announce = useCallback((message: string, priority: 'polite' | 'assertive' = 'polite') => {
    if (liveRegionRef.current) {
      liveRegionRef.current.setAttribute('aria-live', priority)
      liveRegionRef.current.textContent = message
      
      // Clear after announcement
      setTimeout(() => {
        if (liveRegionRef.current) {
          liveRegionRef.current.textContent = ''
        }
      }, 1000)
    }
  }, [])
  
  const LiveRegion = useCallback(() => (
    <div
      ref={liveRegionRef}
      aria-live="polite"
      aria-atomic="true"
      className="sr-only"
    />
  ), [])
  
  return { announce, LiveRegion }
}

// ============================================================================
// REDUCED MOTION UTILITIES
// ============================================================================

/**
 * Hook for detecting user's motion preferences
 */
export function useReducedMotion() {
  const prefersReducedMotion = typeof window !== 'undefined' 
    ? window.matchMedia('(prefers-reduced-motion: reduce)').matches
    : false
  
  return prefersReducedMotion
}

/**
 * Conditionally applies animation classes based on motion preferences
 */
export function getAnimationClasses(
  animationClasses: string,
  reducedMotionClasses = ''
): string {
  const prefersReducedMotion = typeof window !== 'undefined'
    ? window.matchMedia('(prefers-reduced-motion: reduce)').matches
    : false
  
  return prefersReducedMotion ? reducedMotionClasses : animationClasses
}

// ============================================================================
// COLOR CONTRAST UTILITIES
// ============================================================================

/**
 * Calculates the contrast ratio between two colors
 */
export function getContrastRatio(color1: string, color2: string): number {
  const getLuminance = (color: string): number => {
    // This is a simplified version - in production, use a proper color library
    const rgb = color.match(/\d+/g)?.map(Number) || [0, 0, 0]
    const [r, g, b] = rgb.map(c => {
      c = c / 255
      return c <= 0.03928 ? c / 12.92 : Math.pow((c + 0.055) / 1.055, 2.4)
    })
    return 0.2126 * r + 0.7152 * g + 0.0722 * b
  }
  
  const lum1 = getLuminance(color1)
  const lum2 = getLuminance(color2)
  const brightest = Math.max(lum1, lum2)
  const darkest = Math.min(lum1, lum2)
  
  return (brightest + 0.05) / (darkest + 0.05)
}

/**
 * Checks if color combination meets WCAG contrast requirements
 */
export function meetsContrastRequirement(
  color1: string, 
  color2: string, 
  level: 'AA' | 'AAA' = 'AA',
  size: 'normal' | 'large' = 'normal'
): boolean {
  const ratio = getContrastRatio(color1, color2)
  
  if (level === 'AAA') {
    return size === 'large' ? ratio >= 4.5 : ratio >= 7
  }
  
  return size === 'large' ? ratio >= 3 : ratio >= 4.5
}

// ============================================================================
// SKIP LINKS UTILITY
// ============================================================================

/**
 * Creates a skip link for keyboard navigation
 */
export function createSkipLink(targetId: string, text = 'Skip to main content') {
  return {
    href: `#${targetId}`,
    className: 'sr-only focus:not-sr-only focus:absolute focus:top-4 focus:left-4 focus:z-50 focus:px-4 focus:py-2 focus:bg-primary focus:text-primary-foreground focus:rounded-md',
    children: text,
  }
}

// ============================================================================
// VALIDATION UTILITIES
// ============================================================================

/**
 * Validates if an element has proper accessibility attributes
 */
export function validateAccessibility(element: HTMLElement): string[] {
  const issues: string[] = []
  
  // Check for missing alt text on images
  if (element.tagName === 'IMG' && !element.getAttribute('alt')) {
    issues.push('Image missing alt attribute')
  }
  
  // Check for missing labels on form controls
  if (['INPUT', 'SELECT', 'TEXTAREA'].includes(element.tagName)) {
    const hasLabel = element.getAttribute('aria-label') || 
                    element.getAttribute('aria-labelledby') ||
                    document.querySelector(`label[for="${element.id}"]`)
    
    if (!hasLabel) {
      issues.push('Form control missing label')
    }
  }
  
  // Check for missing focus indicators
  const computedStyle = window.getComputedStyle(element, ':focus')
  if (computedStyle.outline === 'none' && !computedStyle.boxShadow) {
    issues.push('Element missing focus indicator')
  }
  
  return issues
}
