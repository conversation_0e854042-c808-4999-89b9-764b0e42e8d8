# Supabase Configuration
NEXT_PUBLIC_SUPABASE_URL=your_supabase_url
NEXT_PUBLIC_SUPABASE_ANON_KEY=your_supabase_anon_key
SUPABASE_SERVICE_ROLE_KEY=your_supabase_service_role_key

# Ethereum Configuration
NEXT_PUBLIC_ETHEREUM_NETWORK=sepolia
NEXT_PUBLIC_CONTRACT_ADDRESS=your_contract_address
NEXT_PUBLIC_INFURA_PROJECT_ID=your_infura_project_id

# Go Backend API
NEXT_PUBLIC_GO_API_URL=http://localhost:8080

# Cloudflare Workers Configuration
NEXT_PUBLIC_WORKERS_URL=http://localhost:8787
NEXT_PUBLIC_FILES_URL=https://files.goreal.com
NEXT_PUBLIC_WEBSOCKET_URL=wss://goreal-workers.your-subdomain.workers.dev

# Production Cloudflare URLs (update with your actual domains)
# NEXT_PUBLIC_WORKERS_URL=https://goreal-workers.your-subdomain.workers.dev
# NEXT_PUBLIC_FILES_URL=https://files.goreal.com
# NEXT_PUBLIC_WEBSOCKET_URL=wss://goreal-workers.your-subdomain.workers.dev

# Application Configuration
NEXT_PUBLIC_APP_URL=http://localhost:3000
NEXTAUTH_SECRET=your_nextauth_secret
NEXTAUTH_URL=http://localhost:3000

# IPFS Configuration (fallback)
NEXT_PUBLIC_IPFS_GATEWAY=https://gateway.pinata.cloud/ipfs/
PINATA_API_KEY=your_pinata_api_key
PINATA_SECRET_API_KEY=your_pinata_secret_api_key

# Cloudflare Pages Configuration
CLOUDFLARE_ACCOUNT_ID=your_cloudflare_account_id
CLOUDFLARE_API_TOKEN=your_cloudflare_api_token

# Analytics (Optional)
NEXT_PUBLIC_GOOGLE_ANALYTICS_ID=your_ga_id
