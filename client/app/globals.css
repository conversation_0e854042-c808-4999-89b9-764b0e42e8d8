@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  input[type="number"]::-webkit-inner-spin-button,
  input[type="number"]::-webkit-outer-spin-button {
    -webkit-appearance: none;
    margin: 0;
  }

  * {
    box-sizing: border-box;
    padding: 0;
    margin: 0;
  }

  html,
  body {
    max-width: 100vw;
    overflow-x: hidden;
    scroll-behavior: smooth;
  }

  body {
    color: hsl(var(--foreground));
    background: hsl(var(--background));
    font-feature-settings: "rlig" 1, "calt" 1;
    font-synthesis: none;
    text-rendering: optimizeLegibility;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
  }

  a {
    color: inherit;
    text-decoration: none;
  }

  /* Enhanced focus styles */
  *:focus-visible {
    outline: 2px solid hsl(var(--ring));
    outline-offset: 2px;
  }

  /* Selection styles */
  ::selection {
    background: hsl(var(--primary) / 0.2);
    color: hsl(var(--primary-foreground));
  }
}

@layer components {
  .fullHeight {
    height: calc(100vh - 4rem);
  }

  @media (max-width: 620px) {
    .fullHeight {
      height: calc(100vh - 7rem);
    }
  }

  /* Epic UI Components */
  .glass-card {
    background: rgba(var(--glass-bg));
    backdrop-filter: blur(20px);
    border: 1px solid rgba(var(--glass-border));
    box-shadow: var(--shadow-lg);
  }

  .gradient-primary {
    background: linear-gradient(135deg, hsl(var(--primary)), hsl(var(--primary-600)));
  }

  .gradient-secondary {
    background: linear-gradient(135deg, hsl(var(--secondary)), hsl(var(--secondary-600)));
  }

  .gradient-accent {
    background: linear-gradient(135deg, hsl(var(--accent)), hsl(var(--accent-600)));
  }

  .gradient-mesh {
    background:
      radial-gradient(circle at 20% 80%, hsl(var(--primary) / 0.15) 0%, transparent 50%),
      radial-gradient(circle at 80% 20%, hsl(var(--accent) / 0.15) 0%, transparent 50%),
      radial-gradient(circle at 40% 40%, hsl(var(--secondary) / 0.1) 0%, transparent 50%),
      radial-gradient(circle at 60% 60%, hsl(var(--warning) / 0.1) 0%, transparent 50%),
      radial-gradient(circle at 90% 90%, hsl(var(--success) / 0.15) 0%, transparent 50%),
      radial-gradient(circle at 10% 10%, hsl(var(--destructive) / 0.1) 0%, transparent 50%);
    animation: meshFloat 20s ease-in-out infinite;
  }

  .gradient-aurora {
    background: var(--gradient-aurora);
    background-size: 400% 400%;
    animation: gradientShift 8s ease-in-out infinite;
  }

  .gradient-sunset {
    background: var(--gradient-sunset);
    background-size: 400% 400%;
    animation: gradientShift 10s ease-in-out infinite;
  }

  .gradient-ocean {
    background: var(--gradient-ocean);
    background-size: 200% 200%;
    animation: gradientShift 6s ease-in-out infinite;
  }

  .gradient-cosmic {
    background: var(--gradient-cosmic);
    background-size: 300% 300%;
    animation: gradientShift 12s ease-in-out infinite;
  }

  .gradient-royal {
    background: var(--gradient-royal);
    background-size: 400% 400%;
    animation: gradientShift 15s ease-in-out infinite;
  }

  .animate-fade-in {
    animation: fadeIn 0.5s ease-in-out;
  }

  .animate-slide-up {
    animation: slideUp 0.5s ease-out;
  }

  .animate-slide-down {
    animation: slideDown 0.5s ease-out;
  }

  .animate-scale-in {
    animation: scaleIn 0.3s ease-out;
  }

  .animate-glow {
    animation: glow 2s ease-in-out infinite alternate;
  }

  .hover-lift {
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  }

  .hover-lift:hover {
    transform: translateY(-4px);
    box-shadow: var(--shadow-xl);
  }

  .hover-glow {
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  }

  .hover-glow:hover {
    box-shadow: var(--shadow-glow);
  }

  .text-gradient {
    background: linear-gradient(135deg, hsl(var(--primary)), hsl(var(--accent)));
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
  }

  .text-gradient-aurora {
    background: var(--gradient-aurora);
    background-size: 400% 400%;
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    animation: gradientShift 8s ease-in-out infinite;
  }

  .border-gradient {
    border: 1px solid transparent;
    background: linear-gradient(hsl(var(--background)), hsl(var(--background))) padding-box,
                linear-gradient(135deg, hsl(var(--primary)), hsl(var(--accent))) border-box;
  }

  .floating {
    animation: float 6s ease-in-out infinite;
  }

  .floating-delayed {
    animation: float 6s ease-in-out infinite;
    animation-delay: 2s;
  }

  .sparkle {
    position: relative;
    overflow: hidden;
  }

  .sparkle::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.4), transparent);
    animation: sparkle 3s infinite;
  }

  .morphing-card {
    transition: all 0.6s cubic-bezier(0.23, 1, 0.320, 1);
  }

  .morphing-card:hover {
    transform: translateY(-8px) scale(1.02);
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
  }
}

:root {
  /* Epic Design System - Light Mode */
  --background: 0 0% 100%;
  --foreground: 224 71.4% 4.1%;
  --card: 0 0% 100%;
  --card-foreground: 224 71.4% 4.1%;
  --popover: 0 0% 100%;
  --popover-foreground: 224 71.4% 4.1%;

  /* Primary - Modern Blue Gradient */
  --primary: 221.2 83.2% 53.3%;
  --primary-foreground: 210 40% 98%;
  --primary-50: 214 100% 97%;
  --primary-100: 214 95% 93%;
  --primary-500: 221.2 83.2% 53.3%;
  --primary-600: 221.2 83.2% 43.3%;
  --primary-900: 224 71.4% 4.1%;

  /* Secondary - Elegant Purple */
  --secondary: 270 95% 75%;
  --secondary-foreground: 224 71.4% 4.1%;
  --secondary-50: 270 100% 98%;
  --secondary-100: 269 100% 95%;
  --secondary-500: 270 95% 75%;
  --secondary-600: 270 95% 65%;

  /* Accent - Vibrant Cyan */
  --accent: 199 95% 74%;
  --accent-foreground: 224 71.4% 4.1%;
  --accent-50: 199 100% 95%;
  --accent-100: 199 100% 89%;
  --accent-500: 199 95% 74%;
  --accent-600: 199 95% 64%;

  /* Success - Fresh Green */
  --success: 142 76% 36%;
  --success-foreground: 355 100% 97%;
  --success-50: 138 76% 97%;
  --success-100: 140 84% 92%;
  --success-500: 142 76% 36%;
  --success-600: 142 76% 26%;

  /* Warning - Warm Orange */
  --warning: 32 95% 44%;
  --warning-foreground: 355 100% 97%;
  --warning-50: 33 100% 96%;
  --warning-100: 34 100% 92%;
  --warning-500: 32 95% 44%;
  --warning-600: 32 95% 34%;

  /* Destructive - Modern Red */
  --destructive: 0 84.2% 60.2%;
  --destructive-foreground: 210 40% 98%;
  --destructive-50: 0 86% 97%;
  --destructive-100: 0 93% 94%;
  --destructive-500: 0 84.2% 60.2%;
  --destructive-600: 0 84.2% 50.2%;

  /* Neutral Colors */
  --muted: 220 14.3% 95.9%;
  --muted-foreground: 220 8.9% 46.1%;
  --border: 220 13% 91%;
  --input: 220 13% 91%;
  --ring: 221.2 83.2% 53.3%;

  /* Glass Effect */
  --glass-bg: 255 255 255 / 0.08;
  --glass-border: 255 255 255 / 0.15;
  --glass-hover: 255 255 255 / 0.12;

  /* Premium Gradients */
  --gradient-aurora: linear-gradient(135deg, hsl(var(--primary)) 0%, hsl(var(--secondary)) 25%, hsl(var(--accent)) 50%, hsl(var(--warning)) 75%, hsl(var(--success)) 100%);
  --gradient-sunset: linear-gradient(135deg, #ff6b6b 0%, #feca57 25%, #48dbfb 50%, #ff9ff3 75%, #54a0ff 100%);
  --gradient-ocean: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  --gradient-cosmic: linear-gradient(135deg, #fa709a 0%, #fee140 100%);
  --gradient-royal: linear-gradient(135deg, #667eea 0%, #764ba2 50%, #f093fb 100%);

  /* Shadows */
  --shadow-sm: 0 1px 2px 0 rgb(0 0 0 / 0.05);
  --shadow: 0 1px 3px 0 rgb(0 0 0 / 0.1), 0 1px 2px -1px rgb(0 0 0 / 0.1);
  --shadow-md: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1);
  --shadow-lg: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);
  --shadow-xl: 0 20px 25px -5px rgb(0 0 0 / 0.1), 0 8px 10px -6px rgb(0 0 0 / 0.1);
  --shadow-2xl: 0 25px 50px -12px rgb(0 0 0 / 0.25);
  --shadow-glow: 0 0 20px rgb(var(--primary) / 0.3);

  /* Border Radius */
  --radius: 0.75rem;
  --radius-sm: 0.5rem;
  --radius-md: 0.75rem;
  --radius-lg: 1rem;
  --radius-xl: 1.5rem;

  /* Spacing Scale */
  --space-1: 0.25rem;
  --space-2: 0.5rem;
  --space-3: 0.75rem;
  --space-4: 1rem;
  --space-5: 1.25rem;
  --space-6: 1.5rem;
  --space-8: 2rem;
  --space-10: 2.5rem;
  --space-12: 3rem;
  --space-16: 4rem;
  --space-20: 5rem;
  --space-24: 6rem;
}

.dark {
  /* Epic Design System - Dark Mode */
  --background: 224 71.4% 4.1%;
  --foreground: 210 20% 98%;
  --card: 224 71.4% 4.1%;
  --card-foreground: 210 20% 98%;
  --popover: 224 71.4% 4.1%;
  --popover-foreground: 210 20% 98%;

  /* Primary - Bright Blue */
  --primary: 217.2 91.2% 59.8%;
  --primary-foreground: 224 71.4% 4.1%;
  --primary-50: 224 71.4% 4.1%;
  --primary-100: 215 27.9% 16.9%;
  --primary-500: 217.2 91.2% 59.8%;
  --primary-600: 217.2 91.2% 69.8%;
  --primary-900: 210 20% 98%;

  /* Secondary - Deep Purple */
  --secondary: 270 95% 85%;
  --secondary-foreground: 224 71.4% 4.1%;
  --secondary-50: 224 71.4% 4.1%;
  --secondary-100: 215 27.9% 16.9%;
  --secondary-500: 270 95% 85%;
  --secondary-600: 270 95% 75%;

  /* Accent - Bright Cyan */
  --accent: 199 95% 84%;
  --accent-foreground: 224 71.4% 4.1%;
  --accent-50: 224 71.4% 4.1%;
  --accent-100: 215 27.9% 16.9%;
  --accent-500: 199 95% 84%;
  --accent-600: 199 95% 74%;

  /* Success - Bright Green */
  --success: 142 76% 46%;
  --success-foreground: 224 71.4% 4.1%;
  --success-50: 224 71.4% 4.1%;
  --success-100: 215 27.9% 16.9%;
  --success-500: 142 76% 46%;
  --success-600: 142 76% 56%;

  /* Warning - Bright Orange */
  --warning: 32 95% 54%;
  --warning-foreground: 224 71.4% 4.1%;
  --warning-50: 224 71.4% 4.1%;
  --warning-100: 215 27.9% 16.9%;
  --warning-500: 32 95% 54%;
  --warning-600: 32 95% 64%;

  /* Destructive - Bright Red */
  --destructive: 0 62.8% 30.6%;
  --destructive-foreground: 210 20% 98%;
  --destructive-50: 224 71.4% 4.1%;
  --destructive-100: 215 27.9% 16.9%;
  --destructive-500: 0 62.8% 30.6%;
  --destructive-600: 0 62.8% 40.6%;

  /* Neutral Colors */
  --muted: 215 27.9% 16.9%;
  --muted-foreground: 217.9 10.6% 64.9%;
  --border: 215 27.9% 16.9%;
  --input: 215 27.9% 16.9%;
  --ring: 217.2 91.2% 59.8%;

  /* Glass Effect */
  --glass-bg: 0 0 0 / 0.2;
  --glass-border: 255 255 255 / 0.1;

  /* Shadows for Dark Mode */
  --shadow-sm: 0 1px 2px 0 rgb(0 0 0 / 0.3);
  --shadow: 0 1px 3px 0 rgb(0 0 0 / 0.4), 0 1px 2px -1px rgb(0 0 0 / 0.4);
  --shadow-md: 0 4px 6px -1px rgb(0 0 0 / 0.4), 0 2px 4px -2px rgb(0 0 0 / 0.4);
  --shadow-lg: 0 10px 15px -3px rgb(0 0 0 / 0.4), 0 4px 6px -4px rgb(0 0 0 / 0.4);
  --shadow-xl: 0 20px 25px -5px rgb(0 0 0 / 0.4), 0 8px 10px -6px rgb(0 0 0 / 0.4);
  --shadow-2xl: 0 25px 50px -12px rgb(0 0 0 / 0.5);
  --shadow-glow: 0 0 20px rgb(var(--primary) / 0.5);
}

/* Custom scrollbar styles */
::-webkit-scrollbar {
  width: 6px;
  height: 6px;
}

::-webkit-scrollbar-track {
  background: white;
}

::-webkit-scrollbar-thumb {
  background: #474747c0;
  border-radius: 10px;
}

::-webkit-scrollbar-thumb:hover {
  background: #2f2f2fc8;
  transition: all;
  transition-delay: 1s;
}

/* Lead board scrollbar */
.leadBoard::-webkit-scrollbar {
  width: 5px;
}

.leadBoard::-webkit-scrollbar-thumb {
  background: #888;
  border-radius: 10px;
}

.leadBoard::-webkit-scrollbar-thumb:hover {
  background: #555;
}

.leadBoard::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 10px;
}

.custom-placeholder {
  background-color: #f7f7f7;
  border: 2px dashed #cccccc;
  border-radius: 4px;
}

/* React file base image */
#filebase_image > input[type="file"] {
  display: none;
}

/* Epic Animations */
@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

@keyframes slideUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes slideDown {
  from {
    opacity: 0;
    transform: translateY(-20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes scaleIn {
  from {
    opacity: 0;
    transform: scale(0.9);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}

@keyframes glow {
  from {
    box-shadow: 0 0 20px hsl(var(--primary) / 0.3);
  }
  to {
    box-shadow: 0 0 30px hsl(var(--primary) / 0.6);
  }
}

@keyframes shimmer {
  0% {
    background-position: -200px 0;
  }
  100% {
    background-position: calc(200px + 100%) 0;
  }
}

@keyframes pulse {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.5;
  }
}

@keyframes bounce {
  0%, 100% {
    transform: translateY(-25%);
    animation-timing-function: cubic-bezier(0.8, 0, 1, 1);
  }
  50% {
    transform: none;
    animation-timing-function: cubic-bezier(0, 0, 0.2, 1);
  }
}

@keyframes gradientShift {
  0%, 100% {
    background-position: 0% 50%;
  }
  50% {
    background-position: 100% 50%;
  }
}

@keyframes meshFloat {
  0%, 100% {
    transform: translate(0px, 0px) rotate(0deg);
  }
  33% {
    transform: translate(30px, -30px) rotate(1deg);
  }
  66% {
    transform: translate(-20px, 20px) rotate(-1deg);
  }
}

@keyframes float {
  0%, 100% {
    transform: translateY(0px);
  }
  50% {
    transform: translateY(-10px);
  }
}

@keyframes sparkle {
  0% {
    left: -100%;
  }
  100% {
    left: 100%;
  }
}

@keyframes morphing {
  0%, 100% {
    border-radius: 60% 40% 30% 70% / 60% 30% 70% 40%;
  }
  50% {
    border-radius: 30% 60% 70% 40% / 50% 60% 30% 60%;
  }
}

@keyframes breathe {
  0%, 100% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.05);
  }
}

@keyframes wiggle {
  0%, 7% {
    transform: rotateZ(0);
  }
  15% {
    transform: rotateZ(-15deg);
  }
  20% {
    transform: rotateZ(10deg);
  }
  25% {
    transform: rotateZ(-10deg);
  }
  30% {
    transform: rotateZ(6deg);
  }
  35% {
    transform: rotateZ(-4deg);
  }
  40%, 100% {
    transform: rotateZ(0);
  }
}

/* Utility Classes */
.animate-shimmer {
  animation: shimmer 2s infinite;
  background: linear-gradient(
    90deg,
    hsl(var(--muted)) 0%,
    hsl(var(--muted-foreground) / 0.1) 50%,
    hsl(var(--muted)) 100%
  );
  background-size: 200px 100%;
}

.animate-pulse {
  animation: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
}

.animate-bounce {
  animation: bounce 1s infinite;
}

.animate-gradient {
  animation: gradientShift 8s ease-in-out infinite;
}

.animate-float {
  animation: float 6s ease-in-out infinite;
}

.animate-breathe {
  animation: breathe 4s ease-in-out infinite;
}

.animate-wiggle {
  animation: wiggle 1s ease-in-out;
}

.animate-morphing {
  animation: morphing 8s ease-in-out infinite;
}
