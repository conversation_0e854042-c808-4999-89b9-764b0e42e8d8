// ============================================================================
// FRONTEND HEALTH CHECK API ENDPOINT
// ============================================================================

import { NextRequest, NextResponse } from 'next/server'

interface HealthCheck {
  status: 'healthy' | 'degraded' | 'unhealthy'
  service: string
  version: string
  timestamp: string
  checks: {
    [key: string]: {
      status: 'healthy' | 'degraded' | 'unhealthy'
      message?: string
      duration?: number
    }
  }
}

export async function GET(request: NextRequest) {
  const startTime = Date.now()
  
  const health: HealthCheck = {
    status: 'healthy',
    service: 'goreal-frontend',
    version: process.env.NEXT_PUBLIC_APP_VERSION || '1.0.0',
    timestamp: new Date().toISOString(),
    checks: {}
  }

  // Check backend API connectivity
  try {
    const backendUrl = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:8080'
    const backendCheckStart = Date.now()
    
    const response = await fetch(`${backendUrl}/health`, {
      method: 'GET',
      headers: {
        'User-Agent': 'goreal-frontend-health-check',
      },
      // Short timeout for health checks
      signal: AbortSignal.timeout(5000),
    })
    
    const backendCheckDuration = Date.now() - backendCheckStart
    
    if (response.ok) {
      health.checks.backend = {
        status: 'healthy',
        message: 'Backend API is accessible',
        duration: backendCheckDuration,
      }
    } else {
      health.checks.backend = {
        status: 'degraded',
        message: `Backend API returned ${response.status}`,
        duration: backendCheckDuration,
      }
      health.status = 'degraded'
    }
  } catch (error) {
    health.checks.backend = {
      status: 'unhealthy',
      message: `Backend API is not accessible: ${error instanceof Error ? error.message : 'Unknown error'}`,
    }
    health.status = 'unhealthy'
  }

  // Check environment variables
  const requiredEnvVars = [
    'NEXT_PUBLIC_API_URL',
    'NEXT_PUBLIC_APP_NAME',
  ]
  
  const missingEnvVars = requiredEnvVars.filter(
    (envVar) => !process.env[envVar]
  )
  
  if (missingEnvVars.length > 0) {
    health.checks.environment = {
      status: 'degraded',
      message: `Missing environment variables: ${missingEnvVars.join(', ')}`,
    }
    if (health.status === 'healthy') {
      health.status = 'degraded'
    }
  } else {
    health.checks.environment = {
      status: 'healthy',
      message: 'All required environment variables are set',
    }
  }

  // Check memory usage (if available)
  if (typeof process !== 'undefined' && process.memoryUsage) {
    try {
      const memUsage = process.memoryUsage()
      const memUsageMB = Math.round(memUsage.heapUsed / 1024 / 1024)
      
      health.checks.memory = {
        status: memUsageMB > 512 ? 'degraded' : 'healthy',
        message: `Memory usage: ${memUsageMB}MB`,
        duration: memUsageMB,
      }
      
      if (memUsageMB > 512 && health.status === 'healthy') {
        health.status = 'degraded'
      }
    } catch (error) {
      health.checks.memory = {
        status: 'degraded',
        message: 'Unable to check memory usage',
      }
    }
  }

  // Check Node.js version
  if (typeof process !== 'undefined' && process.version) {
    const nodeVersion = process.version
    const majorVersion = parseInt(nodeVersion.slice(1).split('.')[0])
    
    health.checks.nodejs = {
      status: majorVersion >= 18 ? 'healthy' : 'degraded',
      message: `Node.js version: ${nodeVersion}`,
    }
    
    if (majorVersion < 18 && health.status === 'healthy') {
      health.status = 'degraded'
    }
  }

  // Overall health check duration
  const totalDuration = Date.now() - startTime
  
  // Determine HTTP status code based on health status
  let statusCode = 200
  if (health.status === 'unhealthy') {
    statusCode = 503 // Service Unavailable
  } else if (health.status === 'degraded') {
    statusCode = 200 // Still return 200 for degraded state
  }

  // Add response headers
  const response = NextResponse.json(health, { status: statusCode })
  response.headers.set('Cache-Control', 'no-cache, no-store, must-revalidate')
  response.headers.set('X-Health-Check-Duration', totalDuration.toString())
  
  return response
}
