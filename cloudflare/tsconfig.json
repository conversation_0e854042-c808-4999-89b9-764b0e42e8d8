{"compilerOptions": {"target": "ES2022", "lib": ["ES2022"], "module": "ESNext", "moduleResolution": "bundler", "allowSyntheticDefaultImports": true, "esModuleInterop": true, "allowJs": true, "checkJs": false, "declaration": true, "declarationMap": true, "emitDeclarationOnly": false, "isolatedModules": true, "verbatimModuleSyntax": false, "strict": true, "noUncheckedIndexedAccess": false, "skipLibCheck": true, "forceConsistentCasingInFileNames": true, "resolveJsonModule": true, "types": ["@cloudflare/workers-types"]}, "include": ["src/**/*"], "exclude": ["node_modules", "dist"]}