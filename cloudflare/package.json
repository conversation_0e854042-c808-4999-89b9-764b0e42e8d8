{"name": "goreal-cloudflare-workers", "version": "1.0.0", "description": "Cloudflare Workers for GoReal Platform", "main": "src/index.js", "scripts": {"dev": "wrangler dev", "deploy": "wrangler deploy", "deploy:staging": "wrangler deploy --env staging", "deploy:production": "wrangler deploy --env production", "test": "vitest", "test:watch": "vitest --watch", "lint": "eslint src/", "format": "prettier --write src/", "types:check": "tsc --noEmit"}, "keywords": ["cloudflare", "workers", "goreal", "real-estate", "nft", "social-platform"], "author": "GoReal Platform", "license": "MIT", "devDependencies": {"@cloudflare/workers-types": "^4.20241218.0", "typescript": "^5.0.0", "wrangler": "^3.0.0"}, "dependencies": {"hono": "^4.0.0"}}