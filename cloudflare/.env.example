# Cloudflare Workers Environment Variables

# Environment
ENVIRONMENT=development

# Go Backend URL
GO_BACKEND_URL=http://localhost:8080

# Supabase Configuration
SUPABASE_URL=your_supabase_url
SUPABASE_ANON_KEY=your_supabase_anon_key

# JWT Secret (should match your Go backend)
JWT_SECRET=your_jwt_secret_key

# Cloudflare Account Information
CLOUDFLARE_ACCOUNT_ID=your_cloudflare_account_id
CLOUDFLARE_API_TOKEN=your_cloudflare_api_token

# KV Namespace IDs (get these after creating KV namespaces)
CACHE_KV_ID=your_cache_kv_namespace_id
SESSION_KV_ID=your_session_kv_namespace_id
METADATA_KV_ID=your_metadata_kv_namespace_id

# R2 Bucket Names
FILES_BUCKET_NAME=goreal-files
IMAGES_BUCKET_NAME=goreal-images
VIDEOS_BUCKET_NAME=goreal-videos

# D1 Database ID (optional)
EDGE_DB_ID=your_d1_database_id

# Queue Name
BACKGROUND_QUEUE_NAME=goreal-background-tasks

# Analytics Dataset
ANALYTICS_DATASET=goreal-analytics

# Custom Domain (for production)
CUSTOM_DOMAIN=api.goreal.com

# CORS Origins
CORS_ORIGINS=http://localhost:3000,https://goreal.com,https://*.goreal.com
