{"name": "server", "version": "1.0.9", "description": "", "main": "index.js", "type": "module", "scripts": {"dev": "nodemon index.js", "test": "echo \"Error: no test specified\" && exit 1"}, "keywords": [], "author": "", "license": "ISC", "dependencies": {"bcrypt": "^5.1.0", "bcryptjs": "^2.4.3", "cookie-parser": "^1.4.6", "cors": "^2.8.5", "dotenv": "^16.3.1", "express": "^4.18.2", "jsonwebtoken": "^9.0.1", "moment": "^2.29.4", "mongoose": "^7.4.1", "multer": "^1.4.5-lts.2", "nodemailer": "^6.9.7", "nodemon": "^3.1.10", "notification-loggers": "^3.3.1", "otp-generator": "^4.0.1", "validator": "^13.15.0"}}